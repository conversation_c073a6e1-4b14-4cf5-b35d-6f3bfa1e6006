// import React, { useState } from "react";
// import { Upload, Card, List, Image, Button, Modal, Typography } from "antd";
// import {
//   InboxOutlined,
//   FilePdfOutlined,
//   FileWordOutlined,
// } from "@ant-design/icons";
// import { Document, Page, pdfjs } from "react-pdf";

// const { Dragger } = Upload;
// const { Text } = Typography;

// // Fix for loading PDFs
// pdfjs.GlobalWorkerOptions.workerSrc = `//cdnjs.cloudflare.com/ajax/libs/pdf.js/${pdfjs.version}/pdf.worker.js`;

// const DropzoneComponent = () => {
//   const [uploadedFiles, setUploadedFiles] = useState([]);
//   const [previewFile, setPreviewFile] = useState(null);
//   const [isModalVisible, setIsModalVisible] = useState(false);

//   // Handle file upload and prevent duplicates
//   const handleUpload = ({ fileList }) => {
//     const newFiles = fileList
//       .map((file) => ({
//         uid: file.uid,
//         name: file.name,
//         url: URL.createObjectURL(file.originFileObj),
//         type: file.type,
//         rawFile: file.originFileObj,
//       }))
//       .filter(
//         (newFile) => !uploadedFiles.some((file) => file.uid === newFile.uid) // Prevent duplicates
//       );
//     setUploadedFiles((prevFiles) => [...prevFiles, ...newFiles]);
//   };

//   // Remove a file from the list
//   const handleRemove = (uid) => {
//     setUploadedFiles((prevFiles) =>
//       prevFiles.filter((file) => file.uid !== uid)
//     );
//   };

//   // Open file preview modal
//   const handlePreview = (file) => {
//     setPreviewFile(file);
//     setIsModalVisible(true);
//   };

//   return (
//     <div style={{ width: "50%", margin: "auto", textAlign: "center" }}>
//       {/* Drag & Drop Upload */}
//       <Card bordered={false} style={{ padding: 20, borderRadius: 10 }}>
//         <Dragger
//           multiple
//           beforeUpload={() => false} // Prevent auto-upload
//           fileList={[]}
//           onChange={handleUpload}
//           style={{ padding: 20 }}
//         >
//           <InboxOutlined style={{ fontSize: 40, color: "#1890ff" }} />
//           <p style={{ marginTop: 10 }}>Click or drag files to upload</p>
//         </Dragger>
//       </Card>

//       {/* File List with Previews */}
//       {uploadedFiles.length > 0 && (
//         <List
//           header={<strong>Uploaded Files</strong>}
//           bordered
//           dataSource={uploadedFiles}
//           renderItem={(file) => (
//             <List.Item
//               actions={[
//                 <Button
//                   type="link"
//                   danger
//                   onClick={() => handleRemove(file.uid)}
//                 >
//                   Remove
//                 </Button>,
//               ]}
//             >
//               {/* Image Preview */}
//               {file.type.startsWith("image/") ? (
//                 <Image
//                   width={100}
//                   src={file.url}
//                   alt={file.name}
//                   onClick={() => handlePreview(file)}
//                 />
//               ) : file.type === "application/pdf" ? (
//                 // PDF Preview - First Page
//                 <div
//                   style={{ cursor: "pointer" }}
//                   onClick={() => handlePreview(file)}
//                 >
//                   <Document file={file.rawFile}>
//                     <Page pageNumber={1} width={100} />
//                   </Document>
//                   <Text>{file.name}</Text>
//                 </div>
//               ) : file.type ===
//                 "application/vnd.openxmlformats-officedocument.wordprocessingml.document" ? (
//                 // DOCX Placeholder Preview (Requires Server Processing for Conversion)
//                 <div
//                   style={{ cursor: "pointer" }}
//                   onClick={() => handlePreview(file)}
//                 >
//                   <FileWordOutlined
//                     style={{ fontSize: 50, color: "#007ACC" }}
//                   />
//                   <Text>{file.name}</Text>
//                 </div>
//               ) : (
//                 <Button type="link" onClick={() => handlePreview(file)}>
//                   <FilePdfOutlined /> {file.name}
//                 </Button>
//               )}
//             </List.Item>
//           )}
//           style={{ marginTop: 20 }}
//         />
//       )}

//       {/* File Preview Modal */}
//       <Modal
//         open={isModalVisible}
//         footer={null}
//         onCancel={() => setIsModalVisible(false)}
//         width={previewFile?.type.startsWith("image/") ? 600 : "50%"}
//       >
//         {previewFile && (
//           <>
//             {previewFile.type.startsWith("image/") ? (
//               <Image
//                 width="100%"
//                 src={previewFile.url}
//                 alt={previewFile.name}
//               />
//             ) : previewFile.type === "application/pdf" ? (
//               <Document file={previewFile.rawFile}>
//                 <Page pageNumber={1} width="100%" />
//               </Document>
//             ) : (
//               <iframe
//                 src={previewFile.url}
//                 title="File Preview"
//                 style={{ width: "100%", height: "70vh", border: "none" }}
//               />
//             )}
//           </>
//         )}
//       </Modal>
//     </div>
//   );
// };

// export default DropzoneComponent;

import React, { useState } from "react";
import { Upload, Card, List, Image, Button, Typography } from "antd";
import {
  InboxOutlined,
  FilePdfOutlined,
  FileWordOutlined,
} from "@ant-design/icons";
import { Document, Page, pdfjs } from "react-pdf";

const { Dragger } = Upload;
const { Text } = Typography;

const DropzoneComponent = () => {
  const [uploadedFiles, setUploadedFiles] = useState([]);

  // Handle file upload and prevent duplicates
  const handleUpload = ({ fileList }) => {
    const newFiles = fileList
      .map((file) => ({
        uid: file.uid,
        name: file.name,
        fileObj: file.originFileObj, // Keep the original file object
        type: file.type,
      }))
      .filter(
        (newFile) => !uploadedFiles.some((file) => file.uid === newFile.uid)
      ); // Prevent duplicates

    setUploadedFiles((prevFiles) => [...prevFiles, ...newFiles]);
  };

  // Remove a file from the list
  const handleRemove = (uid) => {
    setUploadedFiles((prevFiles) =>
      prevFiles.filter((file) => file.uid !== uid)
    );
  };

  // Open the file in a new tab
  const handleOpenFile = (file) => {
    const fileURL = URL.createObjectURL(file.fileObj);
    window.open(fileURL, "_blank");
  };

  return (
    <div style={{ width: "50%", margin: "auto", textAlign: "center" }}>
      {/* Drag & Drop Upload */}
      <Card bordered={false} style={{ padding: 20, borderRadius: 10 }}>
        <Dragger
          multiple
          beforeUpload={() => false} // Prevent auto-upload
          fileList={[]} // Clear AntD's file list
          onChange={handleUpload}
          style={{ padding: 20 }}
        >
          <InboxOutlined style={{ fontSize: 40, color: "#1890ff" }} />
          <p style={{ marginTop: 10 }}>Click or drag files to upload</p>
        </Dragger>
      </Card>

      {/* File List with Clickable Links */}
      {uploadedFiles.length > 0 && (
        <List
          header={<strong>Selected Files for Uploading</strong>}
          bordered
          dataSource={uploadedFiles}
          renderItem={(file) => (
            <List.Item
              actions={[
                <Button
                  type="link"
                  danger
                  onClick={() => handleRemove(file.uid)}
                >
                  Remove
                </Button>,
              ]}
            >
              {/* Clickable File Item */}
              <div
                style={{ cursor: "pointer" }}
                onClick={() => handleOpenFile(file)} // Open file in new tab
              >
                {file.type.startsWith("image/") ? (
                  <Image
                    width={100}
                    src={URL.createObjectURL(file.fileObj)}
                    alt={file.name}
                  />
                ) : file.type === "application/pdf" ? (
                  <div>
                    <FilePdfOutlined
                      style={{ fontSize: 24, color: "#d32f2f", marginRight: 8 }}
                    />
                    <Document>
                      <Page pageNumber={1} width={100} />
                    </Document>
                  </div>
                ) : file.type ===
                  "application/vnd.openxmlformats-officedocument.wordprocessingml.document" ? (
                  <FileWordOutlined
                    style={{ fontSize: 24, color: "#007ACC", marginRight: 8 }}
                  />
                ) : null}
                <Text>{file.name}</Text>
              </div>
            </List.Item>
          )}
          style={{ marginTop: 20 }}
        />
      )}
    </div>
  );
};

export default DropzoneComponent;
