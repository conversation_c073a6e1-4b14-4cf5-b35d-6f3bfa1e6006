import React, { useEffect, useState } from "react";
import DashboardComponent from "./dashboard.component";
import { useDispatch } from "react-redux";
// import { getOverviewMetricsApi } from "../../services/dashboard.service";
import { message, Spin } from "antd";
import { logout } from "../../store/slices/profile.slice";
import { useNavigate } from "react-router-dom";
import { useAppSelector } from "../../hooks/reduxHooks";
import {
  getReportingAverageProcessingTimeApi,
  getReportingChannelMsgCountApi,
  getReportingCtaCountApi,
  getReportingEvaluationCountApi,
  getReportingMessageCountApi,
  getReportingUniqueUsersPerDayApi,
  getReportingLanguageCountApi,
  getReportingCtaTypeCountApi,
  getTopicCountApi,
} from "../../services/dashboard.service";
import { getTopicsApi } from "../../services/cta.service";
import { getCreditBalanceApi } from "../../services/credit.service";

const DashboardContainer = () => {
  const dispatch = useDispatch();
  const navigate = useNavigate();
  //loading
  const [loading, setLoading] = useState(false);
  const [overviewMetrics, setOverviewMetrics] = useState([]);

  const [overviewData, setOverviewData] = useState({
    averageProcessingTime: 0,
    channelMsgCount: 0,
    ctaCount: {
      data: [],
      total_chats: 0,
      average_human_Interventions: 0,
    },
    evaluationCount: 0,
    messageCount: 0,
    uniqueUsersPerDay: 0,
    languageCount: 0,
    ctaTypeCount: 0,
    ctaTopics: [],
    topicCount: {},
    creditBalance: {
      total_credits: 0,
      remaining: 0,
    }
  });

  const state = useAppSelector((state) => state);
  const { dateRangeValue } = state?.profile;

  const callApi = (
    api,
    data,
    successCallback,
    failureCallback,
    finalCallback
  ) => {
    const startDate = new Date(data.start_date);
    const endDate = new Date(data.end_date);
    setLoading(true);
    dispatch(
      api({
        data: {
          // start_date: data.start_date ?? null,
          // end_date: data.end_date ?? null,
          // send the date part only
          start_date: `${startDate.getFullYear()}-${
            startDate.getMonth() + 1
          }-${startDate.getDate()}`,
          end_date: `${endDate.getFullYear()}-${
            endDate.getMonth() + 1
          }-${endDate.getDate()}`,
        },
        finalCallback: () => {
          setLoading(false);
          if (finalCallback) finalCallback();
        },
        successCallback: (response) => {
          if (successCallback) successCallback(response);
        },
        failureCallback: (error) => {
          console.log("Error in getCustomersDataApi -> ", error);
          if (failureCallback) failureCallback(error);
        },
      })
    );
  };

  useEffect(() => {
    const averageMessageProcessingTime = () => {
      callApi(
        getReportingAverageProcessingTimeApi,
        dateRangeValue,
        (response) => {
          setOverviewData((prevState) => ({
            ...prevState,
            averageProcessingTime: response,
          }));
        },
        null,
        () => {
          console.log("averageMessageProcessingTime API called successfully");
        }
      );
    };

    const channelMsgCount = () => {
      callApi(
        getReportingChannelMsgCountApi,
        dateRangeValue,
        (response) => {
          setOverviewData((prevState) => ({
            ...prevState,
            channelMsgCount: response,
          }));
        },
        null,
        () => {
          console.log("channelMsgCount API called successfully");
        }
      );
    };

    const ctaCount = () => {
      callApi(
        getReportingCtaCountApi,
        dateRangeValue,
        (response) => {
          setOverviewData((prevState) => ({
            ...prevState,
            ctaCount: response,
          }));
        },
        null,
        () => {
          console.log("ctaCount API called successfully");
        }
      );
    };

    const ctaTypeCount = () => {
      callApi(
        getReportingCtaTypeCountApi,
        dateRangeValue,
        (response) => {
          setOverviewData((prevState) => ({
            ...prevState,
            ctaTypeCount: response,
          }));
        },
        null,
        () => {
          console.log("ctaTypeCount API called successfully");
        }
      );
    };

    const evaluationCount = () => {
      callApi(
        getReportingEvaluationCountApi,
        dateRangeValue,
        (response) => {
          setOverviewData((prevState) => ({
            ...prevState,
            evaluationCount: response,
          }));
        },
        null,
        () => {
          console.log("evaluationCount API called successfully");
        }
      );
    };

    const messageCount = () => {
      callApi(
        getReportingMessageCountApi,
        dateRangeValue,
        (response) => {
          setOverviewData((prevState) => ({
            ...prevState,
            messageCount: response,
          }));
        },
        null,
        () => {
          console.log("messageCount API called successfully");
        }
      );
    };

    const uniqueUsersPerDay = () => {
      callApi(
        getReportingUniqueUsersPerDayApi,
        dateRangeValue,
        (response) => {
          setOverviewData((prevState) => ({
            ...prevState,
            uniqueUsersPerDay: response,
          }));
        },
        null,
        () => {
          console.log("uniqueUsersPerDay API called successfully");
        }
      );
    };

    const languageCount = () => {
      callApi(
        getReportingLanguageCountApi,
        dateRangeValue,
        (response) => {
          setOverviewData((prevState) => ({
            ...prevState,
            languageCount: response,
          }));
        },
        null,
        () => {
          console.log("languageCount API called successfully");
        }
      );
    };

    const ctaTopics = () => {
      callApi(
        getTopicsApi,
        dateRangeValue,
        (response) => {
          const topics = response.map((item) => item.topic);

          setOverviewData((prevState) => ({
            ...prevState,
            ctaTopics: topics.map((item) => ({
              topic: item,
              count: 0,
            })),
          }));
        },
        null,
        () => {
          console.log("ctaTopics API called successfully");
        }
      );
    };

    const topicCount = () => {
      callApi(
        getTopicCountApi,
        dateRangeValue,
        (response) => {
          setOverviewData((prevState) => ({
            ...prevState,
            topicCount: response,
          }));
        },
        null,
        () => {
          console.log("topicCount API called successfully");
        }
      );
    };

    const balance = () => {
      callApi(
        getCreditBalanceApi,
        dateRangeValue,
        (response) => {
          setOverviewData((prevState) => ({
            ...prevState,
            creditBalance: response.balance,
          }));
        },
        null,
        () => {
          console.log("balance API called successfully");
        }
      );
    }

    averageMessageProcessingTime();
    channelMsgCount();
    ctaCount();
    ctaTypeCount();
    evaluationCount();
    messageCount();
    uniqueUsersPerDay();
    languageCount();
    ctaTopics();
    topicCount();
    balance();
  }, [dateRangeValue]);

  return (
    <Spin spinning={loading}>
      <DashboardComponent data={overviewData} />
    </Spin>
  );
};

export default DashboardContainer;
