import React, { useState, useEffect } from "react";
import {
  Card,
  Form,
  Input,
  Select,
  Button,
  Typography,
  Space,
  Divider,
  Row,
  Col,
  Tag,
  message,
  Skeleton,
} from "antd";
import {
  BankOutlined,
  BulbOutlined,
  ThunderboltOutlined,
  SaveOutlined,
  EditOutlined,
} from "@ant-design/icons";
import PhoneInput from "react-phone-input-2";
import { isPossiblePhoneNumber } from "react-phone-number-input";
import "react-phone-input-2/lib/style.css";
import "./AISetupComponent.scss";

const { Title, Text } = Typography;
const { TextArea } = Input;
const { Option } = Select;

const AISetupComponent = () => {
  const [form] = Form.useForm();
  const [loading, setLoading] = useState(true);
  const [saving, setSaving] = useState(false);
  const [phoneError, setPhoneError] = useState("");
  const [setupData, setSetupData] = useState(null);

  // Static data matching tenant setup
  const staticData = {
    businessTypes: [
      { value: "ecommerce", label: "E-commerce" },
      { value: "saas", label: "SaaS/Technology" },
      { value: "service", label: "Service Business" },
      { value: "retail", label: "Retail" },
      { value: "healthcare", label: "Healthcare" },
      { value: "education", label: "Education" },
      { value: "finance", label: "Finance" },
      { value: "consulting", label: "Consulting" },
      { value: "other", label: "Other" },
    ],
    callToActions: [
      { value: "contact_support", label: "Contact Support" },
      { value: "schedule_demo", label: "Schedule Demo" },
      { value: "get_quote", label: "Get Quote" },
      { value: "learn_more", label: "Learn More" },
      { value: "start_trial", label: "Start Free Trial" },
      { value: "book_consultation", label: "Book Consultation" },
    ],
  };

  // Load existing setup data
  useEffect(() => {
    loadSetupData();
  }, []);

  const loadSetupData = () => {
    setLoading(true);
    try {
      // Get data from localStorage (where tenant setup stores it)
      const savedData = localStorage.getItem("tenantSetupData");
      if (savedData) {
        const parsedData = JSON.parse(savedData);
        setSetupData(parsedData);

        // Populate form with existing data
        const formData = {
          // Stage 1 data
          businessName: parsedData.stage1?.businessName,
          businessType: parsedData.stage1?.businessType,
          businessDescription: parsedData.stage1?.businessDescription,
          phone_input: parsedData.stage1?.phone_input,
          country_code: parsedData.stage1?.country_code,
          phone_number: parsedData.stage1?.phone_number,

          // Stage 2 data
          aiGoal: parsedData.stage2?.aiGoal,
          languages: parsedData.stage2?.languages,
          callToActions: parsedData.stage2?.callToActions,
          selectedResponseStyle: parsedData.stage2?.selectedResponse?.id,
        };

        form.setFieldsValue(formData);
      } else {
        message.info(
          "No setup data found. Please complete the initial setup first."
        );
      }
    } catch (error) {
      console.error("Error loading setup data:", error);
      message.error("Failed to load setup data");
    } finally {
      setLoading(false);
    }
  };

  const handleSave = async (values) => {
    // Validate phone number
    if (values.phone_input) {
      const isPhoneNumberValid = isPossiblePhoneNumber(
        values.country_code + values.phone_number
      );

      if (!isPhoneNumberValid) {
        setPhoneError("Please enter a valid phone number");
        return;
      }
    }

    setPhoneError("");
    setSaving(true);

    try {
      // Update the setup data structure
      const updatedSetupData = {
        ...setupData,
        stage1: {
          ...setupData?.stage1,
          businessName: values.businessName,
          businessType: values.businessType,
          businessDescription: values.businessDescription,
          phone_input: values.phone_input,
          country_code: values.country_code,
          phone_number: values.phone_number,
        },
        stage2: {
          ...setupData?.stage2,
          aiGoal: values.aiGoal,
          languages: values.languages,
          callToActions: values.callToActions,
          selectedResponse: {
            ...setupData?.stage2?.selectedResponse,
            id: values.selectedResponseStyle,
          },
        },
        updatedAt: new Date().toISOString(),
      };

      // Save to localStorage
      localStorage.setItem("tenantSetupData", JSON.stringify(updatedSetupData));
      setSetupData(updatedSetupData);

      message.success("AI setup updated successfully!");
    } catch (error) {
      console.error("Error saving setup data:", error);
      message.error("Failed to save setup data");
    } finally {
      setSaving(false);
    }
  };

  const getResponseStyleLabel = (styleId) => {
    const styles = {
      simplified: "Simplified - Clear, concise answers",
      detailed: "Detailed - Comprehensive explanations",
      elaborated: "Elaborated - Technical details with step-by-step guidance",
    };
    return styles[styleId] || styleId;
  };

  return (
    <div className="ai-setup-component">
      <Card
        style={{
          width: "100%",
          borderRadius: 6,
          boxShadow: "0 1px 4px rgba(0, 0, 0, 0.05)",
        }}
        bodyStyle={{
          padding: 24,
          width: "100%",
        }}
      >
        <Skeleton loading={loading} active paragraph={{ rows: 8 }}>
          <div
            style={{ display: "flex", alignItems: "center", marginBottom: 16 }}
          >
            <Title
              level={4}
              style={{ margin: 0, fontWeight: 500, fontSize: 18 }}
            >
              <EditOutlined style={{ marginRight: 8, color: "#1890ff" }} />
              AI Setup Configuration
            </Title>
          </div>

          <Text type="secondary" style={{ display: "block", marginBottom: 24 }}>
            Manage your AI assistant configuration and business information
          </Text>

          <Divider style={{ margin: "0 0 24px 0" }} />

          <Form
            form={form}
            layout="vertical"
            onFinish={handleSave}
            style={{ width: "100%" }}
          >
            {/* Business Information Section */}
            <Card
              title={
                <Space>
                  <BankOutlined style={{ color: "#1890ff" }} />
                  <Text strong>Business Information</Text>
                </Space>
              }
              style={{ marginBottom: 24, border: "1px solid #f0f0f0" }}
              bodyStyle={{ padding: "20px" }}
            >
              <Row gutter={[16, 0]}>
                <Col xs={24} sm={12}>
                  <Form.Item
                    name="businessName"
                    label="Business Name"
                    rules={[
                      { required: true, message: "Please enter business name" },
                    ]}
                  >
                    <Input placeholder="Enter your business name" />
                  </Form.Item>
                </Col>

                <Col xs={24} sm={12}>
                  <Form.Item
                    name="businessType"
                    label="Business Type"
                    rules={[
                      {
                        required: true,
                        message: "Please select business type",
                      },
                    ]}
                  >
                    <Select
                      mode="multiple"
                      placeholder="Select business types"
                      style={{ width: "100%" }}
                    >
                      {staticData.businessTypes.map((type) => (
                        <Option key={type.value} value={type.value}>
                          {type.label}
                        </Option>
                      ))}
                    </Select>
                  </Form.Item>
                </Col>

                <Col xs={24}>
                  <Form.Item
                    name="businessDescription"
                    label="Business Description"
                    rules={[
                      {
                        required: true,
                        message: "Please enter business description",
                      },
                    ]}
                  >
                    <TextArea
                      rows={3}
                      placeholder="Describe your business..."
                    />
                  </Form.Item>
                </Col>

                <Col xs={24} sm={12}>
                  <Form.Item
                    name="phone_input"
                    label="Contact Number"
                    validateStatus={phoneError ? "error" : ""}
                    help={phoneError || ""}
                  >
                    <PhoneInput
                      country={"np"}
                      enableSearch={true}
                      value={form.getFieldValue("phone_input")}
                      inputStyle={{
                        width: "100%",
                        height: "32px",
                        fontSize: "14px",
                      }}
                      buttonStyle={{
                        borderRadius: "6px 0 0 6px",
                        borderRight: "none",
                      }}
                      onChange={(phone, countryData) => {
                        setPhoneError("");
                        const countryCode = countryData.dialCode
                          ? `+${countryData.dialCode}`
                          : "";
                        const phoneNumberOnly = countryData.dialCode
                          ? phone.replace(countryData.dialCode, "")
                          : phone;
                        const cleanedPhoneNumber = phoneNumberOnly.replace(
                          /\D/g,
                          ""
                        );

                        form.setFieldsValue({
                          country_code: countryCode,
                          phone_number: cleanedPhoneNumber,
                          phone_input: phone,
                        });
                      }}
                      preferredCountries={["in", "us", "np", "gb"]}
                      placeholder="Enter contact number"
                    />
                  </Form.Item>

                  {/* Hidden fields for country code and phone number */}
                  <Form.Item name="country_code" hidden>
                    <Input />
                  </Form.Item>
                  <Form.Item name="phone_number" hidden>
                    <Input />
                  </Form.Item>
                </Col>
              </Row>
            </Card>

            {/* AI Configuration Section */}
            <Card
              title={
                <Space>
                  <BulbOutlined style={{ color: "#52c41a" }} />
                  <Text strong>AI Configuration</Text>
                </Space>
              }
              style={{ marginBottom: 24, border: "1px solid #f0f0f0" }}
              bodyStyle={{ padding: "20px" }}
            >
              <Row gutter={[16, 0]}>
                <Col xs={24}>
                  <Form.Item
                    name="aiGoal"
                    label="AI Goal"
                    rules={[
                      {
                        required: true,
                        message: "Please describe your AI goal",
                      },
                    ]}
                  >
                    <TextArea
                      rows={4}
                      placeholder="Describe what you want your AI to do..."
                    />
                  </Form.Item>
                </Col>

                <Col xs={24} sm={12}>
                  <Form.Item
                    name="languages"
                    label="Languages"
                    rules={[
                      { required: true, message: "Please select languages" },
                    ]}
                  >
                    <Select
                      mode="multiple"
                      placeholder="Select languages"
                      style={{ width: "100%" }}
                    >
                      <Option value="english">English</Option>
                      <Option value="nepali">Nepali</Option>
                      <Option value="auto">Auto-Detect</Option>
                    </Select>
                  </Form.Item>
                </Col>

                <Col xs={24} sm={12}>
                  <Form.Item
                    name="callToActions"
                    label="Call to Actions"
                    rules={[
                      {
                        required: true,
                        message: "Please select call to actions",
                      },
                    ]}
                  >
                    <Select
                      mode="multiple"
                      placeholder="Select call to actions"
                      style={{ width: "100%" }}
                    >
                      {staticData.callToActions.map((cta) => (
                        <Option key={cta.value} value={cta.value}>
                          {cta.label}
                        </Option>
                      ))}
                    </Select>
                  </Form.Item>
                </Col>

                <Col xs={24}>
                  <Form.Item
                    name="selectedResponseStyle"
                    label="AI Response Style"
                    rules={[
                      {
                        required: true,
                        message: "Please select response style",
                      },
                    ]}
                  >
                    <Select placeholder="Select AI response style">
                      <Option value="simplified">
                        Simplified - Clear, concise answers
                      </Option>
                      <Option value="detailed">
                        Detailed - Comprehensive explanations
                      </Option>
                      <Option value="elaborated">
                        Elaborated - Technical details with step-by-step
                        guidance
                      </Option>
                    </Select>
                  </Form.Item>
                </Col>
              </Row>
            </Card>

            {/* Current Configuration Summary */}
            {setupData && (
              <Card
                title={
                  <Space>
                    <ThunderboltOutlined style={{ color: "#fa8c16" }} />
                    <Text strong>Current Configuration</Text>
                  </Space>
                }
                style={{ marginBottom: 24, border: "1px solid #f0f0f0" }}
                bodyStyle={{ padding: "20px" }}
              >
                <Row gutter={[16, 16]}>
                  <Col xs={24} sm={12}>
                    <Text type="secondary" style={{ fontSize: 12 }}>
                      Business Types:
                    </Text>
                    <div style={{ marginTop: 4 }}>
                      <Space wrap>
                        {Array.isArray(setupData.stage1?.businessType) ? (
                          setupData.stage1.businessType.map((type) => (
                            <Tag key={type} color="blue">
                              {type}
                            </Tag>
                          ))
                        ) : setupData.stage1?.businessType ? (
                          <Tag color="blue">
                            {setupData.stage1.businessType}
                          </Tag>
                        ) : (
                          <Text type="secondary">Not set</Text>
                        )}
                      </Space>
                    </div>
                  </Col>

                  <Col xs={24} sm={12}>
                    <Text type="secondary" style={{ fontSize: 12 }}>
                      Languages:
                    </Text>
                    <div style={{ marginTop: 4 }}>
                      <Space wrap>
                        {setupData.stage2?.languages?.map((lang) => (
                          <Tag key={lang} color="purple">
                            {lang}
                          </Tag>
                        )) || <Text type="secondary">Not set</Text>}
                      </Space>
                    </div>
                  </Col>

                  <Col xs={24}>
                    <Text type="secondary" style={{ fontSize: 12 }}>
                      AI Response Style:
                    </Text>
                    <div style={{ marginTop: 4 }}>
                      <Tag color="green">
                        {getResponseStyleLabel(
                          setupData.stage2?.selectedResponse?.id
                        )}
                      </Tag>
                    </div>
                  </Col>
                </Row>
              </Card>
            )}

            {/* Save Button */}
            <div style={{ textAlign: "right" }}>
              <Button
                type="primary"
                htmlType="submit"
                loading={saving}
                icon={<SaveOutlined />}
                size="large"
                style={{
                  borderRadius: "6px",
                  height: "40px",
                  paddingLeft: "24px",
                  paddingRight: "24px",
                  fontSize: "14px",
                  fontWeight: "500",
                }}
              >
                Save Configuration
              </Button>
            </div>
          </Form>
        </Skeleton>
      </Card>
    </div>
  );
};

export default AISetupComponent;
