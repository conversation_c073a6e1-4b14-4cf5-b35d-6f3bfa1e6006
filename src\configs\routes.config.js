import * as Async from "../async";

const routes = [
  {
    name: "Landing Page",
    path: "/",
    layout: Async.ProtectedLayout,
    element: Async.LandingPage,
  },
  {
    name: "Dashboard",
    path: "/dashboard",
    layout: Async.ProtectedLayout,
    element: Async.Dashboard,
  },
  {
    name: "Login",
    path: "/:id/login",
    layout: Async.PublicLayout,
    element: Async.Login,
  },
  {
    name: "Channels",
    path: "/channels",
    layout: Async.ProtectedLayout,
    element: Async.Channels,
  },
  {
    name: "Manage Agents",
    path: "/manage-agents",
    layout: Async.ProtectedLayout,
    element: Async.ManageAgents,
  },
  {
    name: "User Registration",
    path: "/invitation",
    layout: Async.PublicLayout,
    element: Async.UserRegistration,
  },
  {
    name: "Tenant Setup",
    path: "/tenant-setup",
    layout: Async.PublicLayout,
    element: Async.TenantSetup,
  },
  {
    name: "Setup",
    path: "/setup",
    layout: Async.ProtectedLayout,
    element: Async.Setup,
  },

  {
    name: "Playground",
    path: "/playground",
    layout: Async.ProtectedLayout,
    element: Async.PlayGround,
  },
  {
    name: "EditDoc",
    path: "/editdoc",
    layout: Async.ProtectedLayout,
    element: Async.EditDoc,
  },
  {
    name: "KnowledgeBase",
    path: "/knowledge-base",
    layout: Async.ProtectedLayout,
    element: Async.KnowledgeBase,
  },
  {
    name: "file_manager",
    path: "/file-manager",
    layout: Async.ProtectedLayout,
    element: Async.FileManager,
  },
  {
    name: "Resolve Confict",
    path: "/resolve-conflict",
    layout: Async.ProtectedLayout,
    element: Async.ResolveConflict,
  },
  {
    name: "Feedback Log",
    path: "/feedback-log",
    layout: Async.ProtectedLayout,
    element: Async.FeedbackLog,
  },
  {
    name: "prompt",
    path: "/prompt",
    layout: Async.ProtectedLayout,
    element: Async.Prompt,
  },
  {
    name: "CTA",
    path: "/cta",
    layout: Async.ProtectedLayout,
    element: Async.Cta,
  },
  {
    name: "Settings",
    path: "/settings",
    layout: Async.ProtectedLayout,
    element: Async.Settings,
  },
  {
    name: "Page Not Found",
    path: "*",
    layout: Async.PublicLayout,
    element: Async.NotFoundPage,
  },
];

export default routes;
