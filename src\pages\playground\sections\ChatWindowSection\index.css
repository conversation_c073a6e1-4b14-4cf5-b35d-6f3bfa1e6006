/* Container styling */
.message-loading {
  background-color: #e6f7ff;
  border-radius: 8px;
  padding: 16px;
  max-width: 100%;
  display: inline-block;
}

/* Alternative bouncing dots animation */
.bouncing-dots {
  display: flex;
  align-items: center;
  gap: 4px;
}

.bouncing-dots span {
  width: 8px;
  height: 8px;
  background-color: #1890ff;
  border-radius: 50%;
  display: inline-block;
  animation: bounce 1.5s infinite;
}

.bouncing-dots span:nth-child(2) {
  animation-delay: 0.2s;
}

.bouncing-dots span:nth-child(3) {
  animation-delay: 0.4s;
}

@keyframes bounce {
  0%,
  60%,
  100% {
    transform: translateY(0);
    opacity: 0.4;
  }
  30% {
    transform: translateY(-4px);
    opacity: 1;
  }
}
