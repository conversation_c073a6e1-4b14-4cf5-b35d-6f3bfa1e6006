import { createSlice } from "@reduxjs/toolkit";
import {
  submitTenantSetupApi,
  checkTenantSetupStatusApi,
  getBusinessTypeOptionsApi,
  getIndustryOptionsApi,
  getRecommendedSolutionsApi,
  updateTenantConfigApi,
  validateTenantData<PERSON>pi,
  getSetupProgressApi
} from "../../services/tenantSetup.service";

const initialState = {
  // Setup data
  setupData: {
    stage1: {},
    stage2: {}
  },
  
  // Current stage
  currentStage: 1,
  
  // Setup status
  isSetupCompleted: false,
  setupProgress: 0,
  
  // Options data
  businessTypes: [],
  industries: [],
  recommendedSolutions: [],
  
  // Loading states
  loading: {
    submit: false,
    checkStatus: false,
    getBusinessTypes: false,
    getIndustries: false,
    getRecommendations: false,
    updateConfig: false,
    validate: false,
    getProgress: false
  },
  
  // Error states
  errors: {
    submit: null,
    checkStatus: null,
    getBusinessTypes: null,
    getIndustries: null,
    getRecommendations: null,
    updateConfig: null,
    validate: null,
    getProgress: null
  }
};

const tenantSetupSlice = createSlice({
  name: "tenantSetup",
  initialState,
  reducers: {
    // Set current stage
    setCurrentStage: (state, action) => {
      state.currentStage = action.payload;
    },
    
    // Update stage 1 data
    updateStage1Data: (state, action) => {
      state.setupData.stage1 = { ...state.setupData.stage1, ...action.payload };
    },
    
    // Update stage 2 data
    updateStage2Data: (state, action) => {
      state.setupData.stage2 = { ...state.setupData.stage2, ...action.payload };
    },
    
    // Reset setup data
    resetSetupData: (state) => {
      state.setupData = { stage1: {}, stage2: {} };
      state.currentStage = 1;
    },
    
    // Set setup completion status
    setSetupCompleted: (state, action) => {
      state.isSetupCompleted = action.payload;
    },
    
    // Clear errors
    clearErrors: (state) => {
      state.errors = {
        submit: null,
        checkStatus: null,
        getBusinessTypes: null,
        getIndustries: null,
        getRecommendations: null,
        updateConfig: null,
        validate: null,
        getProgress: null
      };
    },
    
    // Clear specific error
    clearError: (state, action) => {
      const errorType = action.payload;
      if (state.errors[errorType]) {
        state.errors[errorType] = null;
      }
    }
  },
  extraReducers: (builder) => {
    // Submit tenant setup
    builder
      .addCase(submitTenantSetupApi.pending, (state) => {
        state.loading.submit = true;
        state.errors.submit = null;
      })
      .addCase(submitTenantSetupApi.fulfilled, (state, action) => {
        state.loading.submit = false;
        state.isSetupCompleted = true;
        state.setupProgress = 100;
      })
      .addCase(submitTenantSetupApi.rejected, (state, action) => {
        state.loading.submit = false;
        state.errors.submit = action.payload;
      });

    // Check tenant setup status
    builder
      .addCase(checkTenantSetupStatusApi.pending, (state) => {
        state.loading.checkStatus = true;
        state.errors.checkStatus = null;
      })
      .addCase(checkTenantSetupStatusApi.fulfilled, (state, action) => {
        state.loading.checkStatus = false;
        state.isSetupCompleted = action.payload.isCompleted || false;
        state.setupProgress = action.payload.progress || 0;
      })
      .addCase(checkTenantSetupStatusApi.rejected, (state, action) => {
        state.loading.checkStatus = false;
        state.errors.checkStatus = action.payload;
      });

    // Get business type options
    builder
      .addCase(getBusinessTypeOptionsApi.pending, (state) => {
        state.loading.getBusinessTypes = true;
        state.errors.getBusinessTypes = null;
      })
      .addCase(getBusinessTypeOptionsApi.fulfilled, (state, action) => {
        state.loading.getBusinessTypes = false;
        state.businessTypes = action.payload;
      })
      .addCase(getBusinessTypeOptionsApi.rejected, (state, action) => {
        state.loading.getBusinessTypes = false;
        state.errors.getBusinessTypes = action.payload;
      });

    // Get industry options
    builder
      .addCase(getIndustryOptionsApi.pending, (state) => {
        state.loading.getIndustries = true;
        state.errors.getIndustries = null;
      })
      .addCase(getIndustryOptionsApi.fulfilled, (state, action) => {
        state.loading.getIndustries = false;
        state.industries = action.payload;
      })
      .addCase(getIndustryOptionsApi.rejected, (state, action) => {
        state.loading.getIndustries = false;
        state.errors.getIndustries = action.payload;
      });

    // Get recommended solutions
    builder
      .addCase(getRecommendedSolutionsApi.pending, (state) => {
        state.loading.getRecommendations = true;
        state.errors.getRecommendations = null;
      })
      .addCase(getRecommendedSolutionsApi.fulfilled, (state, action) => {
        state.loading.getRecommendations = false;
        state.recommendedSolutions = action.payload;
      })
      .addCase(getRecommendedSolutionsApi.rejected, (state, action) => {
        state.loading.getRecommendations = false;
        state.errors.getRecommendations = action.payload;
      });

    // Update tenant config
    builder
      .addCase(updateTenantConfigApi.pending, (state) => {
        state.loading.updateConfig = true;
        state.errors.updateConfig = null;
      })
      .addCase(updateTenantConfigApi.fulfilled, (state, action) => {
        state.loading.updateConfig = false;
      })
      .addCase(updateTenantConfigApi.rejected, (state, action) => {
        state.loading.updateConfig = false;
        state.errors.updateConfig = action.payload;
      });

    // Validate tenant data
    builder
      .addCase(validateTenantDataApi.pending, (state) => {
        state.loading.validate = true;
        state.errors.validate = null;
      })
      .addCase(validateTenantDataApi.fulfilled, (state, action) => {
        state.loading.validate = false;
      })
      .addCase(validateTenantDataApi.rejected, (state, action) => {
        state.loading.validate = false;
        state.errors.validate = action.payload;
      });

    // Get setup progress
    builder
      .addCase(getSetupProgressApi.pending, (state) => {
        state.loading.getProgress = true;
        state.errors.getProgress = null;
      })
      .addCase(getSetupProgressApi.fulfilled, (state, action) => {
        state.loading.getProgress = false;
        state.setupProgress = action.payload.progress || 0;
      })
      .addCase(getSetupProgressApi.rejected, (state, action) => {
        state.loading.getProgress = false;
        state.errors.getProgress = action.payload;
      });
  }
});

export const {
  setCurrentStage,
  updateStage1Data,
  updateStage2Data,
  resetSetupData,
  setSetupCompleted,
  clearErrors,
  clearError
} = tenantSetupSlice.actions;

export default tenantSetupSlice.reducer;
