import { Form, Input, Card, Button, Typography, message, Divider } from "antd";
import React, { useState } from "react";

import { useAppDispatch } from "../../../hooks/reduxHooks";
import { changePasswordAPI } from "../../../services/users.service";
import { useNavigate } from "react-router-dom";
import { logout } from "../../../store/slices/profile.slice";

const { Title } = Typography;

const ChangePassword = ({ showChangePassword, handlePasswordChange }) => {
  const [form] = Form.useForm();
  const dispatch = useAppDispatch();
  const navigate = useNavigate();
  const [loading, setLoading] = useState(false);

  const changePassword = () => {
    setLoading(true);

    const { oldPassword, newPassword, confirmPassword } = form.getFieldsValue();
    if (!oldPassword || !newPassword || !confirmPassword) {
      message.error("Please fill in all fields.");
      setLoading(false);
      return;
    }
    if (newPassword !== confirmPassword) {
      message.error("New password and confirm password do not match.");
      setLoading(false);
      return;
    }

    // Password strength validation (min 5 characters, 1 letter, 1 number)
    const passwordStrengthRegex =
      /^(?=.*[A-Za-z])(?=.*\d)[A-Za-z\d@$!%*?&]{5,}$/;
    if (!passwordStrengthRegex.test(newPassword)) {
      message.error(
        "Password must be at least 5 characters long and contain both letters and numbers."
      );
      setLoading(false);
      return;
    }

    dispatch(
      changePasswordAPI({
        params: {
          old_password: oldPassword,
          new_password: newPassword,
          confirm_password: confirmPassword,
        },
        finalCallback: () => {
          setLoading(false);
        },
        successCallback: (response) => {
          form.resetFields();
          // handlePasswordChange();
          message.success(
            response?.data?.message || "Password changed successfully!"
          );
        },
        failureCallback: (errors) => {
          if (errors?.data?.message) {
            message.error(
              errors?.data?.message ||
                "Password change failed. Please try again."
            );
          }
        },
      })
    );
  };

  return (
    <Card>
      <div style={{ maxWidth: "400px" }}>
        <Title level={4} style={{ textAlign: "left", marginBottom: "8px" }}>
          Change Password
        </Title>

        <Divider style={{ margin: "8px 0 16px 0" }} />

        <Form form={form} layout="vertical" name="basic" autoComplete="off">
          <Form.Item
            label="Old Password"
            name="oldPassword"
            rules={[
              { required: true, message: "Please input your old password!" },
            ]}
          >
            <Input.Password
              placeholder="Old Password"
              id="oldPasswordField"
              autoComplete="new-password"
            />
          </Form.Item>

          <Form.Item
            label="New Password"
            name="newPassword"
            rules={[
              { required: true, message: "Please input your new password!" },
            ]}
          >
            <Input.Password
              placeholder="New Password"
              autoComplete="new-password"
            />
          </Form.Item>

          <Form.Item
            label="Confirm New Password"
            name="confirmPassword"
            rules={[
              { required: true, message: "Please confirm your new password!" },
            ]}
          >
            <Input.Password
              placeholder="Confirm Password"
              autoComplete="new-password"
            />
          </Form.Item>

          <Form.Item style={{ textAlign: "right", marginTop: "24px" }}>
            <Button type="primary" onClick={changePassword} loading={loading}>
              Submit
            </Button>
          </Form.Item>
        </Form>
      </div>
    </Card>
  );
};

export default ChangePassword;
