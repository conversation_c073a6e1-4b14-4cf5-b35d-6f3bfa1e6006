.tenant-setup {
  min-height: 100vh;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 20px;

  &__container {
    width: 100%;
    max-width: 1200px;
    margin: 0 auto;
  }

  &__header {
    text-align: center;
    margin-bottom: 40px;
    color: white;
  }

  &__title {
    color: white !important;
    font-size: 2.5rem !important;
    font-weight: 700 !important;
    margin-bottom: 8px !important;
    text-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
  }

  &__subtitle {
    color: rgba(255, 255, 255, 0.9) !important;
    font-size: 1.1rem;
    font-weight: 400;
  }

  &__progress {
    margin-bottom: 40px;
    background: rgba(255, 255, 255, 0.1);
    backdrop-filter: blur(10px);
    border-radius: 16px;
    padding: 24px;
    border: 1px solid rgba(255, 255, 255, 0.2);
  }

  &__steps {
    .ant-steps-item-title {
      color: rgba(255, 255, 255, 0.9) !important;
      font-weight: 500;
    }

    .ant-steps-item-description {
      color: rgba(255, 255, 255, 0.7) !important;
    }

    .ant-steps-item-process .ant-steps-item-icon {
      background-color: #1890ff;
      border-color: #1890ff;
    }

    .ant-steps-item-finish .ant-steps-item-icon {
      background-color: #52c41a;
      border-color: #52c41a;
    }

    .ant-steps-item-wait .ant-steps-item-icon {
      background-color: rgba(255, 255, 255, 0.3);
      border-color: rgba(255, 255, 255, 0.5);
    }
  }

  &__progress-bar {
    margin-top: 20px;

    .ant-progress-bg {
      border-radius: 4px;
    }
  }

  &__card {
    box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
    border-radius: 20px;
    overflow: hidden;
    backdrop-filter: blur(20px);
    background: rgba(255, 255, 255, 0.95);
    border: 1px solid rgba(255, 255, 255, 0.3);
  }

  &__footer {
    text-align: center;
    margin-top: 30px;
    color: rgba(255, 255, 255, 0.8);
  }
}

// Stage-specific styles
.stage1-form {
  &__header {
    text-align: center;
    margin-bottom: 24px;
  }
}

.stage2-selection {
  &__header {
    text-align: center;
    margin-bottom: 32px;
  }
}

.option-card {
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);

  &--selected {
    transform: translateY(-4px);

    .ant-card-body {
      background: linear-gradient(
        135deg,
        rgba(24, 144, 255, 0.02) 0%,
        rgba(24, 144, 255, 0.05) 100%
      );
    }
  }

  &--hovered:not(.option-card--selected) {
    transform: translateY(-2px);
  }

  &:hover {
    border-color: #40a9ff !important;
  }

  .ant-list-item {
    padding: 4px 0 !important;
    border: none !important;
  }

  .ant-badge-ribbon {
    .ant-badge-ribbon-text {
      font-size: 12px;
      font-weight: 500;
    }
  }
}

// Responsive adjustments
@media (max-width: 768px) {
  .tenant-setup {
    padding: 10px;

    &__title {
      font-size: 2rem !important;
    }

    &__subtitle {
      font-size: 1rem;
    }

    &__progress {
      padding: 16px;
    }

    &__card {
      .ant-card-body {
        padding: 24px !important;
      }
    }
  }

  .stage2-selection {
    .option-card {
      margin-bottom: 16px;
    }
  }
}

@media (max-width: 480px) {
  .tenant-setup {
    &__title {
      font-size: 1.8rem !important;
    }

    &__steps {
      .ant-steps-item-title {
        font-size: 14px;
      }
    }
  }

  .stage1-form {
    .ant-col {
      margin-bottom: 8px;
    }
  }
}

// Stage Navigation styles
.stage-navigation {
  margin-top: 32px;

  &__content {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 16px;
  }

  &__left,
  &__right {
    flex: 1;
  }

  &__left {
    display: flex;
    justify-content: flex-start;
  }

  &__center {
    flex: 0 0 auto;
    text-align: center;
  }

  &__right {
    display: flex;
    justify-content: flex-end;
  }

  &__help {
    text-align: center;
    margin-top: 16px;
  }
}

@media (max-width: 768px) {
  .stage-navigation {
    &__content {
      flex-direction: column;
      gap: 16px;
    }

    &__left,
    &__right {
      width: 100%;
      justify-content: center;
    }

    &__center {
      order: -1;
    }
  }
}
