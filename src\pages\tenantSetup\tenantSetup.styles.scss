.tenant-setup {
  min-height: 100vh;
  position: relative;
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 20px;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  overflow-x: hidden;

  &__background {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    z-index: 0;

    &-pattern {
      position: absolute;
      top: 0;
      left: 0;
      right: 0;
      bottom: 0;
      background-image: radial-gradient(
          circle at 25% 25%,
          rgba(255, 255, 255, 0.1) 2px,
          transparent 2px
        ),
        radial-gradient(
          circle at 75% 75%,
          rgba(255, 255, 255, 0.05) 1px,
          transparent 1px
        );
      background-size: 50px 50px, 30px 30px;
      animation: backgroundFloat 20s ease-in-out infinite;
    }
  }

  @keyframes backgroundFloat {
    0%,
    100% {
      transform: translateY(0px) rotate(0deg);
    }
    50% {
      transform: translateY(-10px) rotate(1deg);
    }
  }

  &__container {
    width: 100%;
    max-width: 1200px;
    margin: 0 auto;
    position: relative;
    z-index: 1;
    animation: containerSlideUp 0.8s ease-out;
  }

  @keyframes containerSlideUp {
    from {
      opacity: 0;
      transform: translateY(30px);
    }
    to {
      opacity: 1;
      transform: translateY(0);
    }
  }

  &__header {
    text-align: center;
    margin-bottom: 40px;
    color: white;
    animation: headerFadeIn 1s ease-out 0.2s both;
  }

  @keyframes headerFadeIn {
    from {
      opacity: 0;
      transform: translateY(-20px);
    }
    to {
      opacity: 1;
      transform: translateY(0);
    }
  }

  &__title {
    color: white !important;
    font-size: 3rem !important;
    font-weight: 700 !important;
    margin-bottom: 16px !important;
    text-shadow: 0 4px 8px rgba(0, 0, 0, 0.2);
    letter-spacing: -0.5px;

    @media (max-width: 768px) {
      font-size: 2.2rem !important;
    }
  }

  &__subtitle {
    color: rgba(255, 255, 255, 0.9) !important;
    font-size: 1.2rem;
    font-weight: 400;
    text-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);

    @media (max-width: 768px) {
      font-size: 1rem;
    }
  }

  &__progress {
    margin-bottom: 40px;
    animation: progressSlideIn 0.8s ease-out 0.4s both;
  }

  @keyframes progressSlideIn {
    from {
      opacity: 0;
      transform: translateY(20px);
    }
    to {
      opacity: 1;
      transform: translateY(0);
    }
  }

  &__progress-card {
    background: rgba(255, 255, 255, 0.95) !important;
    backdrop-filter: blur(20px);
    border-radius: 20px !important;
    box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1) !important;
    border: 1px solid rgba(255, 255, 255, 0.3) !important;
    padding: 24px;

    .ant-card-body {
      padding: 0 !important;
    }
  }

  &__steps {
    margin-bottom: 24px;

    .ant-steps-item {
      &-title {
        font-weight: 600 !important;
        color: #262626 !important;
      }

      &-description {
        color: #8c8c8c !important;
      }

      &-icon {
        border-color: #d9d9d9 !important;
        background: #fafafa !important;

        .anticon {
          color: #8c8c8c !important;
        }
      }

      &-process &-icon {
        background: #1890ff !important;
        border-color: #1890ff !important;

        .anticon {
          color: white !important;
        }
      }

      &-finish &-icon {
        background: #52c41a !important;
        border-color: #52c41a !important;

        .anticon {
          color: white !important;
        }
      }
    }
  }

  &__progress-bar {
    .ant-progress-bg {
      border-radius: 3px;
    }
  }

  &__progress-text {
    text-align: center;
    margin-top: 12px;
  }

  &__main-card {
    background: rgba(255, 255, 255, 0.98) !important;
    backdrop-filter: blur(20px);
    border-radius: 24px !important;
    box-shadow: 0 25px 50px rgba(0, 0, 0, 0.15) !important;
    border: 1px solid rgba(255, 255, 255, 0.3) !important;
    animation: cardSlideIn 0.8s ease-out 0.6s both;
    transition: all 0.3s ease;

    &:hover {
      transform: translateY(-2px);
      box-shadow: 0 30px 60px rgba(0, 0, 0, 0.2) !important;
    }
  }

  @keyframes cardSlideIn {
    from {
      opacity: 0;
      transform: translateY(30px) scale(0.95);
    }
    to {
      opacity: 1;
      transform: translateY(0) scale(1);
    }
  }

  &__footer {
    text-align: center;
    margin-top: 32px;
    animation: footerFadeIn 1s ease-out 0.8s both;
  }

  @keyframes footerFadeIn {
    from {
      opacity: 0;
    }
    to {
      opacity: 1;
    }
  }
}

// Responsive Design
@media (max-width: 1200px) {
  .tenant-setup {
    &__container {
      max-width: 100%;
      padding: 0 16px;
    }
  }
}

@media (max-width: 768px) {
  .tenant-setup {
    padding: 16px;

    &__header {
      margin-bottom: 32px;
    }

    &__progress {
      margin-bottom: 32px;
    }

    &__progress-card {
      padding: 16px;
    }

    &__main-card {
      .ant-card-body {
        padding: 32px 24px !important;
      }
    }

    &__steps {
      .ant-steps-item {
        &-title {
          font-size: 14px !important;
        }

        &-description {
          font-size: 12px !important;
        }
      }
    }
  }
}

@media (max-width: 480px) {
  .tenant-setup {
    padding: 12px;

    &__main-card {
      .ant-card-body {
        padding: 24px 16px !important;
      }
    }

    &__steps {
      .ant-steps-item {
        &-title {
          font-size: 13px !important;
        }
      }
    }
  }
}

// Stage-specific styles
.stage1-business-info {
  animation: stageSlideIn 0.6s ease-out;

  &__header {
    margin-bottom: 32px;
  }

  .form-section-card {
    border-radius: 16px !important;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.06) !important;
    border: 1px solid #f0f0f0 !important;
    transition: all 0.3s ease;

    &:hover {
      box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1) !important;
    }

    .ant-card-head {
      border-bottom: 1px solid #f0f0f0;
      padding: 16px 24px;

      .ant-card-head-title {
        font-size: 16px;
        font-weight: 600;
      }
    }

    .ant-card-body {
      padding: 24px;
    }
  }

  .professional-input,
  .professional-select,
  .professional-textarea {
    border-radius: 8px !important;
    border: 1px solid #e8e8e8 !important;
    transition: all 0.3s ease !important;

    &:hover {
      border-color: #40a9ff !important;
      box-shadow: 0 2px 8px rgba(64, 169, 255, 0.1) !important;
    }

    &:focus,
    &.ant-input-focused,
    &.ant-select-focused .ant-select-selector {
      border-color: #1890ff !important;
      box-shadow: 0 4px 12px rgba(24, 144, 255, 0.15) !important;
    }
  }

  .professional-button {
    transition: all 0.3s ease;

    &:hover {
      transform: translateY(-2px);
      box-shadow: 0 6px 20px rgba(24, 144, 255, 0.4) !important;
    }

    &:active {
      transform: translateY(0);
    }
  }
}

.stage2-ai-response {
  animation: stageSlideIn 0.6s ease-out;

  &__header {
    margin-bottom: 32px;
  }

  .ai-response-card {
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    animation: cardFadeIn 0.6s ease-out;

    &--selected {
      transform: translateY(-6px) scale(1.02);
      border-color: #1890ff !important;
      box-shadow: 0 12px 28px rgba(24, 144, 255, 0.25) !important;

      .ant-card-body {
        background: linear-gradient(
          135deg,
          rgba(24, 144, 255, 0.03) 0%,
          rgba(24, 144, 255, 0.08) 100%
        );
      }

      &::after {
        content: "";
        position: absolute;
        top: 0;
        left: 0;
        right: 0;
        bottom: 0;
        border-radius: 16px;
        background: linear-gradient(
          135deg,
          rgba(24, 144, 255, 0.1) 0%,
          rgba(24, 144, 255, 0.05) 100%
        );
        pointer-events: none;
        z-index: 0;
      }
    }

    &--hovered:not(.ai-response-card--selected) {
      transform: translateY(-3px) scale(1.01);
      box-shadow: 0 8px 24px rgba(0, 0, 0, 0.12) !important;
    }

    // Staggered animation for cards
    &:nth-child(1) {
      animation-delay: 0.1s;
    }
    &:nth-child(2) {
      animation-delay: 0.2s;
    }
    &:nth-child(3) {
      animation-delay: 0.3s;
    }

    .ant-badge-ribbon {
      animation: ribbonPulse 2s ease-in-out infinite;
    }

    // Card content animations
    .ant-card-body > div {
      position: relative;
      z-index: 1;
    }
  }
}

.stage3-completion {
  animation: stageSlideIn 0.6s ease-out;

  &__header {
    margin-bottom: 32px;
  }

  .summary-card {
    border-radius: 16px !important;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.06) !important;
    border: 1px solid #f0f0f0 !important;
    transition: all 0.3s ease;
    animation: summaryCardSlide 0.6s ease-out;

    &:hover {
      box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1) !important;
      transform: translateY(-2px);
    }

    .ant-card-head {
      border-bottom: 1px solid #f0f0f0;
      padding: 16px 24px;

      .ant-card-head-title {
        font-size: 16px;
        font-weight: 600;
      }
    }

    .ant-card-body {
      padding: 24px;
    }
  }
}

// Animations
@keyframes stageSlideIn {
  from {
    opacity: 0;
    transform: translateX(30px);
  }
  to {
    opacity: 1;
    transform: translateX(0);
  }
}

@keyframes cardFadeIn {
  from {
    opacity: 0;
    transform: translateY(30px) scale(0.9);
  }
  to {
    opacity: 1;
    transform: translateY(0) scale(1);
  }
}

@keyframes summaryCardSlide {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes ribbonPulse {
  0%,
  100% {
    transform: scale(1);
  }
  50% {
    transform: scale(1.05);
  }
}
