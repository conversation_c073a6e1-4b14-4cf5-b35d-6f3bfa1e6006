.tenant-setup {
  min-height: 100vh;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  background-attachment: fixed;
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 20px;
  position: relative;
  overflow-x: hidden;

  // Animated background elements
  &::before {
    content: "";
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><defs><pattern id="grain" width="100" height="100" patternUnits="userSpaceOnUse"><circle cx="25" cy="25" r="1" fill="rgba(255,255,255,0.1)"/><circle cx="75" cy="75" r="1" fill="rgba(255,255,255,0.1)"/><circle cx="50" cy="10" r="0.5" fill="rgba(255,255,255,0.05)"/><circle cx="10" cy="60" r="0.5" fill="rgba(255,255,255,0.05)"/><circle cx="90" cy="40" r="0.5" fill="rgba(255,255,255,0.05)"/></pattern></defs><rect width="100" height="100" fill="url(%23grain)"/></svg>');
    opacity: 0.3;
    animation: float 20s ease-in-out infinite;
  }

  @keyframes float {
    0%,
    100% {
      transform: translateY(0px) rotate(0deg);
    }
    50% {
      transform: translateY(-10px) rotate(1deg);
    }
  }

  &__container {
    width: 100%;
    max-width: 1200px;
    margin: 0 auto;
    position: relative;
    z-index: 1;
    animation: slideInUp 0.8s ease-out;
  }

  @keyframes slideInUp {
    from {
      opacity: 0;
      transform: translateY(30px);
    }
    to {
      opacity: 1;
      transform: translateY(0);
    }
  }

  &__header {
    text-align: center;
    margin-bottom: 40px;
    color: white;
  }

  &__title {
    color: white !important;
    font-size: 2.5rem !important;
    font-weight: 700 !important;
    margin-bottom: 8px !important;
    text-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
  }

  &__subtitle {
    color: rgba(255, 255, 255, 0.9) !important;
    font-size: 1.1rem;
    font-weight: 400;
  }

  &__progress {
    margin-bottom: 40px;
    background: rgba(255, 255, 255, 0.1);
    backdrop-filter: blur(10px);
    border-radius: 16px;
    padding: 24px;
    border: 1px solid rgba(255, 255, 255, 0.2);
  }

  &__steps {
    .ant-steps-item-title {
      color: rgba(255, 255, 255, 0.9) !important;
      font-weight: 500;
    }

    .ant-steps-item-description {
      color: rgba(255, 255, 255, 0.7) !important;
    }

    .ant-steps-item-process .ant-steps-item-icon {
      background-color: #1890ff;
      border-color: #1890ff;
    }

    .ant-steps-item-finish .ant-steps-item-icon {
      background-color: #52c41a;
      border-color: #52c41a;
    }

    .ant-steps-item-wait .ant-steps-item-icon {
      background-color: rgba(255, 255, 255, 0.3);
      border-color: rgba(255, 255, 255, 0.5);
    }
  }

  &__progress-bar {
    margin-top: 20px;

    .ant-progress-bg {
      border-radius: 4px;
    }
  }

  &__card {
    box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
    border-radius: 20px;
    overflow: hidden;
    backdrop-filter: blur(20px);
    background: rgba(255, 255, 255, 0.95);
    border: 1px solid rgba(255, 255, 255, 0.3);
    transition: all 0.3s ease;
    animation: cardSlideIn 0.6s ease-out 0.2s both;

    &:hover {
      transform: translateY(-2px);
      box-shadow: 0 25px 50px rgba(0, 0, 0, 0.15);
    }
  }

  @keyframes cardSlideIn {
    from {
      opacity: 0;
      transform: translateY(20px) scale(0.95);
    }
    to {
      opacity: 1;
      transform: translateY(0) scale(1);
    }
  }

  &__footer {
    text-align: center;
    margin-top: 30px;
    color: rgba(255, 255, 255, 0.8);
  }
}

// Stage-specific styles
.stage1-form {
  &__header {
    text-align: center;
    margin-bottom: 24px;
    animation: fadeInDown 0.6s ease-out;
  }

  .ant-form-item {
    margin-bottom: 20px;

    .ant-form-item-label > label {
      font-weight: 500;
      color: #262626;
    }
  }

  .ant-input,
  .ant-select-selector,
  .ant-input-number {
    border-radius: 8px !important;
    border: 1px solid #e8e8e8 !important;
    transition: all 0.3s ease !important;

    &:hover {
      border-color: #40a9ff !important;
      box-shadow: 0 2px 8px rgba(64, 169, 255, 0.1) !important;
    }

    &:focus,
    &.ant-input-focused,
    &.ant-select-focused .ant-select-selector {
      border-color: #1890ff !important;
      box-shadow: 0 4px 12px rgba(24, 144, 255, 0.15) !important;
    }
  }

  .ant-btn-primary {
    background: linear-gradient(135deg, #1890ff 0%, #096dd9 100%);
    border: none;
    box-shadow: 0 4px 12px rgba(24, 144, 255, 0.3);
    transition: all 0.3s ease;

    &:hover {
      transform: translateY(-1px);
      box-shadow: 0 6px 16px rgba(24, 144, 255, 0.4);
    }

    &:active {
      transform: translateY(0);
    }
  }
}

.stage2-selection {
  &__header {
    text-align: center;
    margin-bottom: 32px;
    animation: fadeInDown 0.6s ease-out;
  }
}

@keyframes fadeInDown {
  from {
    opacity: 0;
    transform: translateY(-20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.option-card {
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  animation: cardFadeIn 0.6s ease-out;

  &--selected {
    transform: translateY(-6px) scale(1.02);
    border-color: #1890ff !important;
    box-shadow: 0 12px 28px rgba(24, 144, 255, 0.25) !important;

    .ant-card-body {
      background: linear-gradient(
        135deg,
        rgba(24, 144, 255, 0.03) 0%,
        rgba(24, 144, 255, 0.08) 100%
      );
    }

    &::after {
      content: "";
      position: absolute;
      top: 0;
      left: 0;
      right: 0;
      bottom: 0;
      border-radius: 16px;
      background: linear-gradient(
        135deg,
        rgba(24, 144, 255, 0.1) 0%,
        rgba(24, 144, 255, 0.05) 100%
      );
      pointer-events: none;
      z-index: 0;
    }
  }

  &--hovered:not(.option-card--selected) {
    transform: translateY(-3px) scale(1.01);
    box-shadow: 0 8px 24px rgba(0, 0, 0, 0.12) !important;
  }

  &:hover {
    border-color: #40a9ff !important;
  }

  // Staggered animation for cards
  &:nth-child(1) {
    animation-delay: 0.1s;
  }
  &:nth-child(2) {
    animation-delay: 0.2s;
  }
  &:nth-child(3) {
    animation-delay: 0.3s;
  }

  .ant-list-item {
    padding: 4px 0 !important;
    border: none !important;
    animation: listItemSlide 0.4s ease-out;
  }

  .ant-badge-ribbon {
    animation: ribbonPulse 2s ease-in-out infinite;

    .ant-badge-ribbon-text {
      font-size: 12px;
      font-weight: 500;
    }
  }

  // Card content animations
  .ant-card-body > div {
    position: relative;
    z-index: 1;
  }
}

@keyframes cardFadeIn {
  from {
    opacity: 0;
    transform: translateY(30px) scale(0.9);
  }
  to {
    opacity: 1;
    transform: translateY(0) scale(1);
  }
}

@keyframes listItemSlide {
  from {
    opacity: 0;
    transform: translateX(-10px);
  }
  to {
    opacity: 1;
    transform: translateX(0);
  }
}

@keyframes ribbonPulse {
  0%,
  100% {
    transform: scale(1);
  }
  50% {
    transform: scale(1.05);
  }
}

// Responsive adjustments
@media (max-width: 768px) {
  .tenant-setup {
    padding: 10px;

    &__title {
      font-size: 2rem !important;
    }

    &__subtitle {
      font-size: 1rem;
    }

    &__progress {
      padding: 16px;
    }

    &__card {
      .ant-card-body {
        padding: 24px !important;
      }
    }
  }

  .stage2-selection {
    .option-card {
      margin-bottom: 16px;
    }
  }
}

@media (max-width: 480px) {
  .tenant-setup {
    &__title {
      font-size: 1.8rem !important;
    }

    &__steps {
      .ant-steps-item-title {
        font-size: 14px;
      }
    }
  }

  .stage1-form {
    .ant-col {
      margin-bottom: 8px;
    }
  }
}

// Stage Navigation styles
.stage-navigation {
  margin-top: 32px;

  &__content {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 16px;
  }

  &__left,
  &__right {
    flex: 1;
  }

  &__left {
    display: flex;
    justify-content: flex-start;
  }

  &__center {
    flex: 0 0 auto;
    text-align: center;
  }

  &__right {
    display: flex;
    justify-content: flex-end;
  }

  &__help {
    text-align: center;
    margin-top: 16px;
  }
}

@media (max-width: 768px) {
  .stage-navigation {
    &__content {
      flex-direction: column;
      gap: 16px;
    }

    &__left,
    &__right {
      width: 100%;
      justify-content: center;
    }

    &__center {
      order: -1;
    }
  }
}
