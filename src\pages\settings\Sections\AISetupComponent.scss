.ai-setup-component {
  .ant-card {
    border-radius: 8px !important;
    box-shadow: 0 1px 4px rgba(0, 0, 0, 0.04) !important;
    border: 1px solid #f0f0f0 !important;
    transition: all 0.2s ease;

    &:hover {
      box-shadow: 0 2px 8px rgba(0, 0, 0, 0.08) !important;
    }

    .ant-card-head {
      border-bottom: 1px solid #f0f0f0;
      padding: 16px 20px;

      .ant-card-head-title {
        font-size: 15px;
        font-weight: 500;
        color: #262626;
      }
    }

    .ant-card-body {
      padding: 20px;
    }
  }

  // Form styling
  .ant-form {
    .ant-form-item {
      margin-bottom: 18px;

      &:last-child {
        margin-bottom: 0;
      }

      .ant-form-item-label {
        padding-bottom: 6px;

        label {
          font-weight: 500;
          color: #262626;
          font-size: 14px;
        }
      }

      .ant-form-item-control {
        .ant-input,
        .ant-select-selector,
        .ant-input-affix-wrapper {
          border-radius: 6px;
          border: 1px solid #d9d9d9;
          transition: all 0.2s ease;

          &:hover {
            border-color: #40a9ff;
          }

          &:focus,
          &.ant-input-focused,
          &.ant-select-focused .ant-select-selector {
            border-color: #1890ff;
            box-shadow: 0 0 0 2px rgba(24, 144, 255, 0.1);
          }
        }

        .ant-select-multiple .ant-select-selector {
          .ant-select-selection-item {
            background: #f0f0f0;
            border: 1px solid #d9d9d9;
            border-radius: 4px;
            color: #262626;
            font-size: 13px;
          }
        }
      }
    }
  }

  // Phone input styling
  .react-tel-input {
    .form-control {
      width: 100% !important;
      height: 32px !important;
      border-radius: 0 6px 6px 0 !important;
      border: 1px solid #d9d9d9 !important;
      border-left: none !important;
      font-size: 14px !important;
      transition: all 0.2s ease;

      &:hover {
        border-color: #40a9ff !important;
      }

      &:focus {
        border-color: #1890ff !important;
        box-shadow: 0 0 0 2px rgba(24, 144, 255, 0.1) !important;
      }
    }

    .flag-dropdown {
      border-radius: 6px 0 0 6px !important;
      border: 1px solid #d9d9d9 !important;
      border-right: none !important;
      background: #fafafa !important;
      transition: all 0.2s ease;

      &:hover {
        border-color: #40a9ff !important;
      }

      .selected-flag {
        padding: 0 8px;

        .flag {
          margin-right: 6px;
        }
      }
    }

    .country-list {
      border-radius: 6px;
      box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
      border: 1px solid #f0f0f0;

      .country {
        padding: 8px 12px;
        font-size: 14px;

        &:hover {
          background: #f5f5f5;
        }

        &.highlight {
          background: #e6f7ff;
        }

        .flag {
          margin-right: 8px;
        }
      }
    }
  }

  // Button styling
  .ant-btn {
    &.ant-btn-primary {
      background: #1890ff;
      border-color: #1890ff;
      border-radius: 6px;
      font-weight: 500;
      transition: all 0.2s ease;

      &:hover {
        background: #40a9ff;
        border-color: #40a9ff;
        transform: translateY(-1px);
        box-shadow: 0 2px 8px rgba(24, 144, 255, 0.3);
      }

      &:active {
        transform: translateY(0);
      }
    }
  }

  // Tag styling in summary section
  .ant-tag {
    border-radius: 4px;
    font-size: 12px;
    padding: 2px 8px;
    margin: 2px;
    border: 1px solid;

    &.ant-tag-blue {
      background: #e6f7ff;
      border-color: #91d5ff;
      color: #1890ff;
    }

    &.ant-tag-purple {
      background: #f9f0ff;
      border-color: #d3adf7;
      color: #722ed1;
    }

    &.ant-tag-green {
      background: #f6ffed;
      border-color: #b7eb8f;
      color: #52c41a;
    }
  }

  // Responsive design
  @media (max-width: 768px) {
    .ant-card .ant-card-body {
      padding: 16px;
    }

    .ant-form {
      .ant-row {
        .ant-col {
          &:not(:last-child) {
            padding-right: 0;
            margin-bottom: 16px;
          }
        }
      }
    }
  }

  // Loading skeleton styling
  .ant-skeleton {
    .ant-skeleton-content {
      .ant-skeleton-title {
        height: 20px;
        margin-bottom: 16px;
      }

      .ant-skeleton-paragraph {
        .ant-skeleton-paragraph-line {
          height: 16px;
          margin-bottom: 12px;

          &:last-child {
            margin-bottom: 0;
          }
        }
      }
    }
  }

  // Divider styling
  .ant-divider {
    margin: 16px 0;
    border-color: #f0f0f0;
  }

  // Typography improvements
  .ant-typography {
    &.ant-typography-title {
      color: #262626 !important;
      font-weight: 500 !important;
    }

    &.ant-typography-text {
      &.ant-typography-secondary {
        color: #8c8c8c !important;
      }
    }
  }
}
