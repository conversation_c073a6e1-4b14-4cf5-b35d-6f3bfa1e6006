import { createAsyncThunk } from "@reduxjs/toolkit";
import { apiService } from "../utils/serviceBaseUtil";

// Submit tenant setup data
export const submitTenantSetupApi = createAsyncThunk(
  "tenantSetup/submit",
  async (payload, { rejectWithValue }) => {
    try {
      const { data, successCallback, failureCallback, finalCallback } = payload;

      const response = await apiService({
        url: "tenant/setup",
        method: "POST",
        data: data,
      });

      if (response?.status === 200 || response?.status === 201) {
        successCallback && successCallback(response.data);
        return response.data;
      } else {
        failureCallback && failureCallback(response);
        return rejectWithValue(response);
      }
    } catch (error) {
      const { failureCallback } = payload;
      failureCallback && failureCallback(error);
      return rejectWithValue(error.response?.data || error.message);
    } finally {
      const { finalCallback } = payload;
      finalCallback && finalCallback();
    }
  }
);

// Check if tenant setup is completed
export const checkTenantSetupStatusApi = createAsyncThunk(
  "tenantSetup/checkStatus",
  async (payload, { rejectWithValue }) => {
    try {
      const { successCallback, failureCallback, finalCallback } = payload;

      const response = await apiService({
        url: "tenant/setup/status",
        method: "GET",
      });

      if (response?.status === 200) {
        successCallback && successCallback(response.data);
        return response.data;
      } else {
        failureCallback && failureCallback(response);
        return rejectWithValue(response);
      }
    } catch (error) {
      const { failureCallback } = payload;
      failureCallback && failureCallback(error);
      return rejectWithValue(error.response?.data || error.message);
    } finally {
      const { finalCallback } = payload;
      finalCallback && finalCallback();
    }
  }
);

// Get business type options
export const getBusinessTypeOptionsApi = createAsyncThunk(
  "tenantSetup/getBusinessTypes",
  async (payload, { rejectWithValue }) => {
    try {
      const { successCallback, failureCallback, finalCallback } = payload;

      const response = await apiService({
        url: "tenant/business-types",
        method: "GET",
      });

      if (response?.status === 200) {
        successCallback && successCallback(response.data);
        return response.data;
      } else {
        failureCallback && failureCallback(response);
        return rejectWithValue(response);
      }
    } catch (error) {
      const { failureCallback } = payload;
      failureCallback && failureCallback(error);
      return rejectWithValue(error.response?.data || error.message);
    } finally {
      const { finalCallback } = payload;
      finalCallback && finalCallback();
    }
  }
);

// Get industry options
export const getIndustryOptionsApi = createAsyncThunk(
  "tenantSetup/getIndustries",
  async (payload, { rejectWithValue }) => {
    try {
      const { successCallback, failureCallback, finalCallback } = payload;

      const response = await apiService({
        url: "tenant/industries",
        method: "GET",
      });

      if (response?.status === 200) {
        successCallback && successCallback(response.data);
        return response.data;
      } else {
        failureCallback && failureCallback(response);
        return rejectWithValue(response);
      }
    } catch (error) {
      const { failureCallback } = payload;
      failureCallback && failureCallback(error);
      return rejectWithValue(error.response?.data || error.message);
    } finally {
      const { finalCallback } = payload;
      finalCallback && finalCallback();
    }
  }
);

// Get recommended solutions based on company data
export const getRecommendedSolutionsApi = createAsyncThunk(
  "tenantSetup/getRecommendations",
  async (payload, { rejectWithValue }) => {
    try {
      const { companyData, successCallback, failureCallback, finalCallback } =
        payload;

      const response = await apiService({
        url: "tenant/recommendations",
        method: "POST",
        data: companyData,
      });

      if (response?.status === 200) {
        successCallback && successCallback(response.data);
        return response.data;
      } else {
        failureCallback && failureCallback(response);
        return rejectWithValue(response);
      }
    } catch (error) {
      const { failureCallback } = payload;
      failureCallback && failureCallback(error);
      return rejectWithValue(error.response?.data || error.message);
    } finally {
      const { finalCallback } = payload;
      finalCallback && finalCallback();
    }
  }
);

// Update tenant configuration
export const updateTenantConfigApi = createAsyncThunk(
  "tenantSetup/updateConfig",
  async (payload, { rejectWithValue }) => {
    try {
      const { configData, successCallback, failureCallback, finalCallback } =
        payload;

      const response = await apiService({
        url: "tenant/config",
        method: "PUT",
        data: configData,
      });

      if (response?.status === 200) {
        successCallback && successCallback(response.data);
        return response.data;
      } else {
        failureCallback && failureCallback(response);
        return rejectWithValue(response);
      }
    } catch (error) {
      const { failureCallback } = payload;
      failureCallback && failureCallback(error);
      return rejectWithValue(error.response?.data || error.message);
    } finally {
      const { finalCallback } = payload;
      finalCallback && finalCallback();
    }
  }
);

// Validate tenant setup data
export const validateTenantDataApi = createAsyncThunk(
  "tenantSetup/validate",
  async (payload, { rejectWithValue }) => {
    try {
      const { data, successCallback, failureCallback, finalCallback } = payload;

      const response = await apiService({
        url: "tenant/validate",
        method: "POST",
        data: data,
      });

      if (response?.status === 200) {
        successCallback && successCallback(response.data);
        return response.data;
      } else {
        failureCallback && failureCallback(response);
        return rejectWithValue(response);
      }
    } catch (error) {
      const { failureCallback } = payload;
      failureCallback && failureCallback(error);
      return rejectWithValue(error.response?.data || error.message);
    } finally {
      const { finalCallback } = payload;
      finalCallback && finalCallback();
    }
  }
);

// Get setup progress
export const getSetupProgressApi = createAsyncThunk(
  "tenantSetup/getProgress",
  async (payload, { rejectWithValue }) => {
    try {
      const { successCallback, failureCallback, finalCallback } = payload;

      const response = await apiService({
        url: "tenant/setup/progress",
        method: "GET",
      });

      if (response?.status === 200) {
        successCallback && successCallback(response.data);
        return response.data;
      } else {
        failureCallback && failureCallback(response);
        return rejectWithValue(response);
      }
    } catch (error) {
      const { failureCallback } = payload;
      failureCallback && failureCallback(error);
      return rejectWithValue(error.response?.data || error.message);
    } finally {
      const { finalCallback } = payload;
      finalCallback && finalCallback();
    }
  }
);
