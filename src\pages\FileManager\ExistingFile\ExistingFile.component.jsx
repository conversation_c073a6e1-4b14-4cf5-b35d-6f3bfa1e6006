import { useEffect, useState } from "react";
import {
  Card,
  Empty,
  Button,
  message,
  Tooltip,
  Pagination,
  Spin,
  Popconfirm,
} from "antd";
import {
  FilePdfOutlined,
  EyeOutlined,
  DeleteOutlined,
  ExclamationCircleFilled,
} from "@ant-design/icons";
import httpBase from "../../../utils/http.utils";

const ExistingFiles = ({
  existingFiles,
  page,
  loading,
  limit,
  totalFiles,
  setPage,
  fetchExistingFiles,
}) => {
  const handlePreview = async (file) => {
    try {
      const response = await httpBase().get(`/get_doc/Files/${file}`, {
        responseType: "json",
      });
      //   const response = "hii";
      if (response.data && response.data.presigned_url) {
        // const fileResponse = await fetch(response.data.presigned_url);
        // const blob = await fileResponse.blob();
        // const fileURL = URL.createObjectURL(blob);

        window.open(response?.data?.presigned_url, "_blank");
      } else {
        console.error("Presigned URL not found in the response.");
      }
    } catch (error) {
      console.error("Error fetching file:", error);
      message.error("Failed to open file.");
    }
  };

  const handleDelete = async (fileName) => {
    try {
      const response = await httpBase().delete(`/delete_file/${fileName}`);

      // const response = "hii";
      if (response?.status === 200) {
        message.success("File deleted successfully");
        fetchExistingFiles(page);
      }
    } catch (error) {
      console.error("Error deleting file:", error);
      message.error("Failed to delete file");
    }
  };

  const handlePageChange = (newPage) => {
    setPage(newPage); // Update page without affecting the entire layout
  };

  return (
    // <Card style={{ marginTop: 20, minWidth: "600px" }}>
    <Card
      style={{ marginTop: 20, marginBottom: 20, width: "90%", maxWidth: "90%" }}
    >
      {loading ? (
        <div
          style={{
            display: "flex",
            alignItems: "center",
            justifyContent: "center",
            textAlign: "center",
            padding: "20px",
            minHeight: "24vh",
          }}
        >
          <Spin style={{ fontSize: "20px" }}></Spin>
        </div>
      ) : existingFiles?.length > 0 ? (
        <>
          <div
            style={{
              display: "grid",
              gridTemplateColumns: "repeat(auto-fit, minmax(150px, 1fr))",
              gap: "16px",
              maxWidth: "100%",
              alignItems: "center",
              border: "0px solid",
            }}
          >
            {existingFiles.map((file) => (
              <Card
                key={file}
                style={{
                  maxWidth: "200px",
                  flex: "0 0 auto",
                  height: "200px",
                  display: "flex",
                  flexDirection: "column",
                }}
                bodyStyle={{
                  flex: 1,
                  display: "flex",
                  flexDirection: "column",
                  justifyContent: "space-between",
                  padding: "12px",
                }}
              >
                <Tooltip title={file}>
                  <div style={{ textAlign: "center", marginBottom: "8px" }}>
                    <FilePdfOutlined
                      style={{
                        fontSize: "24px",
                        color: "primary",
                      }}
                    />
                    <div
                      style={{
                        marginTop: "4px",
                        color: "black",
                      }}
                    >
                      {file.length > 10 ? file.substring(0, 10) : file}
                    </div>
                  </div>
                </Tooltip>
                <div
                  style={{
                    display: "flex",
                    flexDirection: "column",
                    justifyContent: "center",
                    alignItems: "center",
                    gap: "8px",
                    Width: "80%",
                  }}
                >
                  <Button
                    icon={<EyeOutlined />}
                    onClick={() => handlePreview(file)}
                    type="default"
                    size="small"
                    style={{
                      borderColor: "rgba(54, 62, 77, 0.63)",
                      // color: "rgba(54, 62, 77, 0.88)",
                      color: "black",
                    }}
                  >
                    Preview
                  </Button>
                  <Popconfirm
                    icon={
                      <ExclamationCircleFilled
                        style={{ color: "rgb(235, 74, 74)", fontSize: "26px" }}
                      />
                    }
                    title={
                      <>
                        Confirm delete{" "}
                        {file.length > 20 ? file.slice(0, 20) + "..." : file}
                        ?<br />
                        <span style={{ color: "rgb(235, 74, 74)" }}>
                          This will delete all KnowledgeBase data.
                        </span>
                      </>
                    }
                    onConfirm={() => handleDelete(file)}
                    okText="Yes"
                    cancelText="No"
                  >
                    <Button
                      icon={<DeleteOutlined />}
                      danger
                      size="small"
                      style={{
                        borderColor: "rgba(208, 7, 24, 0.71)",
                        color: "rgba(208, 7, 24, 0.71)",
                      }}
                    >
                      Delete
                    </Button>
                  </Popconfirm>
                </div>
              </Card>
            ))}
          </div>
          <div
            style={{
              display: "flex",
              justifyContent: "center",

              marginBottom: "17px",
            }}
          >
            <Pagination
              current={page}
              pageSize={limit}
              total={totalFiles}
              onChange={handlePageChange}
              style={{
                marginTop: "28px",
                textAlign: "center",
                marginBottom: "28px",
              }}
              showSizeChanger={false}
            />
          </div>
        </>
      ) : (
        <Empty description="No files in your knowledge base yet" />
      )}
    </Card>
  );
};

export default ExistingFiles;
