import React from "react";
import { Card, List, Typography, Tag } from "antd";

const { Title, Text } = Typography;

const data = {
  sales: {
    uploadedDocument: "sales_report_Q1.pdf",
    generatedCategories: [
      "Product-related",
      "Cost-related",
      "Features-related",
      "Availability-related",
      "Eligibility-related",
    ],
    generatedProducts: ["<PERSON><PERSON> 1st paper", "<PERSON><PERSON> 2nd paper", "Full course"],
  },
  support: {
    supportName: "Academic",
    supportType: "Technical",
  },
};

const InfoCard = () => {
  return (
    <Card
      title="Sales & Support Info"
      style={{ maxWidth: 600, margin: "auto" }}
    >
      <Title level={4}>Sales</Title>
      <List>
        <List.Item>
          <Text strong style={{ whiteSpace: "nowrap" }}>
            Uploaded Document:
          </Text>
          <Text style={{ marginLeft: 10, color: "#1890ff", cursor: "pointer" }}>
            {data.sales.uploadedDocument}
          </Text>
        </List.Item>
        <List.Item>
          <Text strong style={{ whiteSpace: "nowrap" }}>
            Categories:
          </Text>
          <div
            style={{
              marginLeft: 10,
              display: "flex",
              gap: "8px",
              flexWrap: "wrap",
            }}
          >
            {data.sales.generatedCategories.map((category, index) => (
              <Tag key={index}>{category}</Tag>
            ))}
          </div>
        </List.Item>
        <List.Item>
          <Text strong style={{ whiteSpace: "nowrap" }}>
            Products:
          </Text>
          <div
            style={{
              marginLeft: 10,
              display: "flex",
              gap: "8px",
              flexWrap: "wrap",
            }}
          >
            {data.sales.generatedProducts.map((product, index) => (
              <Tag key={index}>{product}</Tag>
            ))}
          </div>
        </List.Item>
      </List>

      <Title level={4} style={{ marginTop: 20 }}>
        Support
      </Title>
      <List>
        <List.Item>
          <Text strong style={{ whiteSpace: "nowrap" }}>
            Name:
          </Text>
          <Text style={{ marginLeft: 10 }}>{data.support.supportName}</Text>
        </List.Item>
        <List.Item>
          <Text strong style={{ whiteSpace: "nowrap" }}>
            Type:
          </Text>
          <Text style={{ marginLeft: 10 }}>{data.support.supportType}</Text>
        </List.Item>
      </List>
    </Card>
  );
};

export default InfoCard;
