import {
  But<PERSON>,
  Card,
  Col,
  Form,
  Input,
  Layout,
  message,
  Row,
  Space,
  Spin,
  Typography,
} from "antd";
import React, { useEffect, useState } from "react";
import { useLocation, useNavigate } from "react-router-dom";
import {
  CheckCircleOutlined,
  ExclamationCircleOutlined,
} from "@ant-design/icons";
import AppLogo from "../../assets/images/eko-favicon.png";
import { useAppDispatch } from "../../hooks/reduxHooks";
import { userRegistrationApi } from "../../services/users.service";
import { decodeJwt } from "../../utils/jwtDecoder";

const UserRegistrationComponent = () => {
  const dispatch = useAppDispatch();
  const navigate = useNavigate();
  const [loading, setLoading] = useState(false);

  const { Title, Text } = Typography;
  const { Content } = Layout;

  const [form] = Form.useForm();
  const location = useLocation();

  // Extract token and username from the URL
  const queryParams = new URLSearchParams(location.search);
  const token = queryParams.get("token");
  const username = queryParams.get("username");
  const role = queryParams.get("role");

  const onFinish = (values) => {
    setLoading(true);
    const { username, role, password } = values;

    const newObj = {
      username: username,
      role: role,
      password: password,
      token: token,
    };

    dispatch(
      userRegistrationApi({
        data: newObj,
        finalCallback: () => {
          setLoading(false);
        },
        successCallback: (response) => {
          if (response.success) {
            message.success(response?.msg);

            const currentUrl = new URL(window.location.href);
            const protocol = currentUrl.protocol;
            const hostWithPort = currentUrl.host;

            const jwtPayload = decodeJwt(token);

            window.location.href = `${protocol}//${hostWithPort}/${jwtPayload?.slug}/login`;
          }
        },
        failureCallback: (error) => {},
      })
    );
  };

  return (
    <>
      <Layout>
        <Content
          style={{
            padding: "24px",
            // background: "#f0f2f5",
            minHeight: "100vh",
            overflow: "auto",
          }}
        >
          <Card
            bordered={true}
            style={{
              minWidth: "500px",
              margin: "auto",
              marginTop: "50px",
              padding: "20px",
              textAlign: "center",
            }}
          >
            <img
              src={AppLogo}
              alt="Next AI"
              style={{
                height: "80px",
                marginRight: "16px",
                marginTop: "7px",
                marginBottom: "0px",
              }}
            />

            {token && username && role ? (
              <>
                <Title level={3} style={{ marginBottom: "30px" }}>
                  Eko Registration
                </Title>

                {/* Invite User form */}
                <Form
                  form={form}
                  layout="vertical"
                  onFinish={onFinish}
                  initialValues={{ username, token, role }}
                  style={{ marginBottom: "20px" }}
                >
                  {/* Username (Prepopulated and Disabled) */}
                  <Form.Item label="Username" name="username">
                    <Input disabled />
                  </Form.Item>

                  {/* Token (Prepopulated and Disabled) */}
                  <Form.Item label="Role" name="role">
                    <Input disabled />
                  </Form.Item>

                  {/* Password */}
                  <Form.Item
                    label="Password"
                    name="password"
                    rules={[
                      {
                        required: true,
                        message: "Please input your password!",
                      },
                    ]}
                  >
                    <Input.Password placeholder="Enter your password" />
                  </Form.Item>

                  {/* Confirm Password */}
                  <Form.Item
                    label="Confirm Password"
                    name="confirmPassword"
                    dependencies={["password"]}
                    rules={[
                      {
                        required: true,
                        message: "Please confirm your password!",
                      },
                      ({ getFieldValue }) => ({
                        validator(_, value) {
                          if (!value || getFieldValue("password") === value) {
                            return Promise.resolve();
                          }
                          return Promise.reject(
                            new Error("Passwords do not match!")
                          );
                        },
                      }),
                    ]}
                  >
                    <Input.Password placeholder="Confirm your password" />
                  </Form.Item>

                  {/* Submit Button */}
                  <Form.Item>
                    <Button
                      type="primary"
                      htmlType="submit"
                      loading={loading}
                      block
                    >
                      Register
                    </Button>
                  </Form.Item>
                </Form>
              </>
            ) : (
              <Row
                justify="center"
                align="middle"
                style={{ marginTop: "20px" }}
              >
                <Col>
                  <ExclamationCircleOutlined
                    style={{ color: "red", fontSize: "48px" }}
                  />
                  <Title level={3} style={{ marginTop: "16px" }}>
                    Invalid Link
                  </Title>
                  <Text style={{ fontSize: "16px" }}>
                    Please contact the Supervisor.
                  </Text>
                </Col>
              </Row>
            )}
          </Card>
        </Content>
      </Layout>
    </>
  );
};

// Styling for the page
const styles = {
  container: {
    display: "flex",
    justifyContent: "center",
    alignItems: "center",
    height: "100vh",
    backgroundColor: "#f0f2f5",
  },
  card: {
    width: 400,
    padding: "40px 20px",
    boxShadow: "0 4px 12px rgba(0, 0, 0, 0.1)",
    borderRadius: "8px",
    backgroundColor: "#fff",
  },
  logo: {
    display: "block",
    margin: "0 auto",
    height: 120,
  },
  button: {
    backgroundColor: "#1890ff",
    borderColor: "#1890ff",
    fontWeight: "bold",
  },
};

export default UserRegistrationComponent;
