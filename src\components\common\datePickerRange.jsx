import React, { useEffect } from "react";
import { DatePicker } from "antd";
import "antd/dist/reset.css";
import dayjs from "dayjs";
import { useAppDispatch, useAppSelector } from "../../hooks/reduxHooks";
import { storeDateRange } from "../../store/slices/profile.slice";

const { RangePicker } = DatePicker;

const RangePickerComponent = ({ width, additionalStyle, allowClear }) => {
  const disableFutureDates = (current) => {
    // Disable dates greater than today
    return current && current > dayjs().endOf("day");
  };

  // Redux
  const dispatch = useAppDispatch();
  const { dateRangeValue } = useAppSelector((state) => state.profile);

  const rangePickerValue = [
    dateRangeValue.start_date ? dayjs(dateRangeValue.start_date) : null,
    dateRangeValue.end_date ? dayjs(dateRangeValue.end_date) : null,
  ];

  const handleChange = (dates, dateStrings) => {
    if (dates === null) {
      dispatch(storeDateRange({}));
      return;
    }

    dispatch(
      storeDateRange({
        start_date: dateStrings[0],
        end_date: dateStrings[1],
      })
    );
  };

  return (
    <RangePicker
      onChange={handleChange}
      value={rangePickerValue}
      style={{
        width: width ?? "100%",
        ...(additionalStyle || {})
      }}
      disabledDate={disableFutureDates}
      allowClear={!(allowClear === false)}
    />
  );
};

export default RangePickerComponent;
