import React, { useState, useEffect } from "react";
import {
  Modal,
  Form,
  Button,
  DatePicker,
  Badge,
  Divider,
  Space,
  Typography,
  Tag,
} from "antd";
import {
  CalendarOutlined,
  FilterOutlined,
  CloseOutlined,
  TagOutlined,
  ShoppingOutlined,
  GlobalOutlined,
  SmileOutlined,
  ThunderboltOutlined,
} from "@ant-design/icons";
import SelectOption from "../common/selectOption";
import { transformString } from "../../helpers/channelNameTransformation";
import { useLocation, useNavigate } from "react-router-dom";

const { RangePicker } = DatePicker;
const { Text } = Typography;

const FilterModal = ({
  ctaOptions = [],
  ctaStatus = [],
  tagsList = [],
  productsList = [],
  languagesList = [],
  sentimentsList = [],
  selectedCTA,
  selectedCTAStatus,
  channel,
  selectedProduct,
  selectedLanguage,
  selectedSentiment,
  dateRangeAntdValue,
  handleCTAOptions,
  handleCTAStatus,
  handleChannelChange,
  handleDateChange,
  setSelectedProduct,
  setSelectedLanguage,
  setSelectedSentiment,
  setSearchKeywords,
}) => {
  const [isModalVisible, setIsModalVisible] = useState(false);
  // Add internal state to track date range locally
  const [localDateRange, setLocalDateRange] = useState(dateRangeAntdValue);
  const [form] = Form.useForm();
  const location = useLocation();
  const navigate = useNavigate();

  useEffect(() => {
    if (location.state?.topic) {
      setSelectedProduct(location.state.topic);
    }

    if (location.state?.phoneID || location.state?.topic) {
      navigate(location.pathname, { replace: true });
    }
  }, []);

  // Update local date range state when parent prop changes
  useEffect(() => {
    setLocalDateRange(dateRangeAntdValue);
  }, [dateRangeAntdValue]);

  useEffect(() => {
    // Set form values when modal opens
    if (isModalVisible) {
      form.setFieldsValue({
        cta: selectedCTA,
        status: selectedCTAStatus,
        channel: channel,
        products: selectedProduct,
        language: selectedLanguage,
        sentiment: selectedSentiment,
        dateRange: localDateRange, // Use local state to ensure consistency
      });
    }
  }, [
    isModalVisible,
    form,
    selectedCTA,
    selectedCTAStatus,
    channel,
    selectedProduct,
    selectedLanguage,
    selectedSentiment,
    localDateRange, // Use the local state here
  ]);

  const showModal = () => {
    setIsModalVisible(true);
  };

  const handleOk = () => {
    setIsModalVisible(false);
  };

  const handleCancel = () => {
    setIsModalVisible(false);
  };

  const handleReset = () => {
    // Reset form fields
    form.resetFields();

    // Reset all selected values
    if (handleCTAOptions) handleCTAOptions(undefined);
    if (handleCTAStatus) handleCTAStatus(undefined);
    if (handleChannelChange) handleChannelChange(undefined);
    if (setSelectedProduct) setSelectedProduct(undefined);
    if (setSelectedLanguage) setSelectedLanguage(undefined);
    if (setSelectedSentiment) setSelectedSentiment(undefined);
    if (setSearchKeywords) setSearchKeywords(undefined);

    // Reset local date range first for immediate UI update
    setLocalDateRange(null);

    // Then notify parent
    if (handleDateChange) {
      handleDateChange(null);
    }
  };

  // Calculate active filters count
  const getActiveFiltersCount = () => {
    let count = 0;
    if (selectedCTA) count++;
    if (selectedCTAStatus) count++;
    if (channel) count++;
    if (selectedProduct) count++;
    if (selectedLanguage) count++;
    if (selectedSentiment) count++;
    if (
      localDateRange &&
      localDateRange.length === 2 &&
      localDateRange[0] &&
      localDateRange[1]
    )
      count++;
    return count;
  };

  const activeFiltersCount = getActiveFiltersCount();
  const hasActiveFilters = activeFiltersCount > 0;

  // Get appropriate icon for each filter type
  const getFilterIcon = (type) => {
    switch (type) {
      case "cta":
        return <ThunderboltOutlined />;
      case "channel":
        return <TagOutlined />;
      case "products":
        return <ShoppingOutlined />;
      case "language":
        return <GlobalOutlined />;
      case "sentiment":
        return <SmileOutlined />;
      default:
        return <FilterOutlined />;
    }
  };

  // Get active filters for summary
  const getActiveFilters = () => {
    const filters = [];

    if (selectedCTA)
      filters.push({
        type: "cta",
        label: "CTA",
        value: selectedCTA,
        icon: <ThunderboltOutlined />,
      });
    if (selectedCTAStatus)
      filters.push({
        type: "status",
        label: "CTA Status",
        value: selectedCTAStatus,
        icon: <ThunderboltOutlined />,
      });

    if (channel)
      filters.push({
        type: "channel",
        label: "Channel",
        value: channel,
        icon: <TagOutlined />,
      });

    if (selectedProduct)
      filters.push({
        type: "product",
        label: "Topic",
        value: selectedProduct,
        icon: <ShoppingOutlined />,
      });

    if (selectedLanguage)
      filters.push({
        type: "language",
        label: "Language",
        value: selectedLanguage,
        icon: <GlobalOutlined />,
      });

    if (selectedSentiment)
      filters.push({
        type: "sentiment",
        label: "Sentiment",
        value: selectedSentiment,
        icon: <SmileOutlined />,
      });

    // Make sure we only count date range if both values exist and are valid
    if (
      localDateRange &&
      localDateRange.length === 2 &&
      localDateRange[0] &&
      localDateRange[1]
    ) {
      const startDate = localDateRange[0]?.format("MMM DD, YYYY");
      const endDate = localDateRange[1]?.format("MMM DD, YYYY");
      filters.push({
        type: "dateRange",
        label: "Date Range",
        value: `${startDate} - ${endDate}`,
        icon: <CalendarOutlined />,
      });
    }

    return filters;
  };

  // Active filters summary component
  const ActiveFiltersSummary = () => {
    const activeFilters = getActiveFilters();

    if (!hasActiveFilters) {
      return null;
    }

    // Handle closing individual filter tags
    const handleCloseTag = (filterType, e) => {
      // Prevent the default close behavior to avoid event propagation issues
      if (e) {
        e.stopPropagation();
      }

      switch (filterType) {
        case "cta":
          if (handleCTAOptions) handleCTAOptions(undefined);
          break;
        case "status":
          if (handleCTAStatus) handleCTAStatus(undefined);
          break;
        case "channel":
          if (handleChannelChange) handleChannelChange(undefined);
          break;
        case "product":
          if (setSelectedProduct) setSelectedProduct(undefined);
          break;
        case "language":
          if (setSelectedLanguage) setSelectedLanguage(undefined);
          break;
        case "sentiment":
          if (setSelectedSentiment) setSelectedSentiment(undefined);
          break;
        case "dateRange":
          // First update our local state - this will immediately affect the UI
          setLocalDateRange(null);
          // Update the form state
          form.setFieldValue("dateRange", null);
          // Then notify the parent - this might be async
          if (handleDateChange) {
            handleDateChange(null);
          }

          break;
        default:
          break;
      }
    };

    return (
      <div style={{ marginBottom: "20px" }}>
        <Text strong style={{ marginBottom: "8px", display: "block" }}>
          Active Filters
        </Text>
        <div
          style={{
            display: "flex",
            flexWrap: "wrap",
            gap: "8px",
          }}
        >
          {activeFilters.map((filter, index) => (
            <Tag
              key={index}
              icon={filter.icon}
              color="blue"
              closable
              onClose={(e) => handleCloseTag(filter.type, e)}
              style={{ borderRadius: "4px", padding: "4px 8px" }}
            >
              {filter.label}: {transformString(filter.value)}
              {/* {filter.label}: {filter.value} */}
            </Tag>
          ))}
        </div>
      </div>
    );
  };

  // Add minimal CSS for better styling
  const GlobalStyles = `
.filter-modal .ant-form-item-label > label {
  font-weight: 500;
}

.filter-modal .ant-select-selector,
.filter-modal .ant-picker {
  border-radius: 4px;
}

.filter-modal .ant-tag {
  margin-right: 0;
}
`;

  // Add the global styles to the document
  useEffect(() => {
    if (typeof document !== "undefined") {
      const styleTag = document.createElement("style");
      styleTag.innerHTML = GlobalStyles;
      document.head.appendChild(styleTag);
      return () => {
        document.head.removeChild(styleTag);
      };
    }
    return () => {};
  }, []);

  return (
    <>
      <Button
        onClick={showModal}
        style={{
          fontSize: "15px",
          borderRadius: "6px",
          margin: "10px",
          display: "flex",
          alignItems: "center",
          gap: "6px",
        }}
      >
        <FilterOutlined />
        <span style={{ fontWeight: "500" }}>Filters</span>
        {hasActiveFilters && (
          <Badge
            count={activeFiltersCount}
            style={{ backgroundColor: "#1890ff" }}
          />
        )}
      </Button>

      <Modal
        title={
          <div style={{ display: "flex", alignItems: "center", gap: "8px" }}>
            <FilterOutlined />
            <span>Filters</span>
            {hasActiveFilters && (
              <Badge count={activeFiltersCount} style={{ marginLeft: "8px" }} />
            )}
          </div>
        }
        open={isModalVisible}
        onOk={handleOk}
        onCancel={handleCancel}
        width={600}
        className="filter-modal"
        closeIcon={<CloseOutlined />}
        styles={{
          body: {
            maxHeight: "60vh",
            overflowY: "auto",
            paddingRight: "8px",
          },
        }}
        footer={[
          <div
            key="footer"
            style={{
              display: "flex",
              justifyContent: "space-between",
              alignItems: "center",
            }}
          >
            <Button
              key="reset"
              onClick={handleReset}
              disabled={!hasActiveFilters}
              danger
            >
              Reset Filters
            </Button>
            <Space>
              <Button key="cancel" onClick={handleCancel}>
                Cancel
              </Button>
              {/* <Button key="apply" type="primary" onClick={handleOk}>
                Apply Filters
              </Button> */}
            </Space>
          </div>,
        ]}
      >
        <Divider style={{ margin: "0 0 20px 0" }} />

        <ActiveFiltersSummary />

        <Form layout="vertical" form={form}>
          <div
            style={{
              display: "grid",
              gridTemplateColumns: "1fr 1fr",
              gap: "16px",
              marginBottom: "16px",
            }}
          >
            {ctaOptions?.length > 0 && (
              <Form.Item
                name="cta"
                label={
                  <div
                    style={{
                      display: "flex",
                      alignItems: "center",
                      gap: "6px",
                    }}
                  >
                    {getFilterIcon("cta")}
                    <span>CTA</span>
                  </div>
                }
              >
                <SelectOption
                  options={ctaOptions}
                  onChange={handleCTAOptions}
                  placeholder="Select CTA"
                  value={selectedCTA}
                />
              </Form.Item>
            )}
            {ctaStatus?.length > 0 && (
              <Form.Item
                name="status"
                label={
                  <div
                    style={{
                      display: "flex",
                      alignItems: "center",
                      gap: "6px",
                    }}
                  >
                    {getFilterIcon("cta")}
                    <span>CTA Status</span>
                  </div>
                }
              >
                <SelectOption
                  options={ctaStatus}
                  onChange={handleCTAStatus}
                  placeholder="Select CTA status"
                  value={selectedCTAStatus}
                />
              </Form.Item>
            )}

            {tagsList?.length > 0 && (
              <Form.Item
                name="channel"
                label={
                  <div
                    style={{
                      display: "flex",
                      alignItems: "center",
                      gap: "6px",
                    }}
                  >
                    {getFilterIcon("channel")}
                    <span>Channel</span>
                  </div>
                }
              >
                <SelectOption
                  placeholder="Select Channel"
                  options={tagsList}
                  onChange={handleChannelChange}
                  value={channel}
                />
              </Form.Item>
            )}

            {languagesList?.length > 0 && (
              <Form.Item
                name="language"
                label={
                  <div
                    style={{
                      display: "flex",
                      alignItems: "center",
                      gap: "6px",
                    }}
                  >
                    {getFilterIcon("language")}
                    <span>Language</span>
                  </div>
                }
              >
                <SelectOption
                  placeholder="Select Language"
                  options={languagesList}
                  onChange={(value) => setSelectedLanguage(value)}
                  value={selectedLanguage}
                />
              </Form.Item>
            )}

            {productsList?.length > 0 && (
              <Form.Item
                name="products"
                label={
                  <div
                    style={{
                      display: "flex",
                      alignItems: "center",
                      gap: "6px",
                    }}
                  >
                    {getFilterIcon("products")}
                    <span>Topic</span>
                  </div>
                }
                style={{ gridColumn: "1 / -1" }} // This makes it span all columns
              >
                <SelectOption
                  placeholder="Select Topic"
                  options={productsList}
                  onChange={(value) => setSelectedProduct(value)}
                  value={selectedProduct}
                />
              </Form.Item>
            )}

            {sentimentsList?.length > 0 && (
              <Form.Item
                name="sentiment"
                label={
                  <div
                    style={{
                      display: "flex",
                      alignItems: "center",
                      gap: "6px",
                    }}
                  >
                    {getFilterIcon("sentiment")}
                    <span>Sentiment</span>
                  </div>
                }
              >
                <SelectOption
                  placeholder="Select Sentiment"
                  options={sentimentsList}
                  onChange={(value) => setSelectedSentiment(value)}
                  value={selectedSentiment}
                />
              </Form.Item>
            )}
          </div>

          <Form.Item
            name="dateRange"
            label={
              <div
                style={{ display: "flex", alignItems: "center", gap: "6px" }}
              >
                <CalendarOutlined />
                <span>Date Range</span>
              </div>
            }
          >
            <RangePicker
              value={localDateRange}
              disabledDate={(d) => d && d.valueOf() > Date.now()}
              onChange={(dates) => {
                setLocalDateRange(dates);
                if (handleDateChange) {
                  handleDateChange(dates);
                }
              }}
              style={{
                width: "100%",
                borderRadius: "4px",
                height: "32px",
              }}
              placeholder={["Start date", "End date"]}
              allowClear
              format="YYYY-MM-DD"
            />
          </Form.Item>
        </Form>
      </Modal>
    </>
  );
};

export default FilterModal;
