import React from "react";
import { Typography, Space } from "antd";
import { MessageOutlined } from "@ant-design/icons";

const { Text, Title } = Typography;

const EmptyChat = () => {
  return (
    <div
      style={{
        display: "flex",
        flexDirection: "column",
        alignItems: "center",
        justifyContent: "center",
        height: "100%",
        padding: "24px",
        textAlign: "center",
      }}
    >
      <Space direction="vertical" size="large" align="center">
        <div
          style={{
            width: "90px",
            height: "90px",
            borderRadius: "50%",
            background: "#f0f0f0",
            display: "flex",
            alignItems: "center",
            justifyContent: "center",
            marginBottom: "10px",
            boxShadow: "0 2px 8px rgba(0, 0, 0, 0.05)",
          }}
        >
          <MessageOutlined style={{ fontSize: "40px", color: "#666666" }} />
        </div>

        <Title
          level={3}
          style={{ margin: 0, fontWeight: "500", color: "#999999" }}
        >
          Explore the Knowledge Base
        </Title>

        <Text
          style={{
            maxWidth: "450px",
            fontSize: "15px",
            marginTop: "8px",
            color: "#777777",
          }}
        >
          Enter your query below, and the AI will provide the most relevant and
          current information.
        </Text>
      </Space>
    </div>
  );
};

export default EmptyChat;
