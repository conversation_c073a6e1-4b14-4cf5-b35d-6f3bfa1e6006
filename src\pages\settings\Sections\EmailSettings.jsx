import React, { useEffect, useState } from "react";
import {
  Card,
  Typography,
  Input,
  Button,
  Switch,
  message,
  Modal,
  Tooltip,
  Skeleton,
  Divider,
} from "antd";
import { DeleteOutlined } from "@ant-design/icons";
import {
  getEmailConfigApi,
  addEmailRecipientApi,
  updateEmailRecipientCtaStatusApi,
  deleteEmailRecipientApi,
  updateEmailRecipientStatusApi,
} from "../../../services/email.service";
import { useAppDispatch } from "../../../hooks/reduxHooks";

const { Title, Text } = Typography;

const EmailSettings = () => {
  const [newEmail, setNewEmail] = useState("");
  const [newName, setNewName] = useState("");
  const [isModalVisible, setIsModalVisible] = useState(false);
  const [emailToDelete, setEmailToDelete] = useState(null);
  const [loading, setLoading] = useState(false);
  const [emailConfig, setEmailConfig] = useState(null);

  const dispatch = useAppDispatch();

  useEffect(() => {
    setLoading(true);
    dispatch(
      getEmailConfigApi({
        successCallback: (response) => {
          // Normalize id to _id for consistency
          const normalizedResponse = {
            ...response,
            emails: response.emails.map((email) => ({
              ...email,
              _id: email.id || email._id,
              enable: email.enable !== undefined ? email.enable : true,
              cta_types: email.cta_types.map((cta) => ({
                ...cta,
                _id: cta.id || cta._id,
              })),
            })),
          };
          setEmailConfig(normalizedResponse);
        },
        failureCallback: (errors) => {
          message.error("Failed to fetch email configuration.");
        },
        finalCallback: () => {
          setLoading(false);
        },
      })
    );
  }, [dispatch]);

  const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;

  const handleAddEmail = () => {
    if (!emailRegex.test(newEmail)) {
      message.error("Enter a valid email address.");
      return;
    }

    const data = {
      email: newEmail,
      name: newName || newEmail.split("@")[0],
    };

    dispatch(
      addEmailRecipientApi({
        params: data,
        successCallback: (response) => {
          setEmailConfig((prev) => ({
            ...prev,
            emails: [
              ...prev.emails,
              {
                ...response.data,
                _id: response.data.id || response.data._id,
                enable:
                  response.data.enable !== undefined
                    ? response.data.enable
                    : true,
                cta_types: response.data.cta_types.map((cta) => ({
                  ...cta,
                  _id: cta.id || cta._id,
                })),
              },
            ],
          }));
          setNewEmail("");
          setNewName("");
          message.success(response.message);
        },
        failureCallback: (errors) => {
          message.error("Failed to add email recipient.");
        },
        finalCallback: () => {},
      })
    );
  };

  const handleToggleEmailStatus = (emailId) => {
    const email = emailConfig.emails.find((e) => e._id === emailId);
    const newStatus = !email.enable;

    dispatch(
      updateEmailRecipientStatusApi({
        params: {
          id: emailId,
          status: newStatus ? "enabled" : "disabled",
        },
        successCallback: (response) => {
          setEmailConfig((prev) => ({
            ...prev,
            emails: prev.emails.map((e) =>
              e._id === emailId ? { ...e, enable: newStatus } : e
            ),
          }));
          message.success(
            `Email notifications ${newStatus ? "enabled" : "disabled"} for ${
              email.email
            }.`
          );
        },
        failureCallback: (errors) => {
          message.error("Failed to update email status.");
        },
        finalCallback: () => {},
      })
    );
  };

  const handleToggleCta = (emailId, ctaName) => {
    const email = emailConfig.emails.find((e) => e._id === emailId);
    const currentCta = email.cta_types.find((cta) => cta.name === ctaName);
    const newCtaEnable = !currentCta.enable;

    dispatch(
      updateEmailRecipientCtaStatusApi({
        params: {
          id: emailId,
          name: ctaName,
          enable: newCtaEnable,
        },
        successCallback: (response) => {
          setEmailConfig((prev) => ({
            ...prev,
            emails: prev.emails.map((e) =>
              e._id === emailId
                ? {
                    ...e,
                    cta_types: e.cta_types.map((cta) =>
                      cta.name === ctaName
                        ? { ...cta, enable: newCtaEnable }
                        : cta
                    ),
                  }
                : e
            ),
          }));
          message.success(
            `${
              ctaName === "ticket" ? "Ticket" : "Booking"
            } notifications updated for ${email.email}.`
          );
        },
        failureCallback: (errors) => {
          message.error("Failed to update CTA settings.");
        },
        finalCallback: () => {},
      })
    );
  };

  const showDeleteModal = (id) => {
    setIsModalVisible(true);
    setEmailToDelete(id);
  };

  const handleDelete = () => {
    dispatch(
      deleteEmailRecipientApi({
        params: { id: emailToDelete },
        successCallback: (response) => {
          setEmailConfig((prev) => ({
            ...prev,
            emails: prev.emails.filter((email) => email._id !== emailToDelete),
          }));
          setIsModalVisible(false);
          setEmailToDelete(null);
          message.success(response.message);
        },
        failureCallback: (errors) => {},
        finalCallback: () => {},
      })
    );
  };

  const handleCancelDelete = () => {
    setIsModalVisible(false);
    setEmailToDelete(null);
  };

  const selectedEmailObj = emailConfig?.emails.find(
    (e) => e._id === emailToDelete
  );

  return (
    <Card
      style={{
        width: "100%",
        borderRadius: 6,
        boxShadow: "0 1px 4px rgba(0, 0, 0, 0.05)",
      }}
      bodyStyle={{
        padding: 24,
        width: "100%",
      }}
    >
      <Title level={4} style={{ marginBottom: 8, fontWeight: 600 }}>
        Email Settings
      </Title>

      <Divider style={{ margin: "8px 0 16px 0", width: "100%" }} />

      {/* Add Email */}
      <Title level={5} style={{ marginBottom: 8 }}>
        Add Recipient
      </Title>
      <div
        style={{
          display: "flex",
          alignItems: "flex-start",
          flexDirection: "column",
          width: "100%",
          marginBottom: 4,
        }}
      >
        <div
          style={{ display: "flex", width: "100%", marginBottom: 8, gap: 8 }}
        >
          <Tooltip
            title="Enter a valid email address to receive notifications"
            placement="top"
            style={{ flex: 1, marginRight: 12 }}
          >
            <Input
              placeholder="e.g. <EMAIL>"
              value={newEmail}
              onChange={(e) => setNewEmail(e.target.value)}
              style={{
                width: "100%",
                borderRadius: 6,
              }}
            />
          </Tooltip>
          <Tooltip
            title="Optionally provide a display name for this recipient"
            placement="top"
            style={{ flex: 1, marginRight: 12 }}
          >
            <Input
              placeholder="Name (optional)"
              value={newName}
              onChange={(e) => setNewName(e.target.value)}
              style={{
                width: "100%",
                borderRadius: 6,
              }}
            />
          </Tooltip>
          <Button
            type="primary"
            style={{ borderRadius: 6 }}
            onClick={handleAddEmail}
            disabled={loading}
          >
            Add Recipient
          </Button>
        </div>
      </div>
      <Text type="secondary" style={{ fontSize: 12 }}>
        After adding an email, select which notifications should be sent to it.
      </Text>

      {/* Email List */}
      <Title level={5} style={{ margin: "32px 0 16px" }}>
        Email Recipients ({emailConfig?.emails.length || 0})
      </Title>

      {loading || !emailConfig ? (
        <div>
          {[...Array(2)].map((_, index) => (
            <div
              key={index}
              style={{
                border: "1px solid #f0f0f0",
                borderRadius: 8,
                padding: 16,
                marginBottom: 16,
              }}
            >
              <div
                style={{
                  display: "flex",
                  justifyContent: "space-between",
                  alignItems: "center",
                  marginBottom: 12,
                }}
              >
                <Skeleton.Input active size="small" style={{ width: 200 }} />
                <div style={{ display: "flex", alignItems: "center", gap: 16 }}>
                  <Skeleton.Button active size="small" />
                  <Skeleton.Input active size="small" style={{ width: 140 }} />
                  <Skeleton.Button active size="small" shape="circle" />
                </div>
              </div>
              <div
                style={{
                  display: "flex",
                  gap: 40,
                  flexWrap: "wrap",
                  marginTop: 8,
                }}
              >
                <div style={{ display: "flex", alignItems: "center" }}>
                  <Skeleton.Button active size="small" />
                  <Skeleton.Input
                    active
                    size="small"
                    style={{ width: 140, marginLeft: 8 }}
                  />
                </div>
                <div style={{ display: "flex", alignItems: "center" }}>
                  <Skeleton.Button active size="small" />
                  <Skeleton.Input
                    active
                    size="small"
                    style={{ width: 140, marginLeft: 8 }}
                  />
                </div>
              </div>
            </div>
          ))}
        </div>
      ) : (
        emailConfig.emails.map(({ _id, email, name, cta_types, enable }) => (
          <div
            key={_id}
            style={{
              border: "1px solid #f0f0f0",
              borderRadius: 8,
              padding: 16,
              marginBottom: 16,
            }}
          >
            <div
              style={{
                display: "flex",
                justifyContent: "space-between",
                alignItems: "center",
                marginBottom: 12,
              }}
            >
              <Text strong>
                {name} ({email})
              </Text>
              <div style={{ display: "flex", alignItems: "center", gap: 16 }}>
                <Tooltip
                  title={
                    enable
                      ? "Email notifications are enabled for this recipient. Click to disable."
                      : "Email notifications are disabled for this recipient. Click to enable."
                  }
                  placement="top"
                >
                  <Switch
                    size="small"
                    checked={enable}
                    onChange={() => handleToggleEmailStatus(_id)}
                    style={{ marginRight: 8 }}
                  />
                  <Text style={{ width: 140 }}>
                    {enable ? "Enabled" : "Disabled"}
                  </Text>
                </Tooltip>
                <Tooltip
                  title="Remove this email from recipients"
                  placement="top"
                >
                  <Button
                    type="text"
                    icon={<DeleteOutlined style={{ color: "#ff4d4f" }} />}
                    onClick={() => showDeleteModal(_id)}
                    aria-label={`Delete ${email}`}
                  />
                </Tooltip>
              </div>
            </div>

            <div
              style={{
                display: "flex",
                gap: 40,
                flexWrap: "wrap",
                marginTop: 8,
              }}
            >
              <Tooltip
                title={
                  enable
                    ? "Send ticket-related notifications"
                    : "Enable email status to modify ticket notifications"
                }
                placement="top"
              >
                <div style={{ display: "flex", alignItems: "center" }}>
                  <Switch
                    size="small"
                    checked={
                      cta_types.find((cta) => cta.name === "ticket").enable
                    }
                    onChange={() => handleToggleCta(_id, "ticket")}
                    style={{ marginRight: 8 }}
                    disabled={!enable}
                  />
                  <Text style={{ width: 140 }}>Ticket Notifications</Text>
                </div>
              </Tooltip>

              <Tooltip
                title={
                  enable
                    ? "Send booking-related notifications"
                    : "Enable email status to modify booking notifications"
                }
                placement="top"
              >
                <div style={{ display: "flex", alignItems: "center" }}>
                  <Switch
                    size="small"
                    checked={
                      cta_types.find((cta) => cta.name === "booking").enable
                    }
                    onChange={() => handleToggleCta(_id, "booking")}
                    style={{ marginRight: 8 }}
                    disabled={!enable}
                  />
                  <Text style={{ width: 140, whiteSpace: "nowrap" }}>
                    Booking Notifications
                  </Text>
                </div>
              </Tooltip>
            </div>
          </div>
        ))
      )}

      {/* Delete Modal */}
      <Modal
        title="Remove Email"
        open={isModalVisible}
        onOk={handleDelete}
        onCancel={handleCancelDelete}
        okText="Yes, Remove"
        cancelText="Cancel"
        transitionName=""
        maskTransitionName=""
      >
        {selectedEmailObj && (
          <>
            <Text>
              Are you sure you want to remove{" "}
              <Text strong>
                {selectedEmailObj.name} ({selectedEmailObj.email})
              </Text>{" "}
              from notification recipients?
            </Text>
            <br />
            <br />
          </>
        )}
        <Text type="secondary">
          This email will stop receiving any notifications. You can re-add it
          later if needed.
        </Text>
      </Modal>
    </Card>
  );
};

export default EmailSettings;
