import React, { useEffect, useState } from "react";
import {
  Modal,
  Input,
  Select,
  Button,
  Form,
  Row,
  Col,
  message,
  Space,
  Typography,
} from "antd";
import {
  CheckCircleTwoTone,
  CopyOutlined,
  PlusOutlined,
} from "@ant-design/icons";
import { useAppDispatch } from "../hooks/reduxHooks";
import { userInvitationApi } from "../services/users.service";

const { Option } = Select;

const InviteUserModal = ({
  isUserInviteModalVisible,
  setIsUserInviteModalVisible,
}) => {
  const [username, setUsername] = useState("");
  const [loading, setLoading] = useState(false);
  const [role, setRole] = useState("");
  const [form] = Form.useForm();
  const dispatch = useAppDispatch();

  const { Text } = Typography;

  const roleOptions = ["admin", "agent"];

  useEffect(() => {
    setUsername("");
    setRole("");
    form.resetFields();
  }, [isUserInviteModalVisible]);

  const handleOk = (values) => {
    setLoading(true);
    dispatch(
      userInvitationApi({
        data: values,
        finalCallback: () => {
          setLoading(false);
        },
        successCallback: (response) => {
          if (response.success) {
            message.success(response?.msg || "Token Generated!");

            const invitationURL = response?.registration_token
              ? `${window.location.origin}/invitation?token=${response?.registration_token}&username=${values?.username}&role=${values?.role}`
              : "";
            Modal.success({
              title: "Invitation Link",
              transitionName: "",
              maskTransitionName: "",
              icon: <CheckCircleTwoTone twoToneColor="#52c41a" />,
              okButtonProps: {
                style: {
                  margin: "0 auto", // Centers the button in the footer
                  display: "block",
                },
              },
              content: (
                <Space direction="vertical">
                  <Text
                    copyable
                    style={{
                      fontSize: "16px",
                      display: "inline-block",
                      padding: "10px",
                      background: "#f5f5f5",
                      borderRadius: "5px",
                      marginBottom: "10px",
                    }}
                  >
                    {invitationURL}
                  </Text>
                  <p>
                    <strong>
                      Please copy and paste this url in your browser.
                    </strong>
                  </p>
                </Space>
              ),
            });
            setIsUserInviteModalVisible(false);
          } else {
            message.error(response?.msg || "Failed to invite user.");
          }
        },
        failureCallback: (error) => {},
      })
    );
  };

  return (
    <div>
      <Modal
        title={
          <h3 style={{ textAlign: "center", fontWeight: "600" }}>
            Invite an User
          </h3>
        }
        onCancel={() => setIsUserInviteModalVisible(false)}
        width={350}
        visible={isUserInviteModalVisible}
        transitionName=""
        maskTransitionName=""
        footer={null}
      >
        <Form form={form} layout="vertical" onFinish={handleOk}>
          <Form.Item
            label="Username/Email"
            name="username"
            style={{ marginTop: "32px", marginBottom: "12px" }} // Apply margin here
            rules={[
              { required: true, message: "Please input the username!" },
              {
                min: 3,
                message: "Username must be at least 3 characters.",
              },
            ]}
          >
            <Input placeholder="Enter new agent username/email" />
          </Form.Item>

          <Form.Item
            label="Role"
            name="role"
            rules={[{ required: true, message: "Please select a role!" }]}
            style={{ marginBottom: "32px" }}
          >
            <Select placeholder="Assign a role">
              {roleOptions.map((role) => (
                <Option key={role} value={role}>
                  {role.charAt(0).toUpperCase() + role.slice(1)}
                </Option>
              ))}
            </Select>
          </Form.Item>
          {/* Submit Button */}
          <Form.Item style={{ textAlign: "center" }}>
            <Button
              type="primary"
              htmlType="submit"
              icon={<PlusOutlined />}
              loading={loading}
              block
            >
              Invite
            </Button>
          </Form.Item>
        </Form>
      </Modal>
    </div>
  );
};

export default InviteUserModal;
