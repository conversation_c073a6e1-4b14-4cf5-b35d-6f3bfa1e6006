import React, { useState } from "react";
import {
  Drawer,
  Typography,
  List,
  Card,
  Divider,
  Space,
  Tag,
  Collapse,
  Button,
  Empty,
  Descriptions,
} from "antd";
import {
  FileTextOutlined,
  LinkOutlined,
  DatabaseOutlined,
  SolutionOutlined,
  QuestionCircleOutlined,
} from "@ant-design/icons";
import { transformString } from "../../../../helpers/channelNameTransformation";

const { Title, Text, Paragraph } = Typography;
const { Panel } = Collapse;

const MetaDataSection = ({ drawerData }) => {
  const titleStyle = {
    fontSize: "16px",
    margin: 0,
  };

  const sectionTitleStyle = {
    display: "flex",
    alignItems: "center",
    marginBottom: "12px",
  };

  const iconStyle = {
    marginRight: "8px",
  };

  return (
    <div>
      <div style={{ marginBottom: "24px" }}>
        <div style={sectionTitleStyle}>
          <FileTextOutlined style={{ ...iconStyle, color: "#1890ff" }} />
          <Title level={4} style={titleStyle}>
            Document Sources
          </Title>
        </div>

        {drawerData?.metadata?.length > 0 ? (
          drawerData?.metadata?.map((item, index) => {
            return (
              <>
                <Card
                  style={{
                    margin: 0,
                    padding: 0,
                    marginBottom: "12px",
                  }}
                >
                  <div
                    style={{
                      display: "flex",
                      alignItems: "flex-start",
                    }}
                  >
                    <LinkOutlined
                      style={{
                        color: "#1890ff",
                        marginRight: "6px",
                        marginTop: "6px",
                      }}
                    />
                    <Typography.Link
                      href={item.source_url}
                      target="_blank"
                      rel="noopener noreferrer"
                      style={{
                        fontWeight: "bold",
                      }}
                    >
                      {item.source}
                    </Typography.Link>
                  </div>
                </Card>
              </>
            );
          })
        ) : (
          <div
            style={{
              padding: "6px 0",
              textAlign: "center",
              backgroundColor: "#f9f9f9",
              borderRadius: "8px",
              border: "1px solid #f0f0f0",
            }}
          >
            <Empty
              image={Empty.PRESENTED_IMAGE_SIMPLE}
              description={
                <Space direction="vertical" size="small">
                  <Typography.Text type="secondary">
                    No document sources were used in this response.
                  </Typography.Text>
                </Space>
              }
            />
          </div>
        )}
      </div>

      <div style={sectionTitleStyle}>
        <FileTextOutlined style={{ ...iconStyle, color: "#1890ff" }} />
        <Title level={4} style={titleStyle}>
          AI Actions
        </Title>
      </div>

      {drawerData?.information_gathering?.length > 0 ? (
        drawerData?.information_gathering?.map((item, index) => (
          <Card
            key={index}
            style={{
              marginBottom: 26,
              borderRadius: "8px",
              boxShadow: "0 2px 8px rgba(0,0,0,0.09)",
            }}
            bordered={false}
          >
            <Space direction="vertical" size="middle" style={{ width: "100%" }}>
              {Object.entries(item?.function_args)?.length !== 0 && (
                <div>
                  {Object.entries(item?.function_args).map(([key, value]) => {
                    if (value === null) return null;
                    return (
                      <div
                        key={key}
                        style={{
                          display: "flex",
                          marginBottom: "4px",
                        }}
                      >
                        <Text
                          strong
                          style={{
                            paddingRight: "8px",
                            minWidth: "100px",
                            maxWidth: "200px",
                            textTransform: "capitalize",
                          }}
                        >
                          {transformString(key)}:
                        </Text>
                        <Text>{transformString(value) ?? "N/A"}</Text>
                      </div>
                    );
                  })}
                  <Divider style={{ margin: "12px 0" }} />
                </div>
              )}

              <div>
                {Object.entries(item?.result).map(([key, value]) => {
                  if (value === null) return null;
                  return (
                    <div
                      key={key}
                      style={{
                        display: "flex",
                        marginBottom: "4px",
                      }}
                    >
                      <Text
                        strong
                        style={{
                          paddingRight: "8px",
                          minWidth: "100px",
                          maxWidth: "200px",
                          textTransform: "capitalize",
                        }}
                      >
                        {transformString(key)}:
                      </Text>
                      <Text>
                        {typeof value === "object"
                          ? JSON.stringify(value)
                          : String(value)}
                      </Text>
                    </div>
                  );
                })}
              </div>

              <div style={{ textAlign: "left" }}>
                <Tag color="blue" icon={<DatabaseOutlined />}>
                  {transformString(item.function_name)}
                </Tag>
              </div>
            </Space>
          </Card>
        ))
      ) : (
        <div
          style={{
            padding: "32px 0",
            textAlign: "center",
            backgroundColor: "#f9f9f9",
            borderRadius: "8px",
            border: "1px solid #f0f0f0",
          }}
        >
          <Empty
            image={Empty.PRESENTED_IMAGE_SIMPLE}
            description={
              <Space direction="vertical" size="small">
                <Typography.Text type="secondary">
                  The AI did not perform any action in this response.
                </Typography.Text>
              </Space>
            }
          />
        </div>
      )}
    </div>
  );
};

export default MetaDataSection;
