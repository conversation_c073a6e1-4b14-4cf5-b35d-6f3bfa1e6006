import React from "react";
import { Row, Col } from "antd";
import { useNavigate } from "react-router-dom";

export default function MainCards({ ctaCount, creditBalance }) {
  const navigate = useNavigate();

  const ctaData = ctaCount.data ?? [];

  const totalCta = ctaData.reduce((sum, item) => {
    if (item.type === "booking" || item.type === "ticket") {
      return sum + (item.count || 0);
    }
    return sum;
  }, 0);

  const cardData = [
    {
      title: "Pending Bookings",
      value: ctaData
        .filter((item) => item.type === "booking" && item.status === "open")
        .reduce((sum, item) => sum + (item.count || 0), 0),
      icon: "icon-park-solid:appointment",
      trend:
        ctaData
          .filter(
            (item) => item.type === "booking" && item.status === "resolved"
          )
          .reduce((sum, item) => sum + (item.count || 0), 0) + " resolved",
      trendIcon: "uil:comment-verify",
      bg: "linear-gradient(135deg, #e6f2ff, #b3d7ff)",
      color: "#0052cc",
      accent: "#deebff",
      onClick: () => {
        navigate("/cta", {
          state: {
            type: "booking",
            status: "open",
          },
        });
      },
      trendOnClick: () => {
        navigate("/cta", {
          state: {
            type: "booking",
            status: "resolved",
          },
        });
      },
    },
    {
      title: "Pending Tickets",
      value: ctaData
        .filter((item) => item.type === "ticket" && item.status === "open")
        .reduce((sum, item) => sum + (item.count || 0), 0),
      icon: "ic:baseline-contact-support",
      trend:
        ctaData
          .filter(
            (item) => item.type === "ticket" && item.status === "resolved"
          )
          .reduce((sum, item) => sum + (item.count || 0), 0) + " resolved",
      trendIcon: "uil:comment-verify",
      bg: "linear-gradient(135deg, #e0f0ff, #b8dcff)",
      color: "#0065d2",
      accent: "#d4e9ff",
      onClick: () => {
        navigate("/cta", {
          state: {
            type: "ticket",
            status: "open",
          },
        });
      },
      trendOnClick: () => {
        navigate("/cta", {
          state: {
            type: "ticket",
            status: "resolved",
          },
        });
      },
    },
    {
      title: "Total Messages",
      value: ctaCount.total_chats ?? 0,
      icon: "ri:chat-smile-ai-fill",
      trend: totalCta + " Interventions",
      trendIcon: "hugeicons:user-settings-02",
      bg: "linear-gradient(135deg, #e1eeff, #c2d9ff)",
      color: "#0747a6",
      accent: "#cce0ff",
      onClick: () => {
        navigate("/channels");
      },
      trendOnClick: () => {
        navigate("/cta", {
          state: {
            type: null,
            status: null,
          },
        });
      },
    },
    {
      title: "Remaining Credits",
      value: creditBalance.remaining ?? 0,
      icon: "bxs:coin-stack",
      trend: `${
        creditBalance.total_credits - creditBalance.remaining || 0
      } Used`,
      trendIcon: "mingcute:check-2-fill",
      bg: "linear-gradient(135deg,rgb(197, 210, 226),rgb(188, 214, 255))",
      color: "#0747a6",
      accent: "#cce0ff",
      onClick: () => {
        if (localStorage.getItem("role") === "admin") {
          navigate("/settings?tab=billing-usage");
        }
      },
      trendOnClick: () => {
        if (localStorage.getItem("role") === "admin") {
          navigate("/settings?tab=billing-usage");
        }
      },
    },
  ];

  return (
    <Row gutter={[24, 24]}>
      {cardData.map((card, index) => (
        <Col key={index} xs={24} sm={12} lg={6}>
          <div
            onClick={card.onClick}
            style={{
              background: card.bg,
              borderRadius: "16px",
              boxShadow: "0 4px 12px rgba(0,0,0,0.05)",
              color: card.color,
              height: "180px",
              display: "flex",
              flexDirection: "column",
              justifyContent: "space-between",
              padding: "24px",
              position: "relative",
              overflow: "hidden",
              transition: "transform 0.3s ease, box-shadow 0.3s ease",
              cursor:
                card?.title === "Remaining Credits" &&
                localStorage.getItem("role") !== "admin"
                  ? null
                  : "pointer",
            }}
          >
            <div
              style={{
                position: "absolute",
                top: "-50px",
                right: "-50px",
                width: "120px",
                height: "120px",
                borderRadius: "50%",
                background: card.accent,
                opacity: 0.6,
              }}
            />
            <div
              style={{
                display: "flex",
                justifyContent: "space-between",
                zIndex: 1,
              }}
            >
              <div
                style={{
                  width: "48px",
                  height: "48px",
                  borderRadius: "12px",
                  background: card.accent,
                  display: "flex",
                  alignItems: "center",
                  justifyContent: "center",
                }}
              >
                <iconify-icon
                  icon={card.icon}
                  width="24"
                  height="24"
                  style={{ color: card.color }}
                />
              </div>
              <div
                style={{ textAlign: "right", cursor: "pointer" }}
                onClick={(e) => {
                  e.stopPropagation();
                  card.trendOnClick?.();
                }}
              >
                <span
                  onMouseEnter={(e) =>
                    (e.currentTarget.style.background = "#f0f0f0")
                  }
                  onMouseLeave={(e) =>
                    (e.currentTarget.style.background = card.accent)
                  }
                  style={{
                    display: "inline-flex",
                    alignItems: "center",
                    fontSize: "12px",
                    fontWeight: 500,
                    padding: "4px 8px",
                    borderRadius: "12px",
                    background: card.accent,
                    transition: "background-color 0.3s ease",
                  }}
                >
                  <iconify-icon icon={card.trendIcon} width="14" height="14" />
                  <span style={{ marginLeft: 4 }}>{card.trend}</span>
                </span>
              </div>
            </div>
            <div style={{ zIndex: 1 }}>
              <div
                style={{
                  fontSize: "14px",
                  fontWeight: 500,
                  marginBottom: "8px",
                  opacity: 0.9,
                }}
              >
                {card.title}
              </div>
              <div
                style={{
                  fontSize: "34px",
                  fontWeight: 700,
                  lineHeight: 1,
                }}
              >
                {card.value}
              </div>
            </div>
          </div>
        </Col>
      ))}
    </Row>
  );
}
