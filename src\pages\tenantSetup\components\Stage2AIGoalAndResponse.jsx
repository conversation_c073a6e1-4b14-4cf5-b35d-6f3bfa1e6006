import { useState } from "react";
import { 
  <PERSON>, 
  <PERSON><PERSON>, 
  Row, 
  Col, 
  Typo<PERSON>, 
  Space, 
  Badge, 
  Divider, 
  Form, 
  Input, 
  Select,
  message
} from "antd";
import { 
  CheckOutlined, 
  ArrowLeftOutlined, 
  ArrowRightOutlined,
  StarFilled,
  BulbOutlined,
  GlobalOutlined,
  ThunderboltOutlined
} from "@ant-design/icons";

const { Title, Text, Paragraph } = Typography;
const { TextArea } = Input;
const { Option } = Select;

const Stage2AIGoalAndResponse = ({ 
  staticData, 
  onSubmit, 
  onBack, 
  loading 
}) => {
  const [form] = Form.useForm();
  const [showResponses, setShowResponses] = useState(false);
  const [aiGoal, setAiGoal] = useState("");
  const [languages, setLanguages] = useState([]);
  const [selectedResponse, setSelectedResponse] = useState(null);
  const [aiResponses, setAiResponses] = useState([]);

  // Generate AI responses based on user goal
  const generateAIResponses = (goal, langs) => {
    return [
      {
        id: "simplified",
        title: "Simplified",
        description: "Clear, concise answers",
        icon: "📝",
        features: [
          "Short and direct responses",
          "Easy to understand",
          "Quick answers",
          "Minimal technical details"
        ],
        response: `For your goal "${goal.substring(0, 50)}...", I'll provide simple, clear answers that get straight to the point. Perfect for quick decisions and easy understanding.`
      },
      {
        id: "detailed",
        title: "Detailed",
        description: "Comprehensive explanations",
        icon: "📚",
        features: [
          "In-depth explanations",
          "Context and background",
          "Multiple perspectives",
          "Balanced information"
        ],
        response: `Regarding "${goal.substring(0, 50)}...", I'll provide thorough explanations with context, examples, and detailed analysis to help you make well-informed decisions.`
      },
      {
        id: "elaborated",
        title: "Elaborated",
        description: "Technical details with step-by-step guidance",
        icon: "🔧",
        features: [
          "Technical specifications",
          "Step-by-step guides",
          "Implementation details",
          "Advanced insights"
        ],
        response: `For your objective "${goal.substring(0, 50)}...", I'll provide comprehensive technical guidance with detailed implementation steps, best practices, and advanced strategies.`
      }
    ];
  };

  const handleGoalSubmit = async (values) => {
    try {
      setAiGoal(values.aiGoal);
      setLanguages(values.languages);
      
      // Generate AI responses based on the goal
      const responses = generateAIResponses(values.aiGoal, values.languages);
      setAiResponses(responses);
      setShowResponses(true);
      
      message.success("AI responses generated successfully!");
    } catch (error) {
      message.error("Failed to generate AI responses");
    }
  };

  const handleCardClick = (response) => {
    setSelectedResponse(response.id);
  };

  const handleFinalSubmit = () => {
    const selected = aiResponses.find(response => response.id === selectedResponse);
    if (selected) {
      onSubmit({
        aiGoal,
        languages,
        selectedResponse: selected
      });
    }
  };

  const getCardClassName = (responseId) => {
    let className = "ai-response-card";
    if (selectedResponse === responseId) {
      className += " ai-response-card--selected";
    }
    return className;
  };

  return (
    <div className="stage2-ai-goal-response" style={{ padding: "0 16px" }}>
      <div className="stage2-ai-goal-response__header">
        <Title level={2} style={{ marginBottom: 8, color: '#262626', textAlign: 'center' }}>
          AI Goal & Response Style
        </Title>
        <Paragraph style={{ textAlign: 'center', fontSize: '15px', color: '#8c8c8c', marginBottom: 32 }}>
          Tell us what you want your AI to do and choose your preferred response style
        </Paragraph>
      </div>

      {!showResponses ? (
        // AI Goal and Language Selection Form
        <Form
          form={form}
          layout="vertical"
          onFinish={handleGoalSubmit}
          style={{ marginBottom: 32 }}
        >
          {/* AI Goal Section */}
          <Card
            title={
              <Space>
                <BulbOutlined style={{ color: "#1890ff" }} />
                <Text strong>What do you want your AI to do?</Text>
              </Space>
            }
            className="form-section-card"
            style={{ marginBottom: 24 }}
          >
            <Form.Item
              name="aiGoal"
              label="Describe your AI goal or what you expect from the AI assistant"
              rules={[
                { required: true, message: "Please describe your AI goal" },
                { min: 20, message: "Please provide at least 20 characters" }
              ]}
            >
              <TextArea
                rows={4}
                placeholder="e.g., I want the AI to help customers with product inquiries, provide technical support, automate customer service responses, help with sales questions..."
                className="professional-textarea"
                style={{
                  resize: "vertical",
                  minHeight: "120px",
                }}
              />
            </Form.Item>
          </Card>

          {/* Language Selection */}
          <Card
            title={
              <Space>
                <GlobalOutlined style={{ color: "#52c41a" }} />
                <Text strong>Language Preferences</Text>
              </Space>
            }
            className="form-section-card"
            style={{ marginBottom: 32 }}
          >
            <Form.Item
              name="languages"
              label="Select preferred languages for AI responses"
              rules={[{ required: true, message: "Please select at least one language" }]}
            >
              <Select
                mode="multiple"
                placeholder="Select languages"
                className="professional-select"
                style={{ width: "100%" }}
              >
                <Option value="english">English</Option>
                <Option value="nepali">Nepali</Option>
                <Option value="auto">Auto-Detect</Option>
              </Select>
            </Form.Item>
          </Card>

          {/* Submit Button */}
          <div style={{ textAlign: "center" }}>
            <Space size="large">
              <Button
                icon={<ArrowLeftOutlined />}
                onClick={onBack}
                size="middle"
                style={{
                  borderRadius: "6px",
                  height: "40px",
                  paddingLeft: "20px",
                  paddingRight: "20px",
                  fontSize: "14px",
                }}
              >
                Back to Business Info
              </Button>

              <Button
                type="primary"
                htmlType="submit"
                loading={loading}
                size="middle"
                icon={<ThunderboltOutlined />}
                className="professional-button"
                style={{
                  borderRadius: "6px",
                  height: "40px",
                  paddingLeft: "24px",
                  paddingRight: "24px",
                  fontSize: "14px",
                  fontWeight: "500",
                }}
              >
                Generate AI Responses
              </Button>
            </Space>
          </div>
        </Form>
      ) : (
        // AI Response Selection
        <>
          <Divider style={{ margin: "32px 0" }}>
            <Text type="secondary">Choose Your AI Response Style</Text>
          </Divider>

          <Row gutter={[24, 24]} style={{ marginBottom: 40 }}>
            {aiResponses.map((response, index) => (
              <Col xs={24} lg={8} key={response.id}>
                <Card
                  className={getCardClassName(response.id)}
                  hoverable
                  onClick={() => handleCardClick(response)}
                  style={{
                    height: '100%',
                    borderRadius: '8px',
                    border: selectedResponse === response.id ? '2px solid #1890ff' : '1px solid #f0f0f0',
                    boxShadow: selectedResponse === response.id 
                      ? '0 4px 12px rgba(24, 144, 255, 0.15)' 
                      : '0 2px 8px rgba(0, 0, 0, 0.06)',
                    transition: 'all 0.3s ease',
                    cursor: 'pointer',
                    position: 'relative',
                  }}
                >
                  {/* Recommended Badge for middle option */}
                  {index === 1 && (
                    <Badge.Ribbon
                      text={
                        <Space size={4}>
                          <StarFilled />
                          <span>Recommended</span>
                        </Space>
                      }
                      color="gold"
                      style={{ top: 16, right: -8 }}
                    />
                  )}

                  {/* Selection Indicator */}
                  {selectedResponse === response.id && (
                    <div
                      style={{
                        position: 'absolute',
                        top: 16,
                        left: 16,
                        width: 24,
                        height: 24,
                        borderRadius: '50%',
                        backgroundColor: '#1890ff',
                        display: 'flex',
                        alignItems: 'center',
                        justifyContent: 'center',
                        zIndex: 1,
                      }}
                    >
                      <CheckOutlined style={{ color: 'white', fontSize: 12 }} />
                    </div>
                  )}

                  <div style={{ padding: '20px' }}>
                    {/* Header */}
                    <div style={{ textAlign: 'center', marginBottom: 16 }}>
                      <div style={{ fontSize: '28px', marginBottom: 8 }}>
                        {response.icon}
                      </div>
                      <Title level={4} style={{ marginBottom: 4, color: '#262626' }}>
                        {response.title}
                      </Title>
                      <Text type="secondary" style={{ fontSize: 13 }}>
                        {response.description}
                      </Text>
                    </div>

                    <Divider style={{ margin: '12px 0' }} />

                    {/* Features */}
                    <div style={{ marginBottom: 16 }}>
                      <Text strong style={{ color: '#595959', fontSize: 13, display: 'block', marginBottom: 8 }}>
                        Key Features:
                      </Text>
                      <Space direction="vertical" size={4} style={{ width: '100%' }}>
                        {response.features.map((feature, idx) => (
                          <Space key={idx} size={6}>
                            <CheckOutlined style={{ color: '#52c41a', fontSize: 10 }} />
                            <Text style={{ fontSize: 12, color: '#595959' }}>
                              {feature}
                            </Text>
                          </Space>
                        ))}
                      </Space>
                    </div>

                    {/* Sample Response */}
                    <div style={{ marginBottom: 16 }}>
                      <Text strong style={{ color: '#595959', fontSize: 13, display: 'block', marginBottom: 8 }}>
                        Sample Response:
                      </Text>
                      <div 
                        style={{ 
                          background: '#fafafa', 
                          padding: '12px', 
                          borderRadius: '6px',
                          border: '1px solid #f0f0f0',
                          maxHeight: '100px',
                          overflowY: 'auto'
                        }}
                      >
                        <Text style={{ fontSize: 12, color: '#595959', lineHeight: 1.4 }}>
                          {response.response}
                        </Text>
                      </div>
                    </div>

                    {/* Select Button */}
                    <Button
                      type={selectedResponse === response.id ? "primary" : "default"}
                      block
                      size="small"
                      style={{
                        borderRadius: '6px',
                        height: '36px',
                        fontWeight: '500',
                        fontSize: '13px',
                      }}
                      onClick={(e) => {
                        e.stopPropagation();
                        handleCardClick(response);
                      }}
                    >
                      {selectedResponse === response.id ? "Selected" : "Select This Style"}
                    </Button>
                  </div>
                </Card>
              </Col>
            ))}
          </Row>

          {/* Navigation Buttons */}
          <div style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
            <Button
              icon={<ArrowLeftOutlined />}
              onClick={() => setShowResponses(false)}
              size="middle"
              style={{
                borderRadius: '6px',
                height: '40px',
                paddingLeft: '20px',
                paddingRight: '20px',
                fontSize: '14px'
              }}
            >
              Back to AI Goal
            </Button>

            <Button
              type="primary"
              icon={<ArrowRightOutlined />}
              onClick={handleFinalSubmit}
              disabled={!selectedResponse}
              loading={loading}
              size="middle"
              style={{
                borderRadius: '6px',
                height: '40px',
                paddingLeft: '24px',
                paddingRight: '24px',
                fontSize: '14px',
                fontWeight: '500',
              }}
            >
              Continue to Final Step
            </Button>
          </div>

          {/* Help Text */}
          {selectedResponse && (
            <div style={{ marginTop: 20, textAlign: 'center' }}>
              <Text type="secondary" style={{ fontSize: 13 }}>
                You can always change your response style later in the settings.
              </Text>
            </div>
          )}
        </>
      )}
    </div>
  );
};

export default Stage2AIGoalAndResponse;
