import React, { useEffect, useState } from "react";
import { Layout, Drawer } from "antd";
import CustomerList from "../../components/CustomerList/CustomerList";
import ChatWindow from "../../components/ChatWindow/ChatWindow";
import useConversations from "../../hooks/useConversations";
import MetadataSection from "../playground/sections/MetaDataSection";
import { useNavigate, useLocation } from "react-router-dom";

const { Content } = Layout;

const ChannelsComponent = ({
  selectedCustomerId,
  chatData,
  setChatData,
  customerMetaData,
  handleCustomerClick,
  messageText,
  setMessageText,
  handleSendMessage,
  messagesEndRef,
  content,
  setContent,
  chatWindowLoading,
  // isVerified,
  // setIsVerified,
  firstCustomerId,
  setFirstCustomerId,
  setSelectedCustomerId,
  scrollToBottom,
}) => {
  const [collapsed, setCollapsed] = useState(true);
  const [profile, setProfile] = useState("facebook");
  const [dateRange, setDateRange] = useState([]);
  const [searchTerm, setSearchTerm] = useState("");
  const [selectedCustomer, setSelectedCustomer] = useState(null);

  const [customerInfo, setCustomerInfo] = useState();

  const [refreshCustomerList, setRefreshCustomerList] = useState(false);

  // ...   DRAWER
  const [visible, setVisible] = useState(false);
  const onClose = () => {
    setVisible(false);
  };
  const [drawerData, setDrawerData] = useState();

  const collapsedWidth = collapsed ? 80 : 150; // Adjust as needed

  const handleProfileChange = (value) => {
    setProfile(value);
    setSelectedCustomer(null); // Reset selected customer when channel changes
  };

  const handleDateChange = (dates) => {
    setDateRange(dates);
  };

  const handleSearch = (value) => {
    setSearchTerm(value);
  };

  // FilterModel ma implement vako le ya bata hatai deko
  
  // const navigate = useNavigate();
  // const location = useLocation();
  
  // useEffect(() => {
  //   if (location.state?.phoneID || location.state?.topic) {
  //     navigate(location.pathname, { replace: true });
  //   }
  // }, []);

  const { conversations } = useConversations(
    profile,
    dateRange,
    searchTerm,
    "asc", // Sort option
    true // useDummyData
  );

  return (
    <Layout
      style={{
        height: "90vh",
        overflow: "hidden",
        //  border: "solid 1px black"
      }}
    >
      <CustomerList
        setCustomerInfo={setCustomerInfo}
        selectedCustomerId={selectedCustomerId}
        handleCustomerClick={handleCustomerClick}
        conversations={conversations}
        profile={profile}
        dateRange={dateRange}
        searchTerm={searchTerm}
        handleProfileChange={handleProfileChange}
        handleDateChange={handleDateChange}
        handleSearch={handleSearch}
        selectedCustomer={selectedCustomer}
        setSelectedCustomer={setSelectedCustomer}
        // setIsVerified={setIsVerified}
        firstCustomerId={firstCustomerId}
        setFirstCustomerId={setFirstCustomerId}
        setSelectedCustomerId={setSelectedCustomerId}
        refreshCustomerList={refreshCustomerList}
      />
      <ChatWindow
        customerInfo={customerInfo}
        setVisible={setVisible}
        setDrawerData={setDrawerData}
        chatData={chatData}
        setChatData={setChatData}
        customerMetaData={customerMetaData}
        selectedCustomerId={selectedCustomerId}
        messageText={messageText}
        setMessageText={setMessageText}
        handleSendMessage={handleSendMessage}
        messagesEndRef={messagesEndRef}
        content={content}
        setContent={setContent}
        chatWindowLoading={chatWindowLoading}
        // isVerified={isVerified}
        scrollToBottom={scrollToBottom}
        setRefreshCustomerList={setRefreshCustomerList}
      />
      <Drawer
        visible={visible}
        onClose={onClose}
        title={<div>Gathered Information</div>}
        placement="right"
        width={480}
      >
        <MetadataSection drawerData={drawerData} />
      </Drawer>
    </Layout>
  );
};

export default ChannelsComponent;
