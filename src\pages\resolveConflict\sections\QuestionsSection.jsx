import {
  But<PERSON>,
  Card,
  List,
  Typography,
  message,
  Tag,
  Select,
  Form,
  Divider,
  Space,
} from "antd";
import React, { useEffect, useState } from "react";
import SelectOption from "../../../components/common/selectOption";
import {
  getAllQuestionsApi,
  getAllSectionsApi,
  generateConflictsApi,
  regenerateQuestionAPI,
  getAnswerByIdApi,
  getAllFiltersApi,
} from "../../../services/resolveConflict.service";
import { useAppDispatch } from "../../../hooks/reduxHooks";
import {
  BookOutlined,
  BranchesOutlined,
  CheckCircleOutlined,
  CheckOutlined,
  CloseCircleOutlined,
  ExclamationCircleOutlined,
  FileTextOutlined,
  LoadingOutlined,
  RedoOutlined,
  RightOutlined,
  SettingOutlined,
  WarningOutlined,
} from "@ant-design/icons";
import DataTablePagination from "../../../components/common/dataTablePagination";
import FilterMultiSelect from "../../../components/common/FilterMultiSelect";

const QuestionsSection = ({
  questionId,
  setQuestionId,
  setQuestion,
  setSources,
  setAnswer,
}) => {
  const dispatch = useAppDispatch();

  const { Text } = Typography;

  // Loading states
  const [rerunLoading, setRerunLoading] = useState({});
  const [regenerateLoading, setRegenerateLoading] = useState(false);

  const [questionsList, setQuestionsList] = useState();
  const [sections, setSections] = useState([]);
  const [sectionName, setSectionName] = useState();
  const [sectionGenerated, setSectionGenerated] = useState();
  const [sectionFilters, setSectionFilters] = useState([]);

  // Filter Select Options
  const [selectedOption, setSelectedOption] = useState([]);

  // Handle change in selection
  const handleChange = (value) => {
    // Get the corresponding label for the selected value
    setSelectedOption(value);
  };

  // Pagination
  const [totalQuestions, setTotalQuestions] = useState();
  const [currentPage, setCurrentPage] = useState();
  const [pageNumber, setPageNumber] = useState(1);

  const sectionOnChange = (value) => {
    const selectedSection = sections.find((section) => section.value === value);
    setSectionName(value);
    setSectionGenerated(selectedSection?.generated);
  };

  const fetchAllQuestions = () => {
    dispatch(
      getAllQuestionsApi({
        data: sectionName,
        params: {
          relation: selectedOption,
          page: pageNumber,
          page_size: "10",
        },
        finalCallback: () => {},
        successCallback: (response) => {
          setQuestion(response?.data?.[0]?.question?.question);
          setQuestionId(response?.data?.[0]?.question?.question_id);
          setQuestionsList(response?.data);
          setTotalQuestions(response?.meta?.total_count);
          setCurrentPage(response?.meta?.page);
        },
        failureCallback: () => {},
      })
    );
  };

  const generateAnalysis = () => {
    setRegenerateLoading(true);
    message.warning("Section has been sent for re-regeneration!");
    const webSocket = new WebSocket(
      `${
        process.env.REACT_APP_WS_BASE_URL
      }/qdrant/generate_questions/${sectionName}?token=${localStorage.getItem(
        "authToken"
      )}`
    );

    webSocket.onopen = () => {
      console.log("WebSocket connected");
      webSocket.send(JSON.stringify({ type: "resolve_conflict" }));
    };

    // Dispatch API call asynchronously (without waiting)
    dispatch(
      generateConflictsApi({
        data: sectionName,
        params: {
          mode: "simple",
        },
        finalCallback: () => {
          setRegenerateLoading(false);
        },
        successCallback: (response) => {
          fetchAllQuestions();
        },
        failureCallback: (error) => {
          console.error("API Error ->", error);
        },
      })
    );

    // WebSocket message handler
    webSocket.onmessage = (event) => {
      console.log("Received message:", event.data);
      try {
        const status = JSON.parse(event.data);
        console.log("Status update:", status);

        const m_status = status?.status;
        if (m_status === "error") {
          message.error(`Status update: ${status?.message}`);
        } else if (m_status === "loading") {
          message.loading(`Status update: ${status?.message}`);
        } else if (m_status === "success") {
          message.success(`Status update: ${status?.message}`);
        }
      } catch (error) {
        message.error("Error parsing WebSocket message: " + error.message);
      }
    };

    // WebSocket error handling
    webSocket.onerror = (error) => {
      message.error(`WebSocket error: ${error}`);
    };

    // WebSocket close handling
    webSocket.onclose = () => {
      message.info("WebSocket connection closed");
    };

    // Cleanup function to close WebSocket when the component unmounts
    return () => {
      if (
        webSocket.readyState === WebSocket.OPEN ||
        webSocket.readyState === WebSocket.CONNECTING
      ) {
        webSocket.close();
      }
    };
  };

  const handleRegenerateQna = (questionId) => {
    setRerunLoading((prev) => ({ ...prev, [questionId]: true }));
    message.warning("Question has been sent for simulation!");
    dispatch(
      regenerateQuestionAPI({
        params: questionId,
        finalCallback: () => {
          setRerunLoading((prev) => ({ ...prev, [questionId]: false }));
        },
        successCallback: (response) => {
          message.success("Regenerated successfully!");
          dispatch(
            getAnswerByIdApi({
              id: questionId,
              finalCallback: () => {},
              successCallback: (response) => {
                setAnswer(response?.answer);
                setSources(response?.source_nodes);
              },
              failureCallback: () => {},
            })
          );
        },
        failureCallback: () => {
          message.error("Failed to regenerate questions.");
        },
      })
    );
  };

  // const fetchAllFilters = () => {
  //   dispatch(
  //     getAllFiltersApi({
  //       finalCallback: () => {},
  //       successCallback: (response) => {
  //         const list = response?.map((item) => {
  //           return { label: item, value: item };
  //         });
  //         setSectionFilters(list);
  //       },
  //       failureCallback: () => {},
  //     })
  //   );
  // };

  const fetchAllSections = () => {
    dispatch(
      getAllSectionsApi({
        finalCallback: () => {},
        successCallback: (response) => {
          console.log("RESPONSE fetchAllSections -> ", response);
          const list = response?.map((item) => {
            return {
              id: item?._id,
              label: item?.section_name,
              value: item?.section_name,
              generated: item?.generated,
              resolved: item?.resloved,
              status: item?.status,
              unresolved_count: item?.unresolved_count,
            };
          });
          setSectionName(list[0]?.label);
          setSectionGenerated(list[0]?.generated);

          setSections(list);
        },
        failureCallback: () => {},
      })
    );
  };

  useEffect(() => {
    fetchAllSections();
  }, []);

  useEffect(() => {
    if (sectionName) {
      fetchAllQuestions();
    }
  }, [sectionName, pageNumber, selectedOption]);

  const getIcon = (section) => {
    if (section.status === "resolved" || section.status === "no-issues") {
      return (
        <Tag style={{ paddingBottom: "2px" }} color="success">
          {section.status}
        </Tag>
      );
    } else if (
      section.status === "pending" ||
      section.status === "in-progress"
    ) {
      return <Tag style={{ paddingBottom: "2px" }}>{section.status}</Tag>;
    } else if (section.status === "issues") {
      return (
        <Tag style={{ paddingBottom: "2px" }} color="error">
          {`${section.status} (${section.unresolved_count})`}
        </Tag>
      );
    }
  };

  // Inside JSX:

  return (
    <Card
      title={
        <div
          style={{
            marginTop: "28px",
            marginBottom: "28px",
          }}
        >
          <div
            style={{
              display: "flex",
              gap: "24px",
              marginBottom: "12px",
            }}
          >
            <div style={{ flex: 1 }}>
              <FilterMultiSelect
                options={[
                  { label: "Conflict", value: "conflict" },
                  { label: "Duplicate", value: "duplicate" },
                ]}
                handleChange={handleChange}
              />
            </div>

            <Button
              type="primary"
              onClick={() => generateAnalysis()}
              // icon={<SettingOutlined />}
              style={{
                display: "flex",
                alignItems: "center",
                gap: "8px",
              }}
              loading={regenerateLoading}
            >
              {sectionGenerated ? "Regenerate" : "Generate"}
            </Button>
          </div>

          <Select
            value={sectionName}
            placeholder="Select section"
            style={{ width: "100%" }}
            onChange={sectionOnChange}
          >
            {sections.map((section) => (
              <Select.Option key={section.value} value={section.value}>
                <div
                  style={{
                    display: "flex",
                    justifyContent: "space-between",
                    alignItems: "center",
                  }}
                >
                  <span
                    style={{
                      fontWeight: "500",
                    }}
                  >
                    {/* {section.label} */}
                    {section.label.length > 30
                      ? section.label.substring(0, 30) + "..."
                      : section.label}
                  </span>

                  {getIcon(section)}
                </div>
              </Select.Option>
            ))}
          </Select>
        </div>
      }
      style={{
        height: "90vh",
        display: "flex",
        flexDirection: "column",
        overflow: "auto",
        scrollbarWidth: "thin",
        position: "sticky",
      }}
      styles={{
        body: {
          height: "100%",

          padding: 14,
        },
      }}
    >
      <div
        style={{
          height: "100%",
          display: "flex",
          flexDirection: "column",
        }}
      >
        <div
          style={{
            height: "100%",
            overflowY: "auto",
            paddingRight: "10px",
          }}
        >
          <List
            itemLayout="horizontal"
            dataSource={questionsList}
            style={{ height: "40vh" }}
            locale={{
              emptyText: (
                <div
                  style={{
                    textAlign: "center",
                    padding: "20px",
                    height: "100%",
                  }}
                >
                  <img
                    src="https://cdn-icons-png.flaticon.com/512/4076/4076432.png" // Example empty state icon
                    alt="No Data"
                    style={{ width: "100px", marginBottom: "10px" }}
                  />
                  <p style={{ color: "#999", fontSize: "16px" }}>
                    No questions available <br></br>Please Run Conflict
                    Identification.
                  </p>
                </div>
              ),
            }}
            renderItem={(item) => {
              const isSelected = item?.question?.question_id === questionId;

              return (
                <List.Item
                  actions={[
                    <div
                      style={{
                        display: "flex",
                        flexDirection: "column",
                        justifyContent: "center",
                        alignItems: "center",
                      }}
                    >
                      <Button
                        onClick={() =>
                          handleRegenerateQna(item?.question?.question_id)
                        }
                        icon={<RedoOutlined />}
                        loading={
                          rerunLoading[item?.question?.question_id] || false
                        }
                      >
                        Re-Run
                      </Button>
                    </div>,
                  ]}
                  style={{
                    padding: "15px",
                    borderBottom: "1px solid #f0f0f0",
                    transition: "background 0.3s ease",
                    cursor: "pointer",
                    background: isSelected ? "#e6f7ff" : "transparent",
                  }}
                  onMouseEnter={(e) => {
                    if (!isSelected)
                      e.currentTarget.style.background = "#f5f5f5";
                  }}
                  onMouseLeave={(e) => {
                    if (!isSelected)
                      e.currentTarget.style.background = "transparent";
                  }}
                  onClick={() => {
                    setQuestionId(item?.question?.question_id);
                    setQuestion(item?.question?.question);
                  }}
                >
                  <List.Item.Meta
                    title={<Text strong>{item?.question?.question}</Text>}
                    description={
                      <>
                        {item?.question?.metadata?.relation && (
                          <Tag>{item?.question?.metadata?.relation}</Tag>
                        )}
                        {item?.question?.metadata.conflict_reason && (
                          <div
                            style={{
                              display: "-webkit-box",
                              WebkitBoxOrient: "vertical",
                              WebkitLineClamp: 2, // Truncate after 2 lines
                              overflow: "hidden",
                              textOverflow: "ellipsis",
                              whiteSpace: "normal",
                              marginTop: "5px",
                            }}
                          >
                            <Text
                              style={{
                                color: "rgba(239, 32, 32, 0.82)",
                              }}
                            >
                              <WarningOutlined
                                style={{
                                  marginRight: 5,
                                  color: "rgb(250, 20, 20)",
                                }}
                              />{" "}
                              Reason of conflict:{" "}
                              {item?.question?.metadata.conflict_reason}
                            </Text>
                          </div>
                        )}
                      </>
                    }
                  />
                </List.Item>
              );
            }}
          />
        </div>
        {/* Pagination */}
        <DataTablePagination
          totalDocuments={totalQuestions}
          currentPage={currentPage}
          setPageNumber={setPageNumber}
        />
      </div>
    </Card>
  );
};

export default QuestionsSection;
