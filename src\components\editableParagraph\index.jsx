// import React, { useState } from "react";
// import { Typo<PERSON>, Button } from "antd";

// const { Paragraph } = Typography;

// const EditableParagraph = ({ content, setContent, setIsParagraphEdited }) => {
//   const [initialContent, setInitialContent] = useState(content); // Keeps the original content for comparison

//   const handleContentChange = (newValue) => {
//     setContent(newValue);

//     // Check if the content is different from the original
//     setIsParagraphEdited(newValue !== initialContent);
//   };

//   return (
//     <div>
//       <Paragraph
//         editable={{
//           onChange: handleContentChange, // Triggered on content change
//           triggerType: ["text"], // Editing starts by clicking text
//           active: true,
//         }}
//         style={{
//           padding: "10px",
//         }}
//       >
//         {content}
//       </Paragraph>
//       <div></div>
//     </div>
//   );
// };

// export default EditableParagraph;

import React from "react";

const EditableParagraph = ({
  textToBeVerified,
  setTextToBeVerified,
  setIsParagraphEdited,
  initialContent,
}) => {
  const handleBlur = (event) => {
    setTextToBeVerified(event.target.textContent);

    // Check if the content is different from the original
    setIsParagraphEdited(event.target.textContent !== initialContent);
  };

  return (
    <div
      contentEditable
      suppressContentEditableWarning
      onBlur={handleBlur}
      style={{
        border: "1px solid #ccc",
        padding: "8px",
        borderRadius: "4px",
        cursor: "text",
        // ...
        maxHeight: "475px",
        overflowY: "auto",
      }}
    >
      {textToBeVerified}
    </div>
  );
};

export default EditableParagraph;
