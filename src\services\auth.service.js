import { message } from "antd";
import { L<PERSON><PERSON>N, VERIFY_TOKEN } from "../constants/url";
import httpBase from "../utils/http.utils";
import { storeAuthToken, storeUserData } from "../store/slices/profile.slice";
import routes from "../configs/routes.config";

export const loginApi = ({ data, finalCallback, successCallback }) => {
  return async (dispatch) => {
    try {
      const response = await httpBase().post(LOGIN, new URLSearchParams(data));

      const nav_permissions = response?.data?.nav_permission;

      // filter the nav_permissions array to remove false values for routing
      // Filter the routes array to include only the ones that have a true permission in nav_permissions

      const allowedRouteNames = routes
        .filter((route) => nav_permissions?.[route.name] === true)
        .map((route) => route.name);

      if (response?.data?.access_token) {
        dispatch(storeAuthToken(response?.data?.access_token));
        dispatch(
          storeUserData({
            id: response?.data?.id,
            username: response?.data?.username,
            role: response?.data?.role,
            tenant_id: response?.data?.tenant_id,
            tenant_label: response?.data?.tenant_label,
          })
        );

        localStorage.setItem("userId", response?.data?.id);
        localStorage.setItem("username", response?.data?.username);
        localStorage.setItem("role", response?.data?.role);
        localStorage.setItem("tenant_label", response?.data?.tenant_label);
        localStorage.setItem("slug", response?.data?.tenant_slug);
        localStorage.setItem(
          "nav_permissions",
          JSON.stringify(allowedRouteNames)
        );
        // const tenantSlug = (response?.data?.tenant_label || "")
        //   .split(" ")
        //   .join("");
        // localStorage.setItem("slug", tenantSlug);

        // ...
        successCallback(response);
        message.success(`Welcome ${response?.data?.username}`);
      } else {
        // message.error("Something went wrong with the login!");
        const errorMessage =
          response?.data?.detail || "Something went wrong with the login";
        message.error(errorMessage);
      }
    } catch (e) {
      message.error(
        e?.response?.data?.detail || "Something went wrong with the login"
      );
    } finally {
      finalCallback();
    }
  };
};

export const verifyTokenApi = ({
  finalCallback,
  successCallback,
  failureCallback,
}) => {
  return async (dispatch) => {
    try {
      const response = await httpBase().get(VERIFY_TOKEN);
      successCallback(response?.data);
    } catch (e) {
      failureCallback(e);
    } finally {
      finalCallback();
    }
  };
};
