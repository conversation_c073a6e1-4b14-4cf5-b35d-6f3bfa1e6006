import React, { useEffect, useRef } from "react";
import * as echarts from "echarts";

export default function HumanInterventionTrend({ data }) {
  const chartRef = useRef(null);

  const newData = data.map(item => {
    const date = item._id;
    const count = item.message_count === 0 ? 0 : item.cta_count / item.message_count;
    return {
      date: date,
      average: count,
      cta_count: item.cta_count,
      message_count: item.message_count
    };
  });

  useEffect(() => {
    if (!chartRef.current) return;

    const chart = echarts.init(chartRef.current);

    const dates = newData.map(item => item.date);
    const averages = newData.map(item => item.average * 100);

    const option = {
      title: {
        text: "Human Intervention Trend",
        left: "center",
        textStyle: { 
          fontSize: 16, 
          fontWeight: "600", 
          color: "#0D47A1",
          fontFamily: "'Inter', sans-serif"
        },
      },
      tooltip: {
        trigger: "axis",
        formatter: (params) => {
          const index = params[0].dataIndex;
          const item = newData[index];
          const date = new Date(item.date);
          const formattedDate = date.toLocaleDateString('en-US', { 
            month: 'short', 
            day: 'numeric',
            year: 'numeric'
          });
          const value = item.average * 100;
          return `
            <div style="font-weight:500; margin-bottom:5px">${formattedDate}</div>
            <div>${item.cta_count} Interventions in ${item.message_count} messages</div>
            <div>Intervention Rate: <strong>${value.toFixed(1)}%</strong></div>
          `;
        },
        backgroundColor: "#ffffff",
        borderColor: "#e2e8f0",
        borderWidth: 1,
        padding: [8, 12]
      },
      grid: {
        left: "3%",
        right: "3%",
        top: "20%",
        bottom: "15%",
        containLabel: true
      },
      xAxis: {
        type: "category",
        data: dates,
        axisLabel: {
          color: "#64748B",
          fontSize: 11,
          formatter: (value) => {
            const date = new Date(value);
            return isNaN(date) ? value : 
              date.toLocaleDateString('en-US', { day: 'numeric' });
          }
        },
        axisLine: {
          lineStyle: {
            color: "#E2E8F0"
          }
        },
        axisTick: {
          alignWithLabel: true,
          lineStyle: {
            color: "#E2E8F0"
          }
        },
      },
      yAxis: {
        type: "value",
        name: "Intervention Rate",
        nameTextStyle: {
          color: "#64748B",
          padding: [0, 0, 0, 40]
        },
        axisLabel: {
          color: "#64748B",
          formatter: "{value}%"
        },
        splitLine: {
          lineStyle: {
            color: "#E2E8F0",
            type: "dashed"
          }
        },
        min: 0,
        max: 100,
        interval: 20
      },
      series: [
        {
          name: "Intervention Rate",
          type: "line",
          data: averages,
          symbol: "circle",
          symbolSize: 6,
          itemStyle: {
            color: "#3B82F6"
          },
          lineStyle: {
            width: 3,
            color: "#3B82F6"
          },
          areaStyle: {
            color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [
              { offset: 0, color: "rgba(59, 130, 246, 0.3)" },
              { offset: 1, color: "rgba(59, 130, 246, 0)" }
            ])
          },
          emphasis: {
            itemStyle: {
              color: "#2563EB",
              borderColor: "#fff",
              borderWidth: 2
            },
            scale: 1.5
          }
        }
      ],
      visualMap: {
        type: 'piecewise',
        show: false,
        dimension: 0,
        seriesIndex: 0,
        pieces: [{
          gt: dates.length - 7,
          lt: dates.length,
          color: '#10B981'
        }]
      }
    };

    chart.setOption(option);

    const handleResize = () => chart.resize();
    window.addEventListener("resize", handleResize);

    return () => {
      chart.dispose();
      window.removeEventListener("resize", handleResize);
    };
  }, [data]);

  return (
    <div style={{
      width: "100%",
      background: "#fff",
      borderRadius: "12px",
      boxShadow: "0 1px 3px rgba(0, 0, 0, 0.05)",
      padding: "16px",
      marginTop: "24px"
    }}>
      <div
        ref={chartRef}
        style={{ width: "100%", height: "400px" }}
      />
    </div>
  );
};