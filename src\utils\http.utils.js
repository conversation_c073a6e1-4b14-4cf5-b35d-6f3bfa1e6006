import axios from "axios";
import { message } from "antd";

// Flag to prevent multiple 401 error messages
let isHandling401Error = false;

const httpBase = (api = true) => {
  let authToken = localStorage.getItem("authToken");
  let slug = localStorage.getItem("slug");

  const instance = axios.create({
    baseURL: `${process.env.REACT_APP_API_BASE_URL}`,
  });

  instance.interceptors.request.use(
    (config) => {
      if (authToken) {
        if (config.headers)
          config.headers.Authorization = `Bearer ${authToken}`;
      }
      return config;
    },
    (error) => {
      // Handle request errors here
      return Promise.reject(error);
    }
  );

  instance.interceptors.response.use(
    (response) => {
      return response;
    },
    async (error) => {
      const originalConfig = error.config;
      console.log("Error in second instance.interceptors.response -> ", error);

      if (
        error.response.data.detail === "Could not validate credentials" &&
        !originalConfig._retry
      ) {
        originalConfig._retry = true;

        // Prevent multiple 401 error handling
        if (!isHandling401Error) {
          isHandling401Error = true;

          // Clear user session
          localStorage.removeItem("authToken");
          localStorage.removeItem("userId");
          localStorage.removeItem("username");
          localStorage.removeItem("role");
          localStorage.removeItem("tenant_label");
          localStorage.removeItem("nav_permissions");

          // Display message to user and delay the redirect
          message.error("Your session has expired. Please log in again.", 2.5);

          // Delay the redirect to allow time for the message to be seen
          setTimeout(() => {
            window.location.href = `/${slug}/login`;
            // Reset the flag after redirection (although page will reload)
            isHandling401Error = false;
          }, 2000); // 2 seconds delay
        }
      } else if (error?.response?.status === 500) {
        // Handle server errors (500) if needed
        message.error("Server error. Please try again later.", 3);
      } else if (error?.response?.status === 403) {
        // Handle forbidden errors (403) if needed
        message.error("You don't have permission to access this resource.", 3);
      } else if (error?.response?.data?.message) {
        // Handle other error messages from the API
        message.error(error.response.data.message, 3);
      }

      return Promise.reject(error);
    }
  );

  return instance;
};

export const handleApiExceptions = (errors) => {
  console.log("caught api exception", errors);

  // Don't show duplicate messages for errors already handled in interceptor
  if (
    errors?.response?.status === 401 ||
    errors?.response?.status === 500 ||
    errors?.response?.status === 403
  ) {
    return;
  }

  // Show error message for other exceptions
  if (errors?.response?.data?.message) {
    message.error(errors.response.data.message, 3);
  } else if (errors?.message) {
    message.error(errors.message, 3);
  }
};

export default httpBase;
