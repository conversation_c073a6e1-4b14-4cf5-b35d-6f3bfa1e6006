import React, { useState, useEffect, useRef } from "react";
import { useAppDispatch } from "../../hooks/reduxHooks.js";
import ChannelsComponent from "./channels.component.jsx";
import {
  getCustomerMessagesById,
  getCustomersDataApi,
  sendMessageApi,
} from "../../services/channels.service.js";
import { currentDate } from "../../helpers/currentDate.js";
import { message } from "antd";

const ChannelsContainer = () => {
  const dispatch = useAppDispatch();

  // Websocket URL
  // const wsUrl = "wss://172.16.16.54:8407/ws";
  const wsUrl = process.env.REACT_APP_WS_BASE_URL;

  // SelectedCustomer
  const [selectedCustomerId, setSelectedCustomerId] = useState();

  // Chat Data of individual customer
  const [chatData, setChatData] = useState([]);

  // Chat Data of individual customer
  const [inputMessageData, setInputMessageData] = useState();

  // Detailed information of individual customer
  const [customerMetaData, setCustomerMetaData] = useState({});

  // Message sent by assistent in input box
  const [messageText, setMessageText] = useState("");

  // CTA Modal Paragraph Content
  const [content, setContent] = useState();

  const [chatWindowLoading, setChatWindowLoading] = useState(false);

  // const [isVerified, setIsVerified] = useState();

  // First Customer's Id
  const [firstCustomerId, setFirstCustomerId] = useState(null);

  // Chat window chat messages scroll to buttom
  const messagesEndRef = useRef(null);
  const scrollToBottom = () => {
    if (messagesEndRef.current) {
      messagesEndRef.current.scrollIntoView({ behavior: "smooth" });
    }
  };

  // HandleCustomerClick
  const handleCustomerClick = (id) => {
    setSelectedCustomerId(id);
  };

  useEffect(() => {
    // Create the WebSocket
    const ws = new WebSocket(wsUrl);

    // Handle incoming messages
    ws.onmessage = (event) => {
      const newMessage = event.data;
      message.success(newMessage);
      // setMessages((prevMessages) => [...prevMessages, newMessage]);
    };

    // Handle WebSocket errors
    ws.onerror = (error) => {
      console.error("WebSocket error:", error);
    };

    // Handle WebSocket disconnection
    ws.onclose = () => {
      console.log("WebSocket connection closed");
    };

    // Cleanup WebSocket on component unmount
    return () => {
      ws.close();
    };
  }, [wsUrl]); // Reconnect if wsUrl changes

  return (
    <ChannelsComponent
      chatData={chatData}
      setChatData={setChatData}
      customerMetaData={customerMetaData}
      selectedCustomerId={selectedCustomerId}
      handleCustomerClick={handleCustomerClick}
      setMessageText={setMessageText}
      messageText={messageText}
      messagesEndRef={messagesEndRef}
      content={content}
      setContent={setContent}
      chatWindowLoading={chatWindowLoading}
      // isVerified={isVerified}
      // setIsVerified={setIsVerified}
      firstCustomerId={firstCustomerId}
      setFirstCustomerId={setFirstCustomerId}
      setSelectedCustomerId={setSelectedCustomerId}
      scrollToBottom={scrollToBottom}
    />
  );
};

export default ChannelsContainer;
