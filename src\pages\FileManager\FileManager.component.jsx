import React, { useEffect, useState } from "react";
import ExistingFiles from "./ExistingFile/ExistingFile.component";
import FileUploadPanel from "./UploadFile/UploadFile.component";
import { <PERSON><PERSON>, Card, message, Typography } from "antd";
import httpBase from "../../utils/http.utils";
import { useNavigate } from "react-router-dom";
import { FolderOutlined } from "@ant-design/icons";
import { useAppDispatch } from "../../hooks/reduxHooks";
import { logout } from "../../store/slices/profile.slice";
const { Title } = Typography;

const FileManagementComponent = () => {
  const [existingFiles, setExistingFiles] = useState([]);
  const [page, setPage] = useState(1);
  const [totalFiles, setTotalFiles] = useState(0);
  const [loading, setLoading] = useState(false);
  const [limit] = useState(5);
  const navigate = useNavigate();
  const dispatch = useAppDispatch();

  const [showExistingFiles, setShowExistingFiles] = useState(false);

  useEffect(() => {
    fetchExistingFiles(page);
  }, [page]);

  const fetchExistingFiles = async (currentPage) => {
    setLoading(true);
    try {
      const response = await httpBase().get(`list_docs`, {
        params: {
          prefix: "Files",
          page: currentPage,
          limit: limit,
        },
      });

      if (response.status === 200) {
        setExistingFiles(response.data.files);
        setTotalFiles(response.data.total_files);
        setShowExistingFiles(true);
      }
    } catch (error) {
      console.error("Error fetching files:", error);
    } finally {
      setLoading(false);
    }
  };

  return (
    <div
      style={{
        display: "flex",
        gap: "20px",
        maxHeight: "50vh",
      }}
    >
      <div
        style={{
          display: "flex",
          justifyContent: "center",
          background: "#fff",
          borderRadius: "8px",
        }}
      >
        {showExistingFiles && existingFiles.length > 0 ? (
          <div
            style={{
              minWidth: "30vw",
              display: "flex",
              justifyContent: "center",
            }}
          >
            <ExistingFiles
              existingFiles={existingFiles}
              page={page}
              loading={loading}
              limit={limit}
              totalFiles={totalFiles}
              setPage={setPage}
              fetchExistingFiles={fetchExistingFiles}
            />
          </div>
        ) : null}
      </div>
      <div
        style={{
          flex: 1,
        }}
      >
        <Card
          style={{
            height: "100%",
          }}
        >
          <FileUploadPanel />
        </Card>
      </div>
    </div>
  );
};

export default FileManagementComponent;
