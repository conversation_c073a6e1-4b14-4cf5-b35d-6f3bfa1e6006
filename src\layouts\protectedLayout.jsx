import React, { useState } from "react";
import { But<PERSON>, FloatButton, Layout, ConfigProvider } from "antd";
import { Navigate, useNavigate } from "react-router-dom";
import SideNav from "../components/SideNav/SideNav";
import AppHeader from "../components/Header/Header";
import { FolderOutlined } from "@ant-design/icons";
import { useAppDispatch } from "../hooks/reduxHooks";
import { verifyTokenApi } from "../services/auth.service";
import { logout } from "../store/slices/profile.slice";

const ProtectedLayout = (props) => {
  const { Content } = Layout;
  const navigate = useNavigate();
  const dispatch = useAppDispatch();

  const [collapsed, setCollapsed] = useState(true);
  const collapsedWidth = collapsed ? 80 : 150; // Adjust these values as per your Sider settings

  const { children } = props;
  const token = localStorage.getItem("authToken");
  const isAuthenticated = !!token;
  const tenant_label = localStorage.getItem("tenant_label");
  const tenantSetupCompleted = localStorage.getItem("tenantSetupCompleted");

  dispatch(
    verifyTokenApi({
      finalCallback: () => {},
      successCallback: () => {},
      failureCallback: (error) => {},
    })
  );

  if (!isAuthenticated) {
    const slug = localStorage.getItem("slug");
    return <Navigate to={`/${slug}/login`} replace />;
  }

  // Check if tenant setup is completed for authenticated users
  if (isAuthenticated && tenantSetupCompleted !== "true") {
    return <Navigate to="/tenant-setup" replace />;
  }

  return (
    <ConfigProvider
      theme={{
        token: {
          motion: false,
        },
      }}
    >
      <Layout
        className="protected-layout"
        id="app-layout"
        // style={{ border: "solid 1px red" }}
      >
        {/* <ProtectedHeader /> */}
        <Layout className="protected-layout__content">
          <SideNav collapsed={collapsed} setCollapsed={setCollapsed} />
          {/* <Layout style={{ marginLeft: collapsedWidth - 5 }}> */}
          <Layout style={{ marginLeft: "0" }}>
            <AppHeader />
            <Content
              style={{
                // marginTop: 14,
                padding: "10px 20px",
                maxHeight: "93vh",
                overflow: "auto",
              }}
            >
              {children}
            </Content>
          </Layout>
        </Layout>
      </Layout>
    </ConfigProvider>
  );
};

export default ProtectedLayout;
