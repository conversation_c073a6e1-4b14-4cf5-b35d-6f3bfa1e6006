export const highlightText = (paragraph, highlightsList) => {
  if (!paragraph || !highlightsList?.length) return paragraph;

  const highlightPattern = new RegExp(
    highlightsList
      .map((sentence) => sentence.replace(/[.*+?^${}()|[\]\\]/g, "\\$&"))
      .join("|"),
    "gi"
  );

  const matches = [...paragraph.matchAll(highlightPattern)];
  if (!matches.length) return paragraph;

  let lastIndex = 0;
  const highlightedText = [];

  matches.forEach((match, index) => {
    const matchText = match[0];
    const matchStart = match.index;

    highlightedText.push(paragraph.substring(lastIndex, matchStart));

    highlightedText.push(
      <span
        key={index}
        style={{ backgroundColor: "yellow", fontWeight: "bold" }}
      >
        {matchText}
      </span>
    );

    lastIndex = matchStart + matchText.length;
  });

  highlightedText.push(paragraph.substring(lastIndex));

  return highlightedText;
};
