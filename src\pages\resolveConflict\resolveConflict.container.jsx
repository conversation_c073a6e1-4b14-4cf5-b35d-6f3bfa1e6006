import React, { useState } from "react";
import ResolveConflictComponent from "./resolveConflict.component";
import { Spin } from "antd";

const ResolveConflictContainer = () => {
  const [loading, setLoading] = useState(false);

  const [question, setQuestion] = useState();
  const [questionId, setQuestionId] = useState();

  const [answer, setAnswer] = useState();
  const [sources, setSources] = useState([]);

  return (
    <div>
      <Spin spinning={loading}>
        <ResolveConflictComponent
          question={question}
          setQuestion={setQuestion}
          questionId={questionId}
          setQuestionId={setQuestionId}
          answer={answer}
          setAnswer={setAnswer}
          sources={sources}
          setSources={setSources}
        />
      </Spin>
    </div>
  );
};

export default ResolveConflictContainer;
