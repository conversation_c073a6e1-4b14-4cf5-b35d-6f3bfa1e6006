import React from "react";
import { Pagination } from "antd";
const DataTablePagination = ({
  setPageNumber,
  pageSize,
  setPageSize,
  totalDocuments,
  currentPage,
}) => (
  <div
    style={{
      display: "flex",
      justifyContent: "center",
      marginTop: "14px",
    }}
  >
    <Pagination
      total={totalDocuments}
      showTotal={(total) => `Total ${total} items`}
      pageSize={pageSize}
      current={currentPage}
      showSizeChanger={true}
      pageSizeOptions={["5", "10", "20", "50", "100"]}
      onChange={(page, size) => {
        setPageNumber(page);
        setPageSize(size);
      }}
      onShowSizeChange={(current, size) => {
        setPageNumber(1);
        setPageSize(size);
      }}
    />
  </div>
);
export default DataTablePagination;
