import React, { useState } from "react";
import {
  Card,
  Typography,
  Avatar,
  Space,
  Input,
  Button,
  Upload,
  Image,
  Modal,
  Form,
  Divider,
  Select,
  message,
  Col,
  Tooltip,
  Tag,
} from "antd";
import {
  UserOutlined,
  RobotOutlined,
  ClockCircleOutlined,
  SendOutlined,
  PaperClipOutlined,
  SyncOutlined,
  LikeOutlined,
  DislikeOutlined,
  LikeFilled,
  DislikeFilled,
  DeleteOutlined,
  CheckCircleOutlined,
  ExclamationCircleOutlined,
  InfoCircleOutlined,
  FileTextOutlined,
  HistoryOutlined,
  PhoneOutlined,
  NumberOutlined,
  TagsOutlined,
  MailOutlined,
  WarningOutlined,
} from "@ant-design/icons";
import RenderImageCollage from "../../../pages/playground/sections/ChatWindowSection/ChatImage";
import { useAppDispatch } from "../../../hooks/reduxHooks";
import {
  handleFeedbackApi,
  handleFeedbackDeleteApi,
} from "../../../services/feedbackLog.service";
import FeedbackModal from "../../../pages/playground/sections/ChatWindowSection/FeedbackModal";
import { resolveCtaApi } from "../../../services/channels.service";
import { transformString } from "../../../helpers/channelNameTransformation";
import { getCtaByIdApi } from "../../../services/cta.service";
// import "./index.css";

const { Text, Title, Paragraph } = Typography;
const { TextArea } = Input;

// Format the timestamp
const formatTime = (timestamp) => {
  const date = new Date(timestamp);
  return date.toLocaleTimeString([], { hour: "2-digit", minute: "2-digit" });
};

// Parse reply text with line breaks
const parseReplyText = (text) => {
  return text.split("\n").map((line, index) => (
    <span key={index}>
      {line}
      {index < text.split("\n").length - 1 && <br />}
    </span>
  ));
};

const SingleChatItemChannel = ({
  message: msng,
  convIndex,
  msgIndex,
  setVisible,
  setResponseData,
  setDrawerData,
  customerInfo,
  fetchChatHis,
  setRefreshCustomerList,
}) => {
  const dispatch = useAppDispatch();

  // State for resolve modal
  const [resolveModalVisible, setResolveModalVisible] = useState(false);
  const [resolveRemarks, setResolveRemarks] = useState("");
  const [resolveFormSubmitting, setResolveFormSubmitting] = useState(false);
  const [ctaByIdInfo, setCTAByIdInfo] = useState();
  const [resolveForm] = Form.useForm();

  // State for like/dislike buttons - initialize based on evaluation_status
  const [liked, setLiked] = useState(msng?.evaluation_status === "like");
  const [disliked, setDisliked] = useState(
    msng?.evaluation_status === "dislike"
  );
  const [feedbackModalVisible, setFeedbackModalVisible] = useState(false);

  // Extract images from message if they exist
  const images = msng.images || [];
  const hasMedia = msng?.media_ids && msng?.media_ids?.length > 0;

  // State for feedback modal
  const [form] = Form.useForm();

  // Split message content by double newlines for assistant messages
  const messageParts =
    msng?.role === "assistant" && msng?.content
      ? msng?.content
          .replace(/\\n/g, "") // Unescape newlines
          .replace(/\\n{2}/g, " \n") // Exactly 2 newlines → space
          .split(/\n{3,}/) // Split on 3+ newlines
          .filter((part) => part.trim())
      : [];

  // Handle like button click
  const handleLike = (e) => {
    e.stopPropagation(); // Prevent bubble click event

    // If already liked, undo the like
    if (liked) {
      // Add API call to remove the like
      dispatch(
        handleFeedbackDeleteApi({
          data: {
            message_id: msng?.message_id,
          },
          finalCallback: () => {},
          successCallback: (response) => {
            setLiked(false);
            message.warning("Unliked!");
          },
          failureCallback: () => {},
        })
      );
      return;
    }

    dispatch(
      handleFeedbackApi({
        data: {
          message_id: msng?.message_id,
          evaluation: "like",
          remark: null,
          categories: null,
          reviewer_id: localStorage.getItem("userId"),
          created_at: null,
        },
        finalCallback: () => {},
        successCallback: (response) => {
          // Otherwise, set liked and remove disliked if necessary
          setLiked(true);
          // After dislike if user likes it dislike is removed
          if (disliked) setDisliked(false);

          // console.log("RESPONSE -> ", response);
          message.success("Liked!");
        },
        failureCallback: () => {},
      })
    );
  };

  // Handle dislike button click
  const handleDislike = (e) => {
    e.stopPropagation(); // Prevent bubble click event

    // If already disliked, just undo it without showing modal
    if (disliked) {
      dispatch(
        handleFeedbackDeleteApi({
          data: {
            message_id: msng?.message_id,
          },
          finalCallback: () => {},
          successCallback: (response) => {
            setDisliked(false);
            message.warning("Dislike removed!");
          },
          failureCallback: () => {},
        })
      );

      return;
    }

    setFeedbackModalVisible(true);
  };

  const fetchCTAById = (id) => {
    dispatch(
      getCtaByIdApi({
        id: id,
        finalCallback: () => {},
        successCallback: (response) => {
          console.log("RESPONSE of fetchCTAById-> ", response);
          setCTAByIdInfo(response?.data);
          // setResponseData(response?.data);
        },
        failureCallback: () => {},
      })
    );
  };

  return (
    <div
      key={`conv-${convIndex}-msg-${msgIndex}`}
      style={{
        marginBottom: "16px",
      }}
    >
      {msng?.role === "user" ? (
        // USER MESSAGES - Left Side
        <div>
          <div
            style={{
              display: "flex",
              alignItems: "flex-start",
              justifyContent: "flex-start", // Left aligned for user
            }}
          >
            <Avatar
              icon={<UserOutlined />}
              style={{
                backgroundColor: "#87d068",
                marginRight: "8px",
              }}
            />
            <div
              style={{
                display: "flex",
                flexDirection: "column",
              }}
            >
              {msng?.content ? (
                <div
                  style={{
                    display: "flex",
                  }}
                >
                  <div
                    style={{
                      backgroundColor: "#f0f0f0", // Light gray for user
                      borderRadius: "8px",
                      padding: "12px",
                      maxWidth: "40vw",
                    }}
                  >
                    <Paragraph style={{ marginBottom: 0 }}>
                      {msng?.content}
                    </Paragraph>
                  </div>
                  {!msng?.ai_enabled || !msng?.has_credit ? (
                    <div
                      style={{
                        marginTop: "0px",
                        marginLeft: "6px",
                      }}
                    >
                      <Tooltip
                        placement="right"
                        title={
                          <p
                            style={{
                              fontSize: "11px",
                              margin: "0",
                            }}
                          >
                            <span
                              style={{
                                fontWeight: 500,
                                fontSize: "13px",
                              }}
                            >
                              No AI Reply for this message!
                            </span>
                            <br />
                            {!msng?.has_credit && !msng?.ai_enabled
                              ? "AI Assistant was disabled when message was received and please recharge your account to continue receiving AI Reply."
                              : !msng?.has_credit
                              ? "No credits left please recharge your account to continue receiving AI Reply."
                              : !msng?.ai_enabled
                              ? "AI Assistant was disabled when message was received."
                              : ""}
                          </p>
                        }
                      >
                        <WarningOutlined
                          style={{ fontSize: "16px", color: "#ff4d4f" }}
                        />
                        {/* <Text type="danger">
                          <iconify-icon
                            icon="cuida:warning-outline"
                            width="24"
                            height="24"
                          ></iconify-icon>
                        </Text> */}
                      </Tooltip>
                    </div>
                  ) : (
                    <></>
                  )}
                </div>
              ) : (
                <div style={{ marginBottom: "16px" }}>
                  <div
                    className="message-loading"
                    style={{
                      backgroundColor: "rgb(220, 245, 200)",
                    }}
                  >
                    <div className="bouncing-dots">
                      <span
                        style={{ backgroundColor: "rgb(46, 139, 87)" }}
                      ></span>
                      <span
                        style={{ backgroundColor: "rgb(46, 139, 87)" }}
                      ></span>
                      <span
                        style={{ backgroundColor: "rgb(46, 139, 87)" }}
                      ></span>
                    </div>
                  </div>
                </div>
              )}

              {hasMedia && (
                <div style={{ marginTop: "8px", maxWidth: "80%" }}>
                  {typeof msng.media_ids === "string" ? (
                    <Card style={{ backgroundColor: "#f0f0f0" }}>
                      {msng?.media_ids}
                    </Card>
                  ) : (
                    <RenderImageCollage images={msng?.media_ids} />
                  )}
                </div>
              )}
            </div>
          </div>
          <div
            style={{
              marginLeft: "40px",
              marginTop: "4px",
            }}
          >
            <Text type="secondary" style={{ fontSize: "12px" }}>
              {formatTime(msng?.created_at || new Date().toISOString())}
            </Text>
          </div>
        </div>
      ) : (
        // ASSISTANT MESSAGES - Right Side
        <div>
          <div
            style={{
              display: "flex",
              justifyContent: "flex-end",
              width: "100%",
            }}
          >
            <div
              style={{
                display: "flex",
                flexDirection: "column",
                alignItems: "flex-end",
                maxWidth: "40vw",
              }}
            >
              {/* Map through message parts to create multiple bubbles */}
              {messageParts?.length > 0 ? (
                messageParts?.map((part, partIndex) => {
                  return (
                    <div
                      style={{
                        display: "flex",
                        gap: "6px",
                        marginBottom: "8px",
                      }}
                    >
                      {msng?.cta?.[0]?.cta_status === "open" ? (
                        <div style={{ marginTop: "0px" }}>
                          <Tooltip title="Resolve">
                            <Button
                              type="primary"
                              size="small"
                              icon={<ExclamationCircleOutlined />}
                              style={{
                                backgroundColor: "#ffc069",
                                alignSelf: "flex-start",
                              }}
                              onClick={(e) => {
                                e.stopPropagation();
                                fetchCTAById(msng?.cta?.[0]?.id);
                                setResolveModalVisible(true);
                              }}
                            />
                          </Tooltip>
                        </div>
                      ) : (
                        msng?.cta?.[0]?.cta_status === "resolved" && (
                          <div style={{ marginTop: "6px" }}>
                            <Tooltip
                              title={`${transformString(
                                msng?.cta?.[0]?.issue_type
                              )} has been resolved`}
                            >
                              <CheckCircleOutlined
                                style={{ fontSize: "18px", color: "#52c41a" }}
                              />
                            </Tooltip>
                          </div>
                        )
                      )}

                      <div
                        key={partIndex}
                        style={{
                          backgroundColor: "#e6f7ff", // Light blue for assistant
                          borderRadius: "8px",
                          padding: "12px",
                          cursor: "pointer",
                          alignSelf: "flex-end", // Align to right
                        }}
                        onClick={() => {
                          setDrawerData(msng);
                          setVisible(true);
                        }}
                      >
                        <Paragraph style={{ marginBottom: 0 }}>
                          {parseReplyText(part)}
                        </Paragraph>
                      </div>
                    </div>
                  );
                })
              ) : (
                // Fallback for empty content or no double newlines
                <></>
              )}

              {msng?.reply_urls?.length > 0 && (
                <div
                  style={{
                    display: "flex",
                    flexWrap: "wrap",
                    gap: "8px",
                    marginBottom: "8px",
                    marginTop: "8px",
                  }}
                >
                  {/* Group all images together for gallery preview */}
                  <Image.PreviewGroup>
                    {msng?.reply_urls?.map((resource, index) => {
                      // Function to determine if the resource is a YouTube video
                      const isYouTubeVideo = (url) => {
                        return (
                          url.includes("youtube.com") ||
                          url.includes("youtu.be")
                        );
                      };

                      // Function to extract video ID from YouTube URL
                      const extractVideoId = (youtubeUrl) => {
                        const standardMatch = youtubeUrl.match(
                          /youtube\.com\/watch\?v=([^&]+)/
                        );
                        const shortMatch =
                          youtubeUrl.match(/youtu\.be\/([^?]+)/);
                        const embedMatch = youtubeUrl.match(
                          /youtube\.com\/embed\/([^?]+)/
                        );

                        if (standardMatch) return standardMatch[1];
                        else if (shortMatch) return shortMatch[1];
                        else if (embedMatch) return embedMatch[1];
                        else return null;
                      };

                      // Function to check if URL is an image based on extension
                      const isImageUrl = (url) => {
                        const imageExtensions = [
                          "jpg",
                          "jpeg",
                          "png",
                          "gif",
                          "webp",
                          "svg",
                          "bmp",
                        ];
                        const extension = url
                          .split(".")
                          .pop()
                          .split(/[#?]/)[0]
                          .toLowerCase();
                        return imageExtensions.includes(extension);
                      };

                      // Determine if this is a YouTube video
                      const isYouTube = isYouTubeVideo(resource);
                      // Determine if this is an image
                      const isImage = isImageUrl(resource);

                      // Set appropriate display based on resource type
                      let thumbnail;
                      let playIconOverlay = null;

                      if (isYouTube) {
                        const videoId = extractVideoId(resource);
                        thumbnail = `https://img.youtube.com/vi/${videoId}/sddefault.jpg`;

                        // Add play button overlay for videos
                        playIconOverlay = (
                          <div
                            style={{
                              position: "absolute",
                              top: 0,
                              left: 0,
                              width: "100%",
                              height: "100%",
                              display: "flex",
                              justifyContent: "center",
                              alignItems: "center",
                              pointerEvents: "none", // This allows clicks to pass through to the image
                            }}
                          >
                            <div
                              style={{
                                width: "30px",
                                height: "30px",
                                borderRadius: "50%",
                                backgroundColor: "rgba(0, 0, 0, 0.6)",
                                display: "flex",
                                justifyContent: "center",
                                alignItems: "center",
                              }}
                            >
                              {/* Triangle Play Icon */}
                              <div
                                style={{
                                  width: 0,
                                  height: 0,
                                  borderTop: "7px solid transparent",
                                  borderBottom: "7px solid transparent",
                                  borderLeft: "12px solid white",
                                  marginLeft: "3px", // Slight offset to center visually
                                }}
                              />
                            </div>
                          </div>
                        );
                      } else if (isImage) {
                        // For images, use the image URL itself as the thumbnail
                        thumbnail = resource;
                      } else {
                        // For other video types or unknown formats, use a generic thumbnail
                        thumbnail =
                          "https://via.placeholder.com/120x90?text=Media";

                        // Add play button for non-YouTube videos
                        if (!isImage) {
                          playIconOverlay = (
                            <div
                              style={{
                                position: "absolute",
                                top: 0,
                                left: 0,
                                width: "100%",
                                height: "100%",
                                display: "flex",
                                justifyContent: "center",
                                alignItems: "center",
                                pointerEvents: "none",
                              }}
                            >
                              <div
                                style={{
                                  width: "30px",
                                  height: "30px",
                                  borderRadius: "50%",
                                  backgroundColor: "rgba(0, 0, 0, 0.6)",
                                  display: "flex",
                                  justifyContent: "center",
                                  alignItems: "center",
                                }}
                              >
                                <div
                                  style={{
                                    width: 0,
                                    height: 0,
                                    borderTop: "7px solid transparent",
                                    borderBottom: "7px solid transparent",
                                    borderLeft: "12px solid white",
                                    marginLeft: "3px",
                                  }}
                                />
                              </div>
                            </div>
                          );
                        }
                      }

                      return (
                        <div
                          key={index}
                          style={{
                            width: "120px",
                            height: "90px",
                            borderRadius: "8px",
                            overflow: "hidden",
                            position: "relative",
                            display: "flex",
                            flexDirection: "column",
                            justifyContent: "space-between",
                            border: "solid 1px #e0e0e0",
                            boxShadow: "0 1px 3px rgba(0,0,0,0.12)",
                            cursor: "pointer",
                          }}
                        >
                          {isImage ? (
                            // Use Ant Design's Image component for images
                            <Image
                              src={thumbnail}
                              alt="Image"
                              style={{
                                objectFit: "cover",
                                width: "100%",
                                height: "100%",
                              }}
                              preview={{
                                mask: null, // Remove default preview mask
                              }}
                              wrapperStyle={{
                                width: "100%",
                                height: "100%",
                              }}
                            />
                          ) : (
                            // For videos, use regular img tag with click handler
                            <>
                              <img
                                src={thumbnail}
                                alt={isYouTube ? "YouTube video" : "Media"}
                                style={{
                                  cursor: "pointer",
                                  width: "100%",
                                  height: "100%",
                                  objectFit: "cover",
                                }}
                                onClick={() => window.open(resource, "_blank")}
                              />
                              {playIconOverlay}
                            </>
                          )}
                        </div>
                      );
                    })}
                  </Image.PreviewGroup>
                </div>
              )}

              {/* Like/Dislike buttons - placed after all bubbles but aligned to left */}
              <Space style={{ marginTop: "5px", alignSelf: "flex-end" }}>
                <Button
                  type={liked ? "primary" : "text"}
                  size="small"
                  icon={liked ? <LikeFilled /> : <LikeOutlined />}
                  onClick={handleLike}
                >
                  {liked ? "Like" : "Like"}
                </Button>

                <Button
                  type={disliked ? "primary" : "text"}
                  size="small"
                  danger={disliked}
                  icon={disliked ? <DislikeFilled /> : <DislikeOutlined />}
                  onClick={handleDislike}
                >
                  {disliked ? "Dislike" : "Dislike"}
                </Button>
              </Space>
            </div>
            <Avatar
              icon={<RobotOutlined />}
              style={{
                backgroundColor: "#1890ff",
                marginLeft: "8px",
              }}
            />
          </div>
          <div
            style={{
              textAlign: "right",
              marginTop: "4px",
              marginRight: "40px",
              marginBottom: "24px",
            }}
          >
            <Text type="secondary" style={{ fontSize: "12px" }}>
              {formatTime(msng?.created_at || new Date().toISOString())}
              {msng?.processing_time && (
                <>{` · ${msng?.processing_time.toFixed(2)}s`}</>
              )}
            </Text>
          </div>
        </div>
      )}

      <FeedbackModal
        message={msng}
        feedbackModalVisible={feedbackModalVisible}
        setFeedbackModalVisible={setFeedbackModalVisible}
        liked={liked}
        setLiked={setLiked}
        setDisliked={setDisliked}
      />

      {/* Resolve Modal */}
      <Modal
        title={
          <div
            style={{
              display: "flex",
              alignItems: "center",
              justifyContent: "center",
              gap: "10px",
              color: "#1890ff",
              fontSize: "20px",
              paddingBottom: "14px",
            }}
          >
            <CheckCircleOutlined /> Resolve CTAs
          </div>
        }
        open={resolveModalVisible}
        onCancel={() => {
          setResolveModalVisible(false);
          setResolveRemarks("");
          resolveForm.resetFields();
        }}
        footer={null}
        width={750}
        centered
        style={{ borderRadius: "8px" }}
        bodyStyle={{
          maxHeight: "calc(100vh - 200px)",
          overflowY: "auto",
          paddingRight: "16px",
        }}
      >
        <Form
          form={resolveForm}
          layout="vertical"
          onFinish={(values) => {
            dispatch(
              resolveCtaApi({
                data: {
                  cta_type: msng?.cta?.[0]?.issue_type,
                  id: msng?.cta?.[0]?.id,
                  resolved_by: localStorage.getItem("username"),
                  resolved_at: new Date().toISOString(),
                  remarks: values?.remarks,
                },
                finalCallback: () => {
                  setResolveFormSubmitting(false); // modal button loading
                },
                successCallback: () => {
                  message.success("CTA resolved successfully!");
                  setResolveModalVisible(false);
                  // Reset form
                  setResolveRemarks("");
                  resolveForm.resetFields();
                  // Refresh Chat
                  fetchChatHis();
                  setRefreshCustomerList(true);
                },
                failureCallback: () => {},
              })
            );
          }}
        >
          <Card>
            <div
              style={{
                display: "flex",
                alignItems: "center",
                gap: "8px",
                color: "#1890ff",
                fontSize: "16px",
                paddingBottom: "8px",
              }}
            >
              <TagsOutlined />{" "}
              <span style={{ fontWeight: "500" }}>Issued CTAs</span>
            </div>
            <div
              style={{
                display: "flex",
                flexWrap: "wrap",
                gap: "10px",
                maxHeight: "150px",
                alignItems: "center",
                overflowY: "auto",
              }}
            >
              {msng?.cta?.map((cta) => (
                <Tag
                  key={cta?.id}
                  style={{
                    padding: "2px 12px",
                    fontSize: "14px",
                  }}
                >
                  {transformString(cta?.issue_type)}
                </Tag>
              ))}
            </div>
          </Card>

          {/* Customer Information Card */}
          <Card
            bordered={false}
            style={{
              marginTop: "24px",
              marginBottom: "24px",
            }}
            title={
              <div
                style={{
                  display: "flex",
                  alignItems: "center",
                  gap: "8px",
                  color: "#1890ff",
                  fontSize: "16px",
                }}
              >
                <UserOutlined /> <span>Customer Information</span>
              </div>
            }
          >
            <div
              style={{
                display: "flex",
                flexDirection: "column",
                gap: "16px",
              }}
            >
              <div
                style={{
                  display: "flex",
                  alignItems: "center",
                  gap: "8px",
                  padding: "8px 12px",
                  backgroundColor: "#f5f7fa",
                  borderRadius: "6px",
                }}
              >
                <Avatar
                  icon={<UserOutlined />}
                  style={{ backgroundColor: "#1890ff" }}
                  size={42}
                />
                <div>
                  <Text strong style={{ fontSize: "16px", display: "block" }}>
                    {customerInfo?.customer_name || "N/A"}
                  </Text>
                  {customerInfo?.channel === "Website" &&
                  customerInfo?.email ? (
                    <Text type="secondary">
                      <MailOutlined style={{ marginRight: "6px" }} />
                      {customerInfo?.email || "N/A"}
                    </Text>
                  ) : customerInfo?.channel === "Whatsapp" &&
                    customerInfo?.customer_id ? (
                    <Text type="secondary">
                      <PhoneOutlined style={{ marginRight: "6px" }} />
                      {customerInfo?.customer_id || "N/A"}
                    </Text>
                  ) : (
                    <></>
                  )}
                </div>
              </div>

              <div
                style={{
                  display: "flex",
                  alignItems: "flex-start",
                  gap: "8px",
                  padding: "16px 12px",
                  borderLeft: "4px solid #1890ff",
                  backgroundColor: "#f9fafc",
                  borderRadius: "0 4px 4px 0",
                }}
              >
                <FileTextOutlined
                  style={{
                    color: "#1890ff",
                    marginTop: "4px",
                    fontSize: "16px",
                  }}
                />
                <div style={{ flex: 1 }}>
                  <Text strong style={{ fontSize: "15px" }}>
                    Description:
                  </Text>
                  <Paragraph
                    style={{
                      margin: "8px 0 0 0",
                      fontSize: "14px",
                      lineHeight: "1.6",
                    }}
                  >
                    {/* {msng?.information_gathering?.[0]?.function_args
                      ?.description || "No description provided"} */}
                    {ctaByIdInfo?.description || "No description provided"}
                  </Paragraph>
                </div>
              </div>
            </div>
          </Card>
          <Form.Item
            name="remarks"
            label={
              <p
                style={{
                  fontWeight: "500",
                  color: "#444444",
                  fontSize: "15px",
                }}
              >
                Remarks (Optional)
              </p>
            }
            rules={[
              {
                message: "Please enter remarks before resolving",
              },
            ]}
          >
            <TextArea
              placeholder="Enter your remarks here..."
              autoSize={{ minRows: 4, maxRows: 6 }}
              value={resolveRemarks}
              onChange={(e) => setResolveRemarks(e.target.value)}
              style={{ borderRadius: "4px" }}
            />
          </Form.Item>
          <div
            style={{
              display: "flex",
              justifyContent: "flex-end",
              gap: "12px",
              marginTop: "20px",
            }}
          >
            <Button
              onClick={() => {
                setResolveModalVisible(false);
                setResolveRemarks("");
                resolveForm.resetFields();
              }}
            >
              Cancel
            </Button>
            <Button
              type="primary"
              htmlType="submit"
              icon={<CheckCircleOutlined />}
              loading={resolveFormSubmitting}
              style={{ backgroundColor: "#1890ff", borderColor: "#1890ff" }}
            >
              Resolve
            </Button>
          </div>
        </Form>
      </Modal>
    </div>
  );
};

export default SingleChatItemChannel;
