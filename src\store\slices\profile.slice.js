import { createSlice } from "@reduxjs/toolkit";
import { lastTwentyFourHoursDateRange } from "../../helpers/lastTwentyFourHoursDateRage";

const initialState = {
  userData: {},
  aiStatus: false,
  authToken: undefined,
  tenantLabel: undefined,
  dateRangeValue: lastTwentyFourHoursDateRange() ?? {},
};

const profileSlice = createSlice({
  name: "profile",
  initialState,
  reducers: {
    storeAuthToken: (state, action) => {
      localStorage.setItem("authToken", action.payload);
      state.authToken = action.payload;
    },
    storeUserData: (state, action) => {
      state.userData = action.payload;
    },
    storeDateRange: (state, action) => {
      state.dateRangeValue = action.payload;
    },
    storeAiStatus: (state, action) => {
      state.aiStatus = action.payload;
    },
    logout: (state) => {
      localStorage.removeItem("authToken");
      localStorage.removeItem("userId");
      localStorage.removeItem("username");
      localStorage.removeItem("role");
      localStorage.removeItem("tenant_label");
      localStorage.removeItem("nav_permissions");
      // localStorage.removeItem("slug");
      state.authToken = undefined;
      state.userData = {};
      state.dateRangeValue = {
        start_date: new Date(Date.now() - 86400000).toISOString().split("T")[0],
        end_date: new Date(Date.now()).toISOString().split("T")[0],
      };
    },
  },
});

export const {
  storeAuthToken,
  storeAiStatus,
  storeUserData,
  storeDateRange,
  logout,
} = profileSlice.actions;

export default profileSlice.reducer;
