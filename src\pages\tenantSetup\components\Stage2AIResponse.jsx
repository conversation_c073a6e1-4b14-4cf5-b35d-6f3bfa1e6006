import { useState } from "react";
import { <PERSON>, <PERSON><PERSON>, <PERSON>, Col, <PERSON>po<PERSON>, <PERSON>, Badge, Divider, Alert } from "antd";
import { 
  CheckOutlined, 
  ArrowLeftOutlined, 
  ArrowRightOutlined,
  StarFilled,
  MessageOutlined
} from "@ant-design/icons";

const { Title, Text, Paragraph } = Typography;

const Stage2AIResponse = ({ aiResponses, userQuestion, onSelect, onBack, loading }) => {
  const [selectedResponse, setSelectedResponse] = useState(null);
  const [hoveredCard, setHoveredCard] = useState(null);

  const handleCardClick = (response) => {
    setSelectedResponse(response.id);
  };

  const handleConfirmSelection = () => {
    const selected = aiResponses.find(response => response.id === selectedResponse);
    if (selected) {
      onSelect(selected);
    }
  };

  const getCardClassName = (responseId) => {
    let className = "ai-response-card";
    if (selectedResponse === responseId) {
      className += " ai-response-card--selected";
    }
    if (hoveredCard === responseId) {
      className += " ai-response-card--hovered";
    }
    return className;
  };

  return (
    <div className="stage2-ai-response">
      <div className="stage2-ai-response__header">
        <Title level={2} style={{ marginBottom: 8, color: '#1890ff', textAlign: 'center' }}>
          Choose Your AI Response Style
        </Title>
        <Paragraph style={{ textAlign: 'center', fontSize: '16px', color: '#595959', marginBottom: 24 }}>
          Based on your question, we've generated three different response styles. Choose the one that best fits your needs.
        </Paragraph>
        
        {userQuestion && (
          <Alert
            message="Your Question"
            description={
              <Text style={{ fontSize: '15px', fontStyle: 'italic' }}>
                "{userQuestion}"
              </Text>
            }
            type="info"
            icon={<MessageOutlined />}
            style={{ marginBottom: 32, borderRadius: '12px' }}
          />
        )}
      </div>

      <Row gutter={[24, 24]} style={{ marginBottom: 40 }}>
        {aiResponses.map((response, index) => (
          <Col xs={24} lg={8} key={response.id}>
            <Card
              className={getCardClassName(response.id)}
              hoverable
              onClick={() => handleCardClick(response)}
              onMouseEnter={() => setHoveredCard(response.id)}
              onMouseLeave={() => setHoveredCard(null)}
              style={{
                height: '100%',
                borderRadius: '16px',
                border: selectedResponse === response.id ? '2px solid #1890ff' : '1px solid #f0f0f0',
                boxShadow: selectedResponse === response.id 
                  ? '0 8px 24px rgba(24, 144, 255, 0.2)' 
                  : hoveredCard === response.id
                  ? '0 8px 24px rgba(0, 0, 0, 0.1)'
                  : '0 2px 8px rgba(0, 0, 0, 0.06)',
                transition: 'all 0.3s ease',
                cursor: 'pointer',
                position: 'relative',
                overflow: 'hidden'
              }}
            >
              {/* Recommended Badge for middle option */}
              {index === 1 && (
                <Badge.Ribbon
                  text={
                    <Space size={4}>
                      <StarFilled />
                      <span>Recommended</span>
                    </Space>
                  }
                  color="gold"
                  style={{ top: 16, right: -8 }}
                />
              )}

              {/* Selection Indicator */}
              {selectedResponse === response.id && (
                <div
                  style={{
                    position: 'absolute',
                    top: 16,
                    left: 16,
                    width: 28,
                    height: 28,
                    borderRadius: '50%',
                    backgroundColor: '#1890ff',
                    display: 'flex',
                    alignItems: 'center',
                    justifyContent: 'center',
                    zIndex: 1,
                    boxShadow: '0 2px 8px rgba(24, 144, 255, 0.3)'
                  }}
                >
                  <CheckOutlined style={{ color: 'white', fontSize: 16 }} />
                </div>
              )}

              <div style={{ padding: '24px', height: '100%', display: 'flex', flexDirection: 'column' }}>
                {/* Header */}
                <div style={{ textAlign: 'center', marginBottom: 20 }}>
                  <div style={{ fontSize: '32px', marginBottom: 12 }}>
                    {response.icon}
                  </div>
                  <Title level={4} style={{ marginBottom: 8, color: '#262626' }}>
                    {response.title}
                  </Title>
                  <Text type="secondary" style={{ fontSize: 14 }}>
                    {response.description}
                  </Text>
                </div>

                <Divider style={{ margin: '16px 0' }} />

                {/* Features */}
                <div style={{ marginBottom: 20 }}>
                  <Text strong style={{ color: '#595959', fontSize: 14, display: 'block', marginBottom: 12 }}>
                    Key Features:
                  </Text>
                  <Space direction="vertical" size={8} style={{ width: '100%' }}>
                    {response.features.map((feature, idx) => (
                      <Space key={idx} size={8}>
                        <CheckOutlined style={{ color: '#52c41a', fontSize: 12 }} />
                        <Text style={{ fontSize: 13, color: '#595959' }}>
                          {feature}
                        </Text>
                      </Space>
                    ))}
                  </Space>
                </div>

                {/* Sample Response */}
                <div style={{ flex: 1, marginBottom: 20 }}>
                  <Text strong style={{ color: '#595959', fontSize: 14, display: 'block', marginBottom: 12 }}>
                    Sample Response:
                  </Text>
                  <div 
                    style={{ 
                      background: '#fafafa', 
                      padding: '16px', 
                      borderRadius: '8px',
                      border: '1px solid #f0f0f0',
                      maxHeight: '120px',
                      overflowY: 'auto'
                    }}
                  >
                    <Text style={{ fontSize: 13, color: '#595959', lineHeight: 1.5 }}>
                      {response.response}
                    </Text>
                  </div>
                </div>

                {/* Select Button */}
                <Button
                  type={selectedResponse === response.id ? "primary" : "default"}
                  block
                  style={{
                    borderRadius: '8px',
                    height: '44px',
                    fontWeight: '500',
                    fontSize: '14px',
                    border: selectedResponse === response.id ? 'none' : '1px solid #d9d9d9',
                    background: selectedResponse === response.id 
                      ? 'linear-gradient(135deg, #1890ff 0%, #096dd9 100%)'
                      : 'white'
                  }}
                  onClick={(e) => {
                    e.stopPropagation();
                    handleCardClick(response);
                  }}
                >
                  {selectedResponse === response.id ? "Selected" : "Select This Style"}
                </Button>
              </div>
            </Card>
          </Col>
        ))}
      </Row>

      {/* Navigation Buttons */}
      <div style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
        <Button
          icon={<ArrowLeftOutlined />}
          onClick={onBack}
          size="large"
          style={{
            borderRadius: '12px',
            height: '50px',
            paddingLeft: '24px',
            paddingRight: '24px',
            fontSize: '14px'
          }}
        >
          Back to Business Info
        </Button>

        <Button
          type="primary"
          icon={<ArrowRightOutlined />}
          onClick={handleConfirmSelection}
          disabled={!selectedResponse}
          loading={loading}
          size="large"
          style={{
            borderRadius: '12px',
            height: '50px',
            paddingLeft: '32px',
            paddingRight: '32px',
            fontSize: '16px',
            fontWeight: '600',
            background: 'linear-gradient(135deg, #52c41a 0%, #389e0d 100%)',
            border: 'none',
            boxShadow: '0 4px 15px rgba(82, 196, 26, 0.3)',
          }}
        >
          Continue to Final Step
        </Button>
      </div>

      {/* Help Text */}
      {selectedResponse && (
        <div style={{ marginTop: 24, textAlign: 'center' }}>
          <Text type="secondary" style={{ fontSize: 14 }}>
            Don't worry, you can always change your response style later in the settings.
          </Text>
        </div>
      )}
    </div>
  );
};

export default Stage2AIResponse;
