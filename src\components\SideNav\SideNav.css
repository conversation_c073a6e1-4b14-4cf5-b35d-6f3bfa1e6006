.custom-sider {
  background-color: #001529;
  overflow: hidden;
}

.custom-menu {
  padding-top: 8px;
  background-color: #001529;
  border-right: none;
}

.menu-item {
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 13.5px;
  padding-left: 25px !important;
  padding-top: 2px !important;
}

.menu-item:hover {
  background: #002a4a !important;
  color: #1890ff !important;
}

.menu-icon {
  font-size: 22px !important;
  margin-right: 4px;
}

.logout-button {
  position: absolute;
  bottom: 20px;
  left: 50%;
  transform: translateX(-50%);
  border: none;
  color: white;
  background-color: transparent;
  font-size: 18px;
  display: flex;
  justify-content: center;
  align-items: center;
}

.logout-button:hover,
.setting-button:hover {
  color: #1890ff;
  background-color: rgba(255, 255, 255, 0.1);
}

.change-password-button {
  position: absolute;
  bottom: 65px;
  left: 50%;
  transform: translateX(-50%);
  border: none;
  color: white;
  background-color: transparent;
  font-size: 18px;
  display: flex;
  justify-content: center;
  align-items: center;
}

.change-password-button:hover {
  color: #1890ff;
  background-color: rgba(255, 255, 255, 0.1);
}

/* New styles for collapsible functionality */
.logo-container {
  height: 50px;
  color: white;
  background-color: #002140;
}

.logo {
  font-size: 18px;
  font-weight: bold;
  color: white;
  white-space: nowrap;
  overflow: hidden;
}

.collapse-button {
  background: transparent;
  color: white;
  border: none;
  font-size: 16px;
  padding: 0;
  width: 32px;
  height: 32px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.collapse-button:hover {
  /* background: #002a4a !important; */
  color: #1890ff !important;
}

.ant-menu-item-selected {
  background-color: #1890ff !important;
  color: white !important;
}

/* To prevent the hover effect from overriding the selected state */
.ant-menu-item-selected:hover {
  background-color: #1890ff !important;
  color: white !important;
}
