import { Select } from "antd";
import React from "react";

const MultipleSelectOption = ({ placeholder, handleChange, options }) => {
  return (
    <Select
      mode="multiple"
      showSearch
      placeholder={placeholder}
      optionFilterProp="children"
      filterOption={(input, option) =>
        option.children.toLowerCase().includes(input.toLowerCase())
      }
      allowClear
      style={{
        width: "100%",
      }}
      //   defaultValue={["a10", "c12"]}
      onChange={handleChange}
      options={options}
    />
  );
};

export default MultipleSelectOption;
