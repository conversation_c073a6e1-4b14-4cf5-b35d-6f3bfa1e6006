import React from "react";
import { Modal, Form, Input, message } from "antd";

const TopicDialog = ({
  visible,
  modalType,
  topics,
  selectedTopicId,
  selectedSubtopic,
  onCreate,
  onUpdate,
  onCancel,
}) => {
  const [form] = Form.useForm();

  React.useEffect(() => {
    if (modalType === "edit-topic") {
      const topic = topics.find(t => t.id === selectedTopicId);
      form.setFieldsValue({ name: topic.name, type: "topic" });
    }
    else if (modalType === "edit-subtopic") {
      form.setFieldsValue({
        name: selectedSubtopic.name,
        type: "subtopic",
        parent_id: selectedTopicId,
      });
    }
    else if (modalType === "add-topic") {
      form.setFieldsValue({ name: "", type: "topic" });
    }
    else if (modalType === "add-subtopic") {
      form.setFieldsValue({ name: "", type: "subtopic", parent_id: selectedTopicId });
    }
  }, [modalType, selectedTopicId, selectedSubtopic, topics, form]);

  const handleOk = () => {
    form.validateFields().then(values => {
      if (modalType.includes("add")) {
        onCreate(values);
      } else {
        onUpdate(values);
      }
    }).catch(() => {
      message.error("Please fill in all required fields");
    });
  };

  const parentTopicName = modalType.includes("subtopic")
    ? topics.find(t => t.id === selectedTopicId)?.name || "N/A"
    : null;

  return (
    <Modal
      title={
        modalType
          ? `${modalType.includes("add") ? "Add" : "Edit"} ${modalType
              .split("-")[1]
              ?.replace(/^\w/, (c) => c.toUpperCase()) || ""}`
          : ""
      }
      open={visible}
      onOk={handleOk}
      onCancel={onCancel}
      okText="Submit"
      transitionName=""
      maskTransitionName=""
    >
      <Form form={form} layout="vertical">
        <Form.Item
          label="Name"
          name="name"
          rules={[{ required: true, message: "Please enter a name" }]}
        >
          <Input placeholder="Enter name" autoFocus />
        </Form.Item>
        <Form.Item name="type" noStyle>
          <Input type="hidden" />
        </Form.Item>
        {modalType.includes("subtopic") && (
          <Form.Item
            label="Parent Topic"
            name="parent_id"
          >
            <Input
              value={parentTopicName}
              disabled
              style={{ color: "rgba(0, 0, 0, 0.85)" }}
            />
            <Input type="hidden" />
          </Form.Item>
        )}
      </Form>
    </Modal>
  );
};

export default TopicDialog;