import { Layout, ConfigProvider } from "antd";
import { Navigate } from "react-router-dom";

const TenantSetupLayout = (props) => {
  const { children } = props;

  const token = localStorage.getItem("authToken");
  const isAuthenticated = !!token;
  const tenantSetupCompleted = localStorage.getItem("tenantSetupCompleted");

  // If not authenticated, redirect to login
  if (!isAuthenticated) {
    const slug = localStorage.getItem("slug") || "demo";
    return <Navigate to={`/${slug}/login`} replace />;
  }

  // If tenant setup is already completed, redirect to dashboard
  if (tenantSetupCompleted === "true") {
    return <Navigate to="/dashboard" replace />;
  }

  return (
    <ConfigProvider
      theme={{
        token: {
          motion: false,
        },
      }}
    >
      <Layout 
        className="tenant-setup-layout" 
        style={{ 
          minHeight: "100vh",
          background: "transparent"
        }}
      >
        {children}
      </Layout>
    </ConfigProvider>
  );
};

export default TenantSetupLayout;
