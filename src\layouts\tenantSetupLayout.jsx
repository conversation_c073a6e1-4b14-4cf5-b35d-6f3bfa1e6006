import React from "react";
import { Layout, ConfigProvider } from "antd";
import { Navigate } from "react-router-dom";
import { useAppDispatch } from "../hooks/reduxHooks";
import { verifyTokenApi } from "../services/auth.service";

const TenantSetupLayout = (props) => {
  const { children } = props;
  const dispatch = useAppDispatch();

  const token = localStorage.getItem("authToken");
  const isAuthenticated = !!token;
  const tenantSetupCompleted = localStorage.getItem("tenantSetupCompleted");

  // Verify token for authenticated users
  if (isAuthenticated) {
    dispatch(
      verifyTokenApi({
        finalCallback: () => {},
        successCallback: () => {},
        failureCallback: (error) => {
          // If token is invalid, redirect to login
          localStorage.removeItem("authToken");
          localStorage.removeItem("tenantSetupCompleted");
          const slug = localStorage.getItem("slug");
          window.location.href = `/${slug}/login`;
        },
      })
    );
  }

  // If not authenticated, redirect to login
  if (!isAuthenticated) {
    const slug = localStorage.getItem("slug");
    return <Navigate to={`/${slug}/login`} replace />;
  }

  // If tenant setup is already completed, redirect to dashboard
  if (tenantSetupCompleted === "true") {
    return <Navigate to="/dashboard" replace />;
  }

  return (
    <ConfigProvider
      theme={{
        token: {
          motion: false,
        },
      }}
    >
      <Layout 
        className="tenant-setup-layout" 
        style={{ 
          minHeight: "100vh",
          background: "transparent"
        }}
      >
        {children}
      </Layout>
    </ConfigProvider>
  );
};

export default TenantSetupLayout;
