import { Modal, Tabs, message } from "antd";
import React, { useEffect, useState, useRef } from "react";
import httpBase from "../../../../utils/http.utils";
import FileUploadPanel from "../../../FileManager/UploadFile/UploadFile.component";
import ExistingFiles from "./ExistingFilemanager";
import { useNavigate } from "react-router-dom";
import { useAppDispatch } from "../../../../hooks/reduxHooks";
import { logout } from "../../../../store/slices/profile.slice";

const FileManagerModalComponent = ({
  visible,
  onClose,
  fetchSources,
  fetchTOC,
}) => {
  const [loading, setLoading] = useState(false);
  const [existingFiles, setExistingFiles] = useState([]);
  const [limit] = useState(8);
  const [totalFiles, setTotalFiles] = useState(0);
  const [page, setPage] = useState(1);

  const navigate = useNavigate();
  const dispatch = useAppDispatch();

  // File uploading
  const wsRef = useRef(null);
  const [fileList, setFileList] = useState([]);

  // Fetching existing files
  const fetchExistingFiles = async (currentPage) => {
    setLoading(true);
    try {
      const response = await httpBase().get(
        `/list_docs?prefix=Files&page=${currentPage}&limit=${limit}`
      );
      if (response.status === 200) {
        setExistingFiles(response.data.files);
        setTotalFiles(response.data.total_files);
      }
    } catch (error) {
      console.error("Error fetching files:", error);
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    fetchExistingFiles(page);
  }, [page]);

  const [activeTab, setActiveTab] = useState("1"); // Manage active tab

  const items = [
    {
      key: "1",
      label: "Upload File",
      children: (
        <FileUploadPanel
          setActiveTab={setActiveTab}
          fetchExistingFiles={fetchExistingFiles}
          onClose={onClose}
          page={page}
          fetchSources={fetchSources}
          fetchTOC={fetchTOC}
        />
      ),
    },
    {
      key: "2",
      label: "Existing File",
      children: (
        <ExistingFiles
          setActiveTab={setActiveTab}
          existingFiles={existingFiles}
          page={page}
          loading={loading}
          limit={limit}
          totalFiles={totalFiles}
          setPage={setPage}
          fetchExistingFiles={fetchExistingFiles}
          fetchSources={fetchSources}
          fetchTOC={fetchTOC}
        />
      ),
    },
  ];

  return (
    <Modal
      title="File Manager"
      open={visible}
      onCancel={onClose}
      footer={null} // Remove default footer buttons
      width="70vw" // Set modal width
      centered // Ensure modal is always centered
      bodyStyle={{
        padding: "0 20px",
        maxHeight: "80vh", // Fixed height for content
        overflowY: "auto", // Enable vertical scrolling
        display: "flex",
        flexDirection: "column",
        alignItems: "center", // Center content horizontally
      }}
      style={{
        borderRadius: "12px",
      }}
      mask={true}
      // maskStyle={{
      //   backdropFilter: "blur(0.8px)",
      // }}
      className="custom-modal" // Custom class for additional styling
    >
      <Tabs
        tabBarStyle={{
          position: "sticky",
          top: 0,
          zIndex: 10,
          background: "#ffffff",
          padding: "0 10px",
        }}
        activeKey={activeTab}
        onChange={(key) => setActiveTab(key)}
        items={items}
        style={{
          width: "100%", // Ensure tabs content takes full width
        }}
      />
    </Modal>
  );
};

export default FileManagerModalComponent;