import React from "react";
import {
  Modal,
  Descriptions,
  Tag,
  Avatar,
  Typography,
  Divider,
  Button,
  Tooltip,
} from "antd";
import {
  UserOutlined,
  CalendarOutlined,
  TagOutlined,
  PhoneOutlined,
  CheckOutlined,
  MailOutlined,
  MessageOutlined,
  GlobalOutlined,
} from "@ant-design/icons";
import dayjs from "dayjs";
import { transformString } from "../../helpers/channelNameTransformation";

const { Title, Text } = Typography;

const CtaDetailsDialog = ({
  visible,
  setCtaStatusDialogOpen,
  ctaData,
  onClose,
}) => {
  console.log("ctaData -> ", ctaData);

  const formatDate = (dateString) => {
    return dateString
      ? dayjs(dateString).format("MMMM D, YYYY [at] h:mm A")
      : "N/A";
  };

  if (!visible || !ctaData) return null;

  return (
    <Modal
      transitionName=""
      maskTransitionName=""
      title={<Title level={4}>{transformString(ctaData?.name) || "N/A"}</Title>}
      open={visible}
      onCancel={onClose}
      footer={null}
      width={800}
      style={{
        zIndex: "10000",
      }}
    >
      {ctaData ? (
        <div>
          <Descriptions
            bordered
            column={1}
            labelStyle={{ width: "30%", fontWeight: 500 }}
            contentStyle={{ width: "70%" }}
          >
            <Descriptions.Item label="Customer Name">
              <div
                style={{ display: "flex", alignItems: "center", gap: "8px" }}
              >
                <Avatar icon={<UserOutlined />} />
                <Text strong>
                  {transformString(ctaData.customer_name) || "N/A"}
                </Text>
              </div>
            </Descriptions.Item>
            {ctaData.customer_phone_number ? (
              <Descriptions.Item label="Phone Number">
                <Text>
                  <PhoneOutlined style={{ marginRight: "8px" }} />
                  {ctaData.customer_phone_number || "N/A"}
                </Text>
              </Descriptions.Item>
            ) : null}
            {ctaData.customer_email ? (
              <Descriptions.Item label="Email Address">
                <Text>
                  <MailOutlined style={{ marginRight: "8px" }} />
                  {ctaData.customer_email || "N/A"}
                </Text>
              </Descriptions.Item>
            ) : null}

            {/* <Descriptions.Item label="Contact Details">
              {ctaData?.channel === "Whatsapp" ? (
                <Text>
                  <PhoneOutlined style={{ marginRight: "8px" }} />
                  {ctaData.customer_phone_number || "N/A"}
                </Text>
              ) : (
                ctaData?.channel === "Website" && (
                  <Text>
                    <MailOutlined style={{ marginRight: "8px" }} />
                    {ctaData.customer_email || "N/A"}
                  </Text>
                )
              )}
            </Descriptions.Item> */}
            <Descriptions.Item label="Description">
              <Text>{ctaData.description || "No description provided"}</Text>
            </Descriptions.Item>
            <Descriptions.Item label="Channel">
              {ctaData.channel === "Whatsapp" ||
              ctaData.channel === "Playground" ||
              ctaData.channel === "Facebook" ? (
                <Tag icon={<MessageOutlined />}>{ctaData.channel || "N/A"}</Tag>
              ) : (
                ctaData.channel === "Website" && (
                  <Tag icon={<GlobalOutlined />}>
                    {ctaData.channel || "N/A"}
                  </Tag>
                )
              )}
            </Descriptions.Item>
            <Descriptions.Item label="Type">
              <Tag icon={<TagOutlined />}>
                {transformString(ctaData.type) || "N/A"}
              </Tag>
            </Descriptions.Item>
            <Descriptions.Item label="Created At">
              <Text>
                <CalendarOutlined style={{ marginRight: "8px" }} />
                {formatDate(ctaData.created_at)}
              </Text>
            </Descriptions.Item>
            <Descriptions.Item label="Status">
              <div style={{ display: "flex", alignItems: "center" }}>
                <Tag
                  color={
                    ctaData.status === "open"
                      ? "blue"
                      : ctaData.status === "resolved"
                      ? "green"
                      : ctaData.status === "in_progress"
                      ? "orange"
                      : "default"
                  }
                >
                  {ctaData.status?.toUpperCase() || "N/A"}
                </Tag>

                {ctaData.status === "open" && (
                  <Tooltip title="Resolve this CTA" placement="top">
                    <Button
                      type="primary"
                      size="small"
                      onClick={() => {
                        setCtaStatusDialogOpen(true);
                        // close the details dialog
                        onClose();
                      }}
                      tooltip="Resolve this CTA"
                      style={{
                        marginLeft: "8px",
                        backgroundColor: "#3D90D7",
                        height: "22px",
                      }}
                    >
                      <CheckOutlined style={{ marginRight: "4px" }} />
                      Mark as Resolved
                    </Button>
                  </Tooltip>
                )}
              </div>
            </Descriptions.Item>
            {ctaData.status === "resolved" && (
              <>
                <Descriptions.Item label="Resolved At">
                  <Text>
                    <CalendarOutlined style={{ marginRight: "4px" }} />
                    {formatDate(ctaData.resolved_at)}
                  </Text>
                </Descriptions.Item>
                <Descriptions.Item label="Resolved By">
                  <Text>{ctaData.resolved_by || "N/A"}</Text>
                </Descriptions.Item>
              </>
            )}
          </Descriptions>
          {ctaData.status === "resolved" && (
            <>
              <Divider />
              <div style={{ marginTop: "16px" }}>
                <Title level={5}>Resolution Details</Title>
                <Text type="secondary">
                  {ctaData.remarks || "No remarks provided."}
                </Text>
              </div>
            </>
          )}
        </div>
      ) : (
        <Text>No data available.</Text>
      )}
    </Modal>
  );
};

export default CtaDetailsDialog;
