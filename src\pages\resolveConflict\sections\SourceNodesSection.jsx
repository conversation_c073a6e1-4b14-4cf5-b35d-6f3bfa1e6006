import React, { useState } from "react";
import {
  List,
  Card,
  Typography,
  Space,
  Tag,
  Tooltip,
  Input,
  Button,
  message,
  Empty,
} from "antd";
import { highlightText } from "../../../helpers/highlightText";
import { EditOutlined } from "@ant-design/icons";
import { useAppDispatch } from "../../../hooks/reduxHooks";
import { updateSourceNodeApi } from "../../../services/resolveConflict.service";

const { Title, Text, Paragraph } = Typography;
const { TextArea } = Input;

const SourceNodesSection = ({ nodes, questionId, fetchAnswerById }) => {
  // console.log("nodes -> ", nodes);
  return (
    <div style={{ marginTop: "12px", marginRight: "10px" }}>
      {nodes?.length > 0 && <Title level={4}>Sources</Title>}
      {nodes?.length > 1 ? (
        <List
          dataSource={nodes}
          renderItem={(item, index) => (
            <SourceCard
              item={item}
              index={index}
              questionId={questionId}
              fetchAnswerById={fetchAnswerById}
            />
          )}
        />
      ) : (
        <div style={{ textAlign: "center", padding: "20px", color: "#888" }}>
          <Empty description="No Duplicate / conflict sources available" />
        </div>
      )}
    </div>
  );
};

const SourceCard = ({ item, index, questionId, fetchAnswerById }) => {
  const dispatch = useAppDispatch();
  const [isEditing, setIsEditing] = useState(false);
  const [text, setText] = useState(item.node.text);
  const [originalText, setOriginalText] = useState(item.node.text);

  // Handle change while editing
  const handleChange = (e) => {
    setText(e.target.value);
  };

  // Handle when 'Cancel' is clicked
  const handleCancel = () => {
    setText(originalText); // Revert to the original text
    setIsEditing(false); // Exit editing mode
  };

  // Handle when 'OK' is clicked
  const handleOk = () => {
    setOriginalText(text); // Update the original text
    setIsEditing(false); // Exit editing mode

    dispatch(
      updateSourceNodeApi({
        data: {
          id_: item.node.id_,
          question_id: questionId,
          text: text,
          extra_info: {
            title: item.node.metadata?.title,
            source: item.node.metadata?.source,
            images: item.node.metadata?.images,
            page_number: item.node.metadata?.page_number,
            section_title: item.node.metadata?.section_title,
          },
        },
        params: { collection_name: "test_page_info" },
        finalCallback: () => {},
        successCallback: (response) => {
          fetchAnswerById();
          message.success("Question updated successfully!");
        },
        failureCallback: () => {},
      })
    );
  };

  // Handle edit button click
  const handleEditClick = () => {
    setIsEditing(true);
  };

  // Determine the minimum height to prevent card shrinking
  const getContentStyle = () => {
    // Use a fixed height that accommodates both viewing and editing modes
    return {
      minHeight: "150px", // Adjust this value as needed
      width: "100%",
      // maxHeight: "70vh",
      // overflow: "auto",
    };
  };

  return (
    <List.Item
      style={{ gap: "8px", padding: 0, marginBottom: "14px", border: "none" }}
    >
      <Card
        size="small"
        title={
          <div
            style={{
              display: "flex",
              justifyContent: "space-between",
              alignItems: "center",
            }}
          >
            <div>
              <Text strong style={{ marginRight: "12px" }}>
                {item.node.metadata?.title || `Source ${index + 1}`}
              </Text>
              <Tooltip title="Relevance Score">
                <Tag color="blue">{(item.score * 100).toFixed(2)}% match</Tag>
              </Tooltip>
            </div>

            <Button
              size="small"
              onClick={handleEditClick}
              icon={<EditOutlined />}
            >
              Edit
            </Button>
          </div>
        }
        style={{ width: "100%" }}
      >
        <div style={getContentStyle()}>
          <Space direction="vertical" style={{ width: "100%" }}>
            {isEditing ? (
              <Space direction="vertical" style={{ width: "100%" }}>
                <TextArea
                  value={text}
                  onChange={handleChange}
                  autoSize={{ minRows: 4, maxRows: 8 }}
                  style={{
                    width: "100%",
                    minHeight: "100px",
                  }}
                />
                <Space>
                  <Button onClick={handleCancel} size="small">
                    Cancel
                  </Button>
                  <Button onClick={handleOk} type="primary" size="small">
                    OK
                  </Button>
                </Space>
              </Space>
            ) : (
              <Paragraph
                onClick={handleEditClick}
                style={{
                  width: "100%",
                  maxHeight: "30vh",
                  overflow: "auto",
                  scrollbarWidth: "thin",
                  paddingRight: "8px",
                }}
              >
                {highlightText(text, item.node.metadata.sentence)}
              </Paragraph>
            )}

            <Space wrap>
              {item.node.metadata?.section_title && (
                <Tag>Section: {item.node.metadata.section_title}</Tag>
              )}
              {item.node.metadata?.source && (
                <Tag>Source: {item.node.metadata.source}</Tag>
              )}
              {item.node.metadata?.page_number && (
                <Tag>Page number: {item.node.metadata.page_number}</Tag>
              )}
            </Space>
          </Space>
        </div>
      </Card>
    </List.Item>
  );
};

export default SourceNodesSection;
