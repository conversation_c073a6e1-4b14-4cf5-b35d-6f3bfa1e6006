import React from "react";
import { But<PERSON>, <PERSON>, Typography } from "antd";
import {
  ArrowLeftOutlined,
  ArrowRightOutlined,
  CheckOutlined
} from "@ant-design/icons";

const { Text } = Typography;

const StageNavigation = ({
  currentStage,
  totalStages,
  onNext,
  onPrevious,
  onComplete,
  nextDisabled = false,
  loading = false,
  nextText = "Next",
  completeText = "Complete Setup"
}) => {
  const isLastStage = currentStage === totalStages;
  const isFirstStage = currentStage === 1;

  return (
    <div className="stage-navigation">
      <div className="stage-navigation__content">
        <div className="stage-navigation__left">
          {!isFirstStage && (
            <Button
              icon={<ArrowLeftOutlined />}
              onClick={onPrevious}
              size="large"
              style={{
                borderRadius: 8,
                height: 48,
                paddingLeft: 24,
                paddingRight: 24
              }}
            >
              Previous
            </Button>
          )}
        </div>

        <div className="stage-navigation__center">
          <Text type="secondary" style={{ fontSize: 14 }}>
            Step {currentStage} of {totalStages}
          </Text>
        </div>

        <div className="stage-navigation__right">
          {isLastStage ? (
            <Button
              type="primary"
              icon={<CheckOutlined />}
              onClick={onComplete}
              disabled={nextDisabled}
              loading={loading}
              size="large"
              style={{
                borderRadius: 8,
                height: 48,
                paddingLeft: 32,
                paddingRight: 32,
                fontSize: 16,
                fontWeight: 600
              }}
            >
              {completeText}
            </Button>
          ) : (
            <Button
              type="primary"
              icon={<ArrowRightOutlined />}
              onClick={onNext}
              disabled={nextDisabled}
              loading={loading}
              size="large"
              style={{
                borderRadius: 8,
                height: 48,
                paddingLeft: 32,
                paddingRight: 32,
                fontSize: 16,
                fontWeight: 600
              }}
            >
              {nextText}
            </Button>
          )}
        </div>
      </div>

      {/* Help Text */}
      <div className="stage-navigation__help">
        <Text type="secondary" style={{ fontSize: 12, textAlign: 'center', display: 'block' }}>
          {isLastStage 
            ? "Don't worry, you can always change these settings later in your dashboard."
            : "All information is saved automatically as you progress."
          }
        </Text>
      </div>
    </div>
  );
};

export default StageNavigation;
