.protected-header {
  background-color: #ffff;
  // border: solid 1px black;
  // ...
  display: flex;
  justify-content: space-between;
  align-items: center;
  // height: 48px; /* Set a smaller height */
  height: 50px; /* Set a smaller height */
  line-height: 48px; /* Adjust line height to vertically center content */
  padding: 0 20px; /* Adjust padding as needed */

  // LOGO
  .logo {
    cursor: pointer;
    color: black;
    font-size: 1.5em;
    font-weight: bold;
    img {
      height: 50px;
      width: 50px;
      object-fit: contain;
    }
    span {
      color: black;
      font-size: 22px;
      font-weight: 400;
      letter-spacing: 2px;
    }
  }

  // Logout Section
  .logout-section {
    // border: solid 1px red;
    display: flex;
    color: #ff4d4f;
    align-items: center;
    cursor: pointer;
  }

  // Menu Items
  .ant-menu-overflow {
    flex: 1;
    margin-left: 32px;
    border-bottom: none;
  }
}
