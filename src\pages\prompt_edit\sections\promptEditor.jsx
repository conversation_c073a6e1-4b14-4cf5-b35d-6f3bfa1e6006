import React, { useState, useEffect } from "react";
import {
  Select,
  Input,
  <PERSON>ton,
  Card,
  Typography,
  Space,
  Row,
  Col,
  Spin,
} from "antd";
import { transformString } from "../../../helpers/channelNameTransformation";
import "./promptEditor.css";

const { Option } = Select;
const { TextArea } = Input;
const { Title, Text } = Typography;

export default function PromptEditor({
  promptsData,
  updatePrompt,
  loadingUpdate,
}) {
  const [selectedPromptId, setSelectedPromptId] = useState(null);
  const [editableData, setEditableData] = useState({
    name: "",
    model: "",
    text: "",
    description: "",
  });

  // Initialize state when promptsData loads
  useEffect(() => {
    if (promptsData && promptsData.length > 0 && !selectedPromptId) {
      const firstPrompt = promptsData[0];
      setSelectedPromptId(firstPrompt.id);
      setEditableData({
        name: firstPrompt.name || "",
        model: firstPrompt.model || "",
        text: firstPrompt.text || "",
        description: firstPrompt.description || "",
      });
    }
  }, [promptsData, selectedPromptId]);

  const handleSelectChange = (value) => {
    const selectedPrompt = promptsData.find((prompt) => prompt.id === value);
    setSelectedPromptId(value);
    setEditableData({
      name: selectedPrompt?.name || "",
      model: selectedPrompt?.model || "",
      text: selectedPrompt?.text || "",
      description: selectedPrompt?.description || "",
    });
  };

  const handleInputChange = (field, value) => {
    setEditableData((prev) => ({
      ...prev,
      [field]: value,
    }));
  };

  const handleSaveChanges = () => {
    const data = {
      id: selectedPromptId,
      ...editableData,
    };

    updatePrompt(data);
  };

  // Show loading state if no data
  if (!promptsData || promptsData.length === 0) {
    return (
      <div
        style={{
          padding: "24px",
          width: "70%",
          margin: "0 auto",
        }}
      >
        <Card
          style={{
            display: "flex",
            justifyContent: "center",
            alignItems: "center",
            height: "500px",
            background: "none",
          }}
        >
          <Spin spinning={true} />
        </Card>
      </div>
    );
  }

  return (
    <div
      style={{
        width: "100%",
      }}
    >
      <Card
        title={
          <Title level={2} style={{ margin: 0 }}>
            Prompt Editor
          </Title>
        }
        style={{
          boxShadow: "0 4px 12px rgba(0,0,0,0.1)",
        }}
      >
        <Space
          direction="vertical"
          size="large"
          style={{
            width: "100%",
          }}
        >
          {/* Two-column layout for Prompt/Model and Description */}
          <Row gutter={[24, 16]} align="stretch">
            {/* Left Column: Select Prompt and Model */}
            <Col xs={24} md={12}>
              <Card
                bordered={false}
                className="prompt-card"
                style={{
                  height: "100%",
                  // backgroundColor: "#fafafa",
                  borderRadius: "8px",
                  padding: "0",
                }}
                bodyStyle={{
                  height: "100%",
                  padding: "20px",
                }}
              >
                {/* Select Dropdown */}
                <div style={{ marginBottom: "24px" }}>
                  <Text
                    strong
                    style={{
                      fontSize: "16px",
                      marginBottom: "8px",
                      display: "block",
                      color: "#333",
                    }}
                  >
                    Select Prompt:
                  </Text>
                  <Select
                    value={selectedPromptId}
                    onChange={handleSelectChange}
                    style={{ width: "100%" }}
                    size="large"
                    dropdownStyle={{ borderRadius: "6px" }}
                  >
                    {promptsData.map((prompt) => (
                      <Option key={prompt.id} value={prompt.id}>
                        {prompt?.label || transformString(prompt?.name)}
                      </Option>
                    ))}
                  </Select>
                </div>

                {/* Model Input */}
                <div>
                  <Text
                    strong
                    style={{
                      fontSize: "16px",
                      marginBottom: "8px",
                      display: "block",
                      color: "#333",
                    }}
                  >
                    Model:
                  </Text>
                  <Input
                    value={editableData?.model}
                    onChange={(e) => handleInputChange("model", e.target.value)}
                    placeholder="Enter model"
                    size="large"
                    style={{ borderRadius: "6px" }}
                  />
                </div>
              </Card>
            </Col>

            {/* Right Column: Description */}
            <Col xs={24} md={12}>
              <Card
                bordered={false}
                title={
                  <Text
                    strong
                    style={{
                      fontSize: "16px",
                      color: "#333",
                      margin: 0,
                    }}
                  >
                    Description
                  </Text>
                }
                style={{
                  height: "100%",
                  backgroundColor: "#fafafa",
                  borderRadius: "8px",
                }}
                bodyStyle={{ height: "calc(100% - 58px)" }}
              >
                <TextArea
                  value={editableData?.description || "Not Available"}
                  readOnly
                  onChange={(e) =>
                    handleInputChange("description", e.target.value)
                  }
                  autoSize={{ minRows: 6, maxRows: 10 }}
                  style={{
                    padding: "12px",
                    backgroundColor: "#fafafa",
                    borderRadius: "6px",
                    fontSize: "14px",
                    lineHeight: "1.6",
                    minHeight: "120px",
                    whiteSpace: "pre-wrap",
                    border: "1px solid #e8e8e8",
                    height: "100%",
                    overflow: "auto",
                  }}
                  className="no-focus-border"
                />
              </Card>
            </Col>
          </Row>

          {/* Text Field - Full Width */}
          <Card
            bordered={false}
            title={
              <Text
                strong
                style={{
                  fontSize: "16px",
                  color: "#333",
                  margin: 0,
                }}
              >
                Text:
              </Text>
            }
            style={{
              borderRadius: "8px",
            }}
          >
            <TextArea
              value={editableData?.text}
              onChange={(e) => handleInputChange("text", e.target.value)}
              placeholder="Enter prompt text here..."
              autoSize={{ minRows: 12, maxRows: 25 }}
              style={{
                borderRadius: "6px",
                fontSize: "14px",
                lineHeight: "1.6",
                backgroundColor: "white",
                border: "1px solid #e8e8e8",
              }}
            />
          </Card>

          {/* Save Button */}
          <div style={{ textAlign: "center", paddingTop: "16px" }}>
            <Button
              type="primary"
              size="large"
              onClick={handleSaveChanges}
              style={{
                backgroundColor: "#1890ff",
                borderColor: "#1890ff",
                borderRadius: "6px",
                padding: "8px 32px",
                height: "auto",
                fontSize: "16px",
                fontWeight: "600",
                boxShadow: "0 2px 0 rgba(0,0,0,0.045)",
              }}
              loading={loadingUpdate}
              disabled={loadingUpdate}
            >
              Save Changes
            </Button>
          </div>
        </Space>
      </Card>
    </div>
  );
}
