.chat-window {
  display: flex;
  flex-direction: column;
  /* height: calc(100vh - 120px); */
  height: calc(100vh - 80px);
  /* border: 1px solid #f0f0f0; */
  border-radius: 8px;
  background: #fff;
}

.chat-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 16px;
  border-bottom: 1px solid #f0f0f0;
}

.customer-info {
  display: flex;
  align-items: center;
  cursor: pointer;
}

.customer-name {
  margin-left: 8px;
  font-weight: 500;
}

.chat-messages {
  flex: 1;
  overflow-y: auto;
  padding: 16px;
  margin-right: 5px;
  /* background: #eceff1; */
}

/* Scrollbar track */
.chat-messages::-webkit-scrollbar {
  width: 8px; /* Width of the scrollbar */
}

/* Scrollbar thumb */
.chat-messages::-webkit-scrollbar-thumb {
  background: #c0c0c0; /* Color of the scrollbar thumb */
  border-radius: 5px; /* Rounded corners */
}

/* Scrollbar thumb hover */
.chat-messages::-webkit-scrollbar-thumb:hover {
  background: #a9a9a9;
}

/* Scrollbar track */
.chat-messages::-webkit-scrollbar-track {
  background: #f1f1f1; /* Background of the scrollbar track */
}

.chat-message {
  display: flex;
  align-items: center;
  margin-bottom: 16px;
}

.chat-message-center {
  justify-content: center;
}

.chat-message-center .centered-message-bubble {
  background: #f9f9fb;
  /* border-radius: 32px; */
  padding: 18px;
  width: 60%;
  cursor: pointer;
  border: solid 1px #d0e0ff;
  margin-bottom: 48px;
}

.chat-message-center .centered-message-bubble .message-text {
  color: #777777;
  font-size: 12px;
  text-align: center;
  margin-right: 36px;
  display: -webkit-box;
  /* ... */
  -webkit-line-clamp: 3; /* Number of lines to show */
  -webkit-box-orient: vertical;
  overflow: hidden;
  text-overflow: ellipsis;
  word-break: break-word; /* Ensures long words break appropriately */
}

.chat-message-left {
  justify-content: flex-start;
}

.chat-message-right {
  justify-content: flex-end;
}

.message-bubble {
  max-width: 60%;
  padding: 12px 16px;
  border-radius: 20px;
  position: relative;
  word-wrap: break-word;
}

.chat-message-left .message-bubble {
  background: #f9f9fb;
  border: 1px solid #f0f0f0;
  border-top-left-radius: 0;
}

.chat-message-right .message-bubble {
  border-top-right-radius: 0;
  background: #c6c6c6;
}

.message-text {
  font-size: 14px;
  color: #444444;
  /* ... */
  display: -webkit-box;
  -webkit-box-orient: vertical;
  -webkit-line-clamp: 7; /* Limit to 3 lines */
  overflow: hidden;
  text-overflow: ellipsis;
  line-height: 1.5; /* Adjust based on your font size */
  max-height: calc(1.5em * 8); /* line-height * number of lines */
}

.message-time {
  font-size: 12px;
  color: #a0a0a0;
  margin-top: 8px;
  text-align: right;
}

.chat-input {
  padding: 16px;
  border-top: 1px solid #f0f0f0;
}

.chat-input .ant-input {
  border-radius: 24px;
  padding: 8px 16px;
}

.chat-input .ant-input-prefix {
  margin-right: 8px;
}

.chat-input .ant-input-suffix {
  margin-left: 8px;
}

.chat-input .ant-btn {
  background: none;
  border: none;
  color: #1890ff;
}

.chat-input .ant-btn:disabled {
  color: rgba(0, 0, 0, 0.25);
}

.no-customer-selected {
  display: flex;
  justify-content: center;
  align-items: center;
  height: calc(100vh - 120px);
  color: #999;
  font-size: 16px;
}

.date-separator {
  text-align: center;
  margin: 10px 0;
  color: #888;
  font-size: 12px;
}