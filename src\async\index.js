import ProtectedLayout from "../layouts/protectedLayout";
import PublicLayout from "../layouts/publicLayout";
import Login from "../pages/login/login.container";
import Dashboard from "../pages/dashboard/dashboard.container";
import Channels from "../pages/channels/channels.container";
import ManageAgents from "../pages/manageAgents/manageAgents.container";
import UserRegistration from "../pages/userRegistration/userRegistration.container";
import Setup from "../pages/setup/setup.container";
import NotFoundPage from "../pages/notFoundPage/notFoundPage.container";
import PlayGround from "../pages/playground/playground.container";
import EditDoc from "../pages/editdoc/EditDoc.container";
import KnowledgeBase from "../pages/kbSetup/kbSetup.container";
import FileManager from "../pages/FileManager/FileManager.container";
import ResolveConflict from "../pages/resolveConflict/resolveConflict.container";
import FeedbackLog from "../pages/feedbackLog/feedbackLog.container";
import Cta from "../pages/cta/cta.container";
import Prompt from "../pages/prompt_edit/prompt.container";
import Settings from "../pages/settings/Settings.Container";
import LandingPage from "../pages/landingPage/landingPage.container";

export {
  ProtectedLayout,
  PublicLayout,
  Login,
  Dashboard,
  Channels,
  ManageAgents,
  UserRegistration,
  Setup,
  NotFoundPage,
  PlayGround,
  EditDoc,
  KnowledgeBase,
  FileManager,
  ResolveConflict,
  FeedbackLog,
  Cta,
  Prompt,
  Settings,
  LandingPage,
};
