import React from "react";
import {
  Form,
  Input,
  Select,
  Row,
  Col,
  Typography,
  Space,
  InputNumber,
  Radio
} from "antd";
import {
  BuildingOutlined,
  UserOutlined,
  MailOutlined,
  PhoneOutlined,
  TeamOutlined
} from "@ant-design/icons";
import StageNavigation from "./StageNavigation";

const { Title, Text } = Typography;
const { Option } = Select;
const { TextArea } = Input;

const Stage1Form = ({ initialData, onSubmit, loading }) => {
  const [form] = Form.useForm();

  const businessTypes = [
    { value: "ecommerce", label: "E-commerce" },
    { value: "saas", label: "SaaS/Technology" },
    { value: "service", label: "Service Business" },
    { value: "retail", label: "Retail" },
    { value: "healthcare", label: "Healthcare" },
    { value: "education", label: "Education" },
    { value: "finance", label: "Finance" },
    { value: "manufacturing", label: "Manufacturing" },
    { value: "other", label: "Other" }
  ];

  const industries = [
    { value: "technology", label: "Technology" },
    { value: "healthcare", label: "Healthcare" },
    { value: "finance", label: "Finance" },
    { value: "retail", label: "Retail" },
    { value: "education", label: "Education" },
    { value: "manufacturing", label: "Manufacturing" },
    { value: "consulting", label: "Consulting" },
    { value: "media", label: "Media & Entertainment" },
    { value: "travel", label: "Travel & Hospitality" },
    { value: "other", label: "Other" }
  ];

  const timezones = [
    { value: "UTC-8", label: "Pacific Time (UTC-8)" },
    { value: "UTC-7", label: "Mountain Time (UTC-7)" },
    { value: "UTC-6", label: "Central Time (UTC-6)" },
    { value: "UTC-5", label: "Eastern Time (UTC-5)" },
    { value: "UTC+0", label: "GMT (UTC+0)" },
    { value: "UTC+1", label: "Central European Time (UTC+1)" },
    { value: "UTC+5:30", label: "India Standard Time (UTC+5:30)" },
    { value: "UTC+8", label: "China Standard Time (UTC+8)" },
    { value: "UTC+9", label: "Japan Standard Time (UTC+9)" }
  ];

  const handleSubmit = (values) => {
    onSubmit(values);
  };

  return (
    <div className="stage1-form">
      <div className="stage1-form__header">
        <Title level={2} style={{ marginBottom: 8, color: '#1890ff' }}>
          Tell us about your company
        </Title>
        <Text type="secondary" style={{ fontSize: '16px' }}>
          This information helps us customize your experience
        </Text>
      </div>

      <Form
        form={form}
        layout="vertical"
        onFinish={handleSubmit}
        initialValues={initialData}
        size="large"
        style={{ marginTop: 32 }}
      >
        <Row gutter={[24, 0]}>
          {/* Company Information */}
          <Col span={24}>
            <Title level={4} style={{ marginBottom: 16, color: '#595959' }}>
              Company Information
            </Title>
          </Col>

          <Col xs={24} sm={12}>
            <Form.Item
              name="companyName"
              label="Company Name"
              rules={[
                { required: true, message: "Please enter your company name" }
              ]}
            >
              <Input
                prefix={<BuildingOutlined />}
                placeholder="Enter company name"
                style={{ borderRadius: 8 }}
              />
            </Form.Item>
          </Col>

          <Col xs={24} sm={12}>
            <Form.Item
              name="businessType"
              label="Business Type"
              rules={[
                { required: true, message: "Please select your business type" }
              ]}
            >
              <Select
                placeholder="Select business type"
                style={{ borderRadius: 8 }}
              >
                {businessTypes.map(type => (
                  <Option key={type.value} value={type.value}>
                    {type.label}
                  </Option>
                ))}
              </Select>
            </Form.Item>
          </Col>

          <Col xs={24} sm={12}>
            <Form.Item
              name="industry"
              label="Industry"
              rules={[
                { required: true, message: "Please select your industry" }
              ]}
            >
              <Select
                placeholder="Select industry"
                style={{ borderRadius: 8 }}
              >
                {industries.map(industry => (
                  <Option key={industry.value} value={industry.value}>
                    {industry.label}
                  </Option>
                ))}
              </Select>
            </Form.Item>
          </Col>

          <Col xs={24} sm={12}>
            <Form.Item
              name="teamSize"
              label="Team Size"
              rules={[
                { required: true, message: "Please enter your team size" }
              ]}
            >
              <InputNumber
                prefix={<TeamOutlined />}
                placeholder="Number of employees"
                style={{ width: '100%', borderRadius: 8 }}
                min={1}
                max={10000}
              />
            </Form.Item>
          </Col>

          {/* Contact Information */}
          <Col span={24} style={{ marginTop: 24 }}>
            <Title level={4} style={{ marginBottom: 16, color: '#595959' }}>
              Contact Information
            </Title>
          </Col>

          <Col xs={24} sm={12}>
            <Form.Item
              name="contactName"
              label="Primary Contact Name"
              rules={[
                { required: true, message: "Please enter contact name" }
              ]}
            >
              <Input
                prefix={<UserOutlined />}
                placeholder="Enter contact name"
                style={{ borderRadius: 8 }}
              />
            </Form.Item>
          </Col>

          <Col xs={24} sm={12}>
            <Form.Item
              name="contactEmail"
              label="Email Address"
              rules={[
                { required: true, message: "Please enter email address" },
                { type: "email", message: "Please enter a valid email" }
              ]}
            >
              <Input
                prefix={<MailOutlined />}
                placeholder="Enter email address"
                style={{ borderRadius: 8 }}
              />
            </Form.Item>
          </Col>

          <Col xs={24} sm={12}>
            <Form.Item
              name="contactPhone"
              label="Phone Number (Optional)"
            >
              <Input
                prefix={<PhoneOutlined />}
                placeholder="Enter phone number"
                style={{ borderRadius: 8 }}
              />
            </Form.Item>
          </Col>

          <Col xs={24} sm={12}>
            <Form.Item
              name="timezone"
              label="Timezone"
              rules={[
                { required: true, message: "Please select your timezone" }
              ]}
            >
              <Select
                placeholder="Select timezone"
                style={{ borderRadius: 8 }}
              >
                {timezones.map(tz => (
                  <Option key={tz.value} value={tz.value}>
                    {tz.label}
                  </Option>
                ))}
              </Select>
            </Form.Item>
          </Col>

          {/* Preferences */}
          <Col span={24} style={{ marginTop: 24 }}>
            <Title level={4} style={{ marginBottom: 16, color: '#595959' }}>
              Initial Preferences
            </Title>
          </Col>

          <Col span={24}>
            <Form.Item
              name="primaryGoal"
              label="What's your primary goal with Echo AI?"
              rules={[
                { required: true, message: "Please select your primary goal" }
              ]}
            >
              <Radio.Group>
                <Space direction="vertical">
                  <Radio value="reduce_response_time">Reduce response time</Radio>
                  <Radio value="improve_satisfaction">Improve customer satisfaction</Radio>
                  <Radio value="scale_support">Scale support operations</Radio>
                  <Radio value="reduce_costs">Reduce support costs</Radio>
                  <Radio value="24_7_availability">Provide 24/7 availability</Radio>
                </Space>
              </Radio.Group>
            </Form.Item>
          </Col>

          <Col span={24}>
            <Form.Item
              name="description"
              label="Brief Description (Optional)"
            >
              <TextArea
                placeholder="Tell us more about your business or specific needs..."
                rows={3}
                style={{ borderRadius: 8 }}
              />
            </Form.Item>
          </Col>

          {/* Submit Button */}
          <Col span={24} style={{ marginTop: 32 }}>
            <Form.Item>
              <Button
                type="primary"
                htmlType="submit"
                loading={loading}
                size="large"
                style={{
                  width: '100%',
                  height: 48,
                  borderRadius: 8,
                  fontSize: 16,
                  fontWeight: 600
                }}
              >
                Continue to Next Step
              </Button>
            </Form.Item>
          </Col>
        </Row>
      </Form>
    </div>
  );
};

export default Stage1Form;
