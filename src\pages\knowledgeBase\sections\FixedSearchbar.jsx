import React, { useState } from "react";
import { Input, Button, message } from "antd";
import { SendOutlined } from "@ant-design/icons";
import { useAppDispatch } from "../../../hooks/reduxHooks";
import {
  filterDocumentsApi,
  simulateDocumentsApi,
} from "../../../services/knowledgebase.service";
import SimulateButton from "./SimulateButton";
import SelectOption from "../../../components/common/selectOption";

const { TextArea } = Input;

const FixedSearchbar = ({
  endpointResponse,
  setEndpointResponse,
  simulateData,
  setSimulateData,
}) => {
  const dispatch = useAppDispatch();
  const [isHighlighted, setIsHighlighted] = useState(false);
  const [value, setValue] = useState("");
  const [loading, setLoading] = useState(false);
  const [simulateQuery, setSimulateQuery] = useState();
  const [selectedLevel, setSelectedLevel] = useState("level-1");

  const handleSubmit = (e) => {
    if (e.key === "Enter" && !e.shiftKey) {
      e.preventDefault();
      // ...
      setLoading(true);
      if (isHighlighted) {
        dispatch(
          simulateDocumentsApi({
            data: {
              query: value,
              level: selectedLevel,
            },
            finalCallback: () => {
              setLoading(false);
            },
            successCallback: (response) => {
              setSimulateData(response);
              setSimulateQuery(value);
              // setEndpointResponse(response?.source_nodes);
              // ...
              setValue("");
            },
            failureCallback: () => {
              message.error("Something went wrong!");
            },
          })
        );
      } else {
        dispatch(
          filterDocumentsApi({
            data: { query: value },
            finalCallback: () => {
              setLoading(false);
            },
            successCallback: (response) => {
              setEndpointResponse(response);
              // ...
              setValue("");
            },
            failureCallback: () => {
              message.error("Something went wrong!");
            },
          })
        );
      }
    }
  };

  const handleSubmitButton = () => {
    setLoading(true);
    if (isHighlighted) {
      dispatch(
        simulateDocumentsApi({
          data: {
            query: value,
            level: selectedLevel,
          },
          finalCallback: () => {
            setLoading(false);
          },
          successCallback: (response) => {
            setSimulateData(response);
            setSimulateQuery(value);

            // setEndpointResponse(response?.source_nodes);
            // ...
            setValue("");
          },
          failureCallback: () => {
            message.error("Something went wrong!");
          },
        })
      );
    } else {
      dispatch(
        filterDocumentsApi({
          data: { query: value },
          finalCallback: () => {
            setLoading(false);
          },
          successCallback: (response) => {
            setEndpointResponse(response);
            // ...
            setValue("");
          },
          failureCallback: () => {
            message.error("Something went wrong!");
          },
        })
      );
    }
  };

  return (
    <div
      style={{
        display: "flex",
        justifyContent: "center",
      }}
    >
      <div
        style={{
          position: "fixed",
          bottom: 0,
          padding: "20px",
          backgroundColor: "#fff",
          border: "1px solid #f0f0f0",
          zIndex: 1000,
          minWidth: "900px",
          marginBottom: "12px",
          borderRadius: "12px",
        }}
      >
        <div
          style={{
            display: "flex",
            gap: "12px",
          }}
        >
          <TextArea
            value={value}
            onChange={(e) => setValue(e.target.value)}
            placeholder="Search..."
            autoSize={{ minRows: 1, maxRows: 6 }}
            style={{
              borderRadius: "8px",
              resize: "none",
            }}
            onKeyDown={handleSubmit}
            disabled={!endpointResponse}
          />
          <div
            style={{
              display: "flex",
              justifyContent: "center",
              alignItems: "center",
            }}
          >
            <Button
              type="primary"
              icon={<SendOutlined />}
              onClick={handleSubmitButton}
              disabled={!value.trim()}
              loading={loading}
            />
          </div>
        </div>
        <div
          style={{
            marginTop: "8px",
            display: "flex",
            alignItems: "center",
            gap: "18px",
          }}
        >
          <SimulateButton
            isHighlighted={isHighlighted}
            setIsHighlighted={setIsHighlighted}
          />
          {isHighlighted && (
            <SelectOption
              defaultValue="level-1"
              options={[
                { value: "level-1", label: "Level 1" },
                { value: "level-2", label: "Level 2" },
                { value: "level-3", label: "Level 3" },
              ]}
              style={{ width: "120px" }}
              onChange={(value) => {
                setSelectedLevel(value);
              }}
            />
          )}
        </div>
      </div>
    </div>
  );
};

export default FixedSearchbar;
