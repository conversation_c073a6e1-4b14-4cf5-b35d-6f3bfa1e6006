import React, { useEffect, useState } from "react";
import { Layout, message } from "antd";
import {
  fetch_prompt_api,
  update_prompt_api,
} from "../../services/prompt.service";
import { useAppDispatch } from "../../hooks/reduxHooks";
import { useNavigate } from "react-router-dom";
import PromptEditor from "./sections/promptEditor";

function PromptComponent() {
  const dispatch = useAppDispatch();
  const navigate = useNavigate();
  const [prompts, setPrompts] = useState([]);
  const [loadingUpdate, setLoadingUpdate] = useState(false);

  const fetchPrompts = () => {
    dispatch(
      fetch_prompt_api({
        successCallback: (data) => {
          // Ensure data is an array
          setPrompts(Array.isArray(data) ? data : []);
        },
        failureCallback: (error) => {
          console.error("Failed to fetch prompts:", error);
          setPrompts([]); // Set empty array on error
        },
        finalCallback: () => {},
      })
    );
  };

  const updatePrompt = (data) => {
    setLoadingUpdate(true);
    dispatch(
      update_prompt_api({
        data: data,
        successCallback: (response) => {
          // Ensure data is an array
          setPrompts(Array.isArray(response) ? response : []);
          message.success("Prompt saved successfully");
          fetchPrompts();
        },
        failureCallback: (error) => {
          console.error("Failed to update prompt:", error);
          message.error("Failed to update prompt");
        },
        finalCallback: () => {
          setLoadingUpdate(false);
        },
      })
    );
  };

  useEffect(() => {
    fetchPrompts();
  }, []);

  if (localStorage.getItem("username") !== "superadmin") {
    navigate("/");
  }

  return (
    <Layout>
      <PromptEditor
        promptsData={prompts}
        updatePrompt={updatePrompt}
        loadingUpdate={loadingUpdate}
      />
    </Layout>
  );
}

export default PromptComponent;
