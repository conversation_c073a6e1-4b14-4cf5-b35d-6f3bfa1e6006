import {
  Form,
  Input,
  Select,
  <PERSON><PERSON>,
  Row,
  Col,
  Typo<PERSON>,
  Space,
  Card,
} from "antd";
import {
  BankOutlined,
  PhoneOutlined,
  ArrowRightOutlined,
} from "@ant-design/icons";

const { Title, Text, Paragraph } = Typography;
const { Option } = Select;
const { TextArea } = Input;

const Stage1BusinessInfo = ({ initialData, staticData, onSubmit, loading }) => {
  const [form] = Form.useForm();

  const handleSubmit = (values) => {
    onSubmit(values);
  };

  return (
    <div className="stage1-business-info">
      <div className="stage1-business-info__header">
        <Title
          level={2}
          style={{ marginBottom: 8, color: "#262626", textAlign: "center" }}
        >
          Tell Us About Your Business
        </Title>
        <Paragraph
          style={{
            textAlign: "center",
            fontSize: "15px",
            color: "#8c8c8c",
            marginBottom: 24,
          }}
        >
          Help us customize your AI assistant by sharing some basic information
          about your business
        </Paragraph>
      </div>

      <Form
        form={form}
        layout="vertical"
        onFinish={handleSubmit}
        initialValues={initialData}
        size="large"
        requiredMark={false}
      >
        {/* Business Information Section */}
        <Card
          title={
            <Space>
              <BankOutlined style={{ color: "#1890ff" }} />
              <Text strong>Business Information</Text>
            </Space>
          }
          className="form-section-card"
          style={{ marginBottom: 24 }}
        >
          <Row gutter={[24, 16]}>
            <Col xs={24} sm={12}>
              <Form.Item
                name="businessName"
                label="Business Name"
                rules={[
                  {
                    required: true,
                    message: "Please enter your business name",
                  },
                ]}
              >
                <Input
                  prefix={<BankOutlined style={{ color: "#8c8c8c" }} />}
                  placeholder="Enter your business name"
                  className="professional-input"
                />
              </Form.Item>
            </Col>

            <Col xs={24} sm={12}>
              <Form.Item
                name="businessType"
                label="Business Type"
                rules={[
                  {
                    required: true,
                    message: "Please select your business type",
                  },
                ]}
              >
                <Select
                  placeholder="Select business type"
                  className="professional-select"
                >
                  {staticData.businessTypes.map((type) => (
                    <Option key={type.value} value={type.value}>
                      {type.label}
                    </Option>
                  ))}
                </Select>
              </Form.Item>
            </Col>

            <Col xs={24}>
              <Form.Item
                name="businessDescription"
                label="Business Description"
                rules={[
                  { required: true, message: "Please describe your business" },
                ]}
              >
                <TextArea
                  placeholder="Briefly describe what your business does..."
                  rows={3}
                  className="professional-textarea"
                />
              </Form.Item>
            </Col>

            <Col xs={24} sm={12}>
              <Form.Item
                name="contactNumber"
                label="Contact Number"
                rules={[
                  {
                    required: true,
                    message: "Please enter your contact number",
                  },
                  {
                    pattern: /^[+]?[\d\s\-\(\)]+$/,
                    message: "Please enter a valid contact number",
                  },
                ]}
              >
                <Input
                  prefix={<PhoneOutlined style={{ color: "#8c8c8c" }} />}
                  placeholder="Enter contact number"
                  className="professional-input"
                />
              </Form.Item>
            </Col>
          </Row>
        </Card>

        {/* Submit Button */}
        <div style={{ textAlign: "center" }}>
          <Button
            type="primary"
            htmlType="submit"
            loading={loading}
            size="large"
            icon={<ArrowRightOutlined />}
            className="professional-button"
            style={{
              height: "40px",
              paddingLeft: "24px",
              paddingRight: "24px",
              fontSize: "14px",
              fontWeight: "500",
              borderRadius: "6px",
            }}
          >
            Continue to AI Goal Setup
          </Button>
        </div>
      </Form>
    </div>
  );
};

export default Stage1BusinessInfo;
