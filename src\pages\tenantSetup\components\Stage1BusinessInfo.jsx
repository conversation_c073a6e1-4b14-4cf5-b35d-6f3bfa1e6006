import {
  Form,
  Input,
  Select,
  But<PERSON>,
  Row,
  Col,
  Typography,
  Space,
  Divider,
  Card,
} from "antd";
import {
  BankOutlined,
  PhoneOutlined,
  AimOutlined,
  GlobalOutlined,
  ThunderboltOutlined,
  QuestionCircleOutlined,
  ArrowRightOutlined,
} from "@ant-design/icons";

const { Title, Text, Paragraph } = Typography;
const { Option } = Select;
const { TextArea } = Input;

const Stage1BusinessInfo = ({ initialData, staticData, onSubmit, loading }) => {
  const [form] = Form.useForm();

  const handleSubmit = (values) => {
    onSubmit(values);
  };

  return (
    <div className="stage1-business-info">
      <div className="stage1-business-info__header">
        <Title
          level={2}
          style={{ marginBottom: 8, color: "#262626", textAlign: "center" }}
        >
          Tell Us About Your Business
        </Title>
        <Paragraph
          style={{
            textAlign: "center",
            fontSize: "15px",
            color: "#8c8c8c",
            marginBottom: 24,
          }}
        >
          Help us customize your AI assistant by sharing some basic information
          about your business
        </Paragraph>
      </div>

      <Form
        form={form}
        layout="vertical"
        onFinish={handleSubmit}
        initialValues={initialData}
        size="large"
        requiredMark={false}
      >
        {/* Business Information Section */}
        <Card
          title={
            <Space>
              <BankOutlined style={{ color: "#1890ff" }} />
              <Text strong>Business Information</Text>
            </Space>
          }
          className="form-section-card"
          style={{ marginBottom: 24 }}
        >
          <Row gutter={[24, 16]}>
            <Col xs={24} sm={12}>
              <Form.Item
                name="businessName"
                label="Business Name"
                rules={[
                  {
                    required: true,
                    message: "Please enter your business name",
                  },
                ]}
              >
                <Input
                  prefix={<BankOutlined style={{ color: "#8c8c8c" }} />}
                  placeholder="Enter your business name"
                  className="professional-input"
                />
              </Form.Item>
            </Col>

            <Col xs={24} sm={12}>
              <Form.Item
                name="businessType"
                label="Business Type"
                rules={[
                  {
                    required: true,
                    message: "Please select your business type",
                  },
                ]}
              >
                <Select
                  placeholder="Select business type"
                  className="professional-select"
                >
                  {staticData.businessTypes.map((type) => (
                    <Option key={type.value} value={type.value}>
                      {type.label}
                    </Option>
                  ))}
                </Select>
              </Form.Item>
            </Col>

            <Col xs={24}>
              <Form.Item
                name="businessDescription"
                label="Business Description"
                rules={[
                  { required: true, message: "Please describe your business" },
                ]}
              >
                <TextArea
                  placeholder="Briefly describe what your business does..."
                  rows={3}
                  className="professional-textarea"
                />
              </Form.Item>
            </Col>

            <Col xs={24} sm={12}>
              <Form.Item
                name="contactNumber"
                label="Contact Number"
                rules={[
                  {
                    required: true,
                    message: "Please enter your contact number",
                  },
                  {
                    pattern: /^[+]?[\d\s\-\(\)]+$/,
                    message: "Please enter a valid contact number",
                  },
                ]}
              >
                <Input
                  prefix={<PhoneOutlined style={{ color: "#8c8c8c" }} />}
                  placeholder="Enter contact number"
                  className="professional-input"
                />
              </Form.Item>
            </Col>
          </Row>
        </Card>

        {/* Business Goal Section */}
        <Card
          title={
            <Space>
              <AimOutlined style={{ color: "#52c41a" }} />
              <Text strong>Business Goal</Text>
            </Space>
          }
          className="form-section-card"
          style={{ marginBottom: 24 }}
        >
          <Row gutter={[24, 16]}>
            <Col xs={24}>
              <Form.Item
                name="businessGoal"
                label="What's your primary business goal?"
                rules={[
                  {
                    required: true,
                    message: "Please select your business goal",
                  },
                ]}
              >
                <Select
                  placeholder="Select your primary goal"
                  className="professional-select"
                >
                  {staticData.businessGoals.map((goal) => (
                    <Option key={goal.value} value={goal.value}>
                      {goal.label}
                    </Option>
                  ))}
                </Select>
              </Form.Item>
            </Col>
          </Row>
        </Card>

        {/* Languages & Call to Actions Section */}
        <Card
          title={
            <Space>
              <GlobalOutlined style={{ color: "#722ed1" }} />
              <Text strong>Preferences</Text>
            </Space>
          }
          className="form-section-card"
          style={{ marginBottom: 24 }}
        >
          <Row gutter={[24, 16]}>
            <Col xs={24} sm={12}>
              <Form.Item
                name="languages"
                label="Preferred Languages"
                rules={[{ required: true, message: "Please select languages" }]}
              >
                <Select
                  mode="multiple"
                  placeholder="Select languages"
                  className="professional-select"
                >
                  {staticData.languages.map((lang) => (
                    <Option key={lang.value} value={lang.value}>
                      {lang.label}
                    </Option>
                  ))}
                </Select>
              </Form.Item>
            </Col>

            <Col xs={24} sm={12}>
              <Form.Item
                name="callToActions"
                label="Call to Actions"
                rules={[
                  { required: true, message: "Please select call to actions" },
                ]}
              >
                <Select
                  mode="multiple"
                  placeholder="Select call to actions"
                  className="professional-select"
                >
                  {staticData.callToActions.map((cta) => (
                    <Option key={cta.value} value={cta.value}>
                      {cta.label}
                    </Option>
                  ))}
                </Select>
              </Form.Item>
            </Col>
          </Row>
        </Card>

        {/* User Question Section */}
        <Card
          title={
            <Space>
              <QuestionCircleOutlined style={{ color: "#fa8c16" }} />
              <Text strong>Ask a Question</Text>
            </Space>
          }
          className="form-section-card"
          style={{ marginBottom: 32 }}
        >
          <Row gutter={[24, 16]}>
            <Col xs={24}>
              <Form.Item
                name="userQuestion"
                label="What would you like to know about our AI assistant?"
                rules={[{ required: true, message: "Please ask a question" }]}
              >
                <TextArea
                  placeholder="Ask any question about our AI assistant, features, or how it can help your business..."
                  rows={4}
                  className="professional-textarea"
                />
              </Form.Item>
            </Col>
          </Row>
        </Card>

        {/* Submit Button */}
        <div style={{ textAlign: "center" }}>
          <Button
            type="primary"
            htmlType="submit"
            loading={loading}
            size="large"
            icon={<ArrowRightOutlined />}
            className="professional-button"
            style={{
              height: "40px",
              paddingLeft: "24px",
              paddingRight: "24px",
              fontSize: "14px",
              fontWeight: "500",
              borderRadius: "6px",
            }}
          >
            Continue to AI Response Selection
          </Button>
        </div>
      </Form>
    </div>
  );
};

export default Stage1BusinessInfo;
