import { useState } from "react";
import {
  Form,
  Input,
  Select,
  Button,
  Row,
  Col,
  Typography,
  Space,
  Card,
} from "antd";
import { BankOutlined, ArrowRightOutlined } from "@ant-design/icons";
import PhoneInput from "react-phone-input-2";
import { isPossiblePhoneNumber } from "react-phone-number-input";
import "react-phone-input-2/lib/style.css";

const { Title, Text, Paragraph } = Typography;
const { Option } = Select;
const { TextArea } = Input;

const Stage1BusinessInfo = ({ initialData, staticData, onSubmit, loading }) => {
  const [form] = Form.useForm();
  const [phoneError, setPhoneError] = useState("");

  const handleSubmit = (values) => {
    // Validate phone number
    const isPhoneNumberValid = isPossiblePhoneNumber(
      values.country_code + values.phone_number
    );

    if (!isPhoneNumberValid) {
      setPhoneError("Please enter a valid phone number");
      return;
    }

    setPhoneError("");
    onSubmit(values);
  };

  return (
    <div className="stage1-business-info">
      <div className="stage1-business-info__header">
        <Title
          level={2}
          style={{ marginBottom: 8, color: "#262626", textAlign: "center" }}
        >
          Tell Us About Your Business
        </Title>
        <Paragraph
          style={{
            textAlign: "center",
            fontSize: "15px",
            color: "#8c8c8c",
            marginBottom: 24,
          }}
        >
          Help us customize your AI assistant by sharing some basic information
          about your business
        </Paragraph>
      </div>

      <Form
        form={form}
        layout="vertical"
        onFinish={handleSubmit}
        initialValues={{
          ...initialData,
          country_code: "",
          phone_number: "",
          phone_input: "",
        }}
        size="large"
        requiredMark={false}
      >
        {/* Business Information Section */}
        <Card
          title={
            <Space>
              <BankOutlined style={{ color: "#1890ff" }} />
              <Text strong>Business Information</Text>
            </Space>
          }
          className="form-section-card"
          style={{ marginBottom: 24 }}
        >
          <Row gutter={[24, 16]}>
            <Col xs={24} sm={12}>
              <Form.Item
                name="businessName"
                label="Business Name"
                rules={[
                  {
                    required: true,
                    message: "Please enter your business name",
                  },
                ]}
              >
                <Input
                  prefix={<BankOutlined style={{ color: "#8c8c8c" }} />}
                  placeholder="Enter your business name"
                  className="professional-input"
                />
              </Form.Item>
            </Col>

            <Col xs={24} sm={12}>
              <Form.Item
                name="businessType"
                label="Business Type"
                rules={[
                  {
                    required: true,
                    message: "Please select at least one business type",
                  },
                ]}
              >
                <Select
                  // mode="multiple"
                  placeholder="Select business types"
                  className="professional-select"
                >
                  {staticData.businessTypes.map((type) => (
                    <Option key={type.value} value={type.value}>
                      {type.label}
                    </Option>
                  ))}
                </Select>
              </Form.Item>
            </Col>

            <Col xs={24}>
              <Form.Item
                name="businessDescription"
                label="Business Description"
                rules={[
                  { required: true, message: "Please describe your business" },
                ]}
              >
                <TextArea
                  placeholder="Briefly describe what your business does..."
                  rows={3}
                  className="professional-textarea"
                />
              </Form.Item>
            </Col>

            <Col xs={24} sm={12}>
              <Form.Item
                name="phone_input"
                label="Contact Number"
                validateStatus={phoneError ? "error" : ""}
                help={phoneError || ""}
              >
                <PhoneInput
                  country={"np"}
                  enableSearch={true}
                  inputClass="form-input"
                  containerClass="phone-input-container"
                  buttonClass="country-dropdown"
                  searchClass="country-search"
                  dropdownClass="country-dropdown-list"
                  value={form.getFieldValue("phone_input")}
                  inputStyle={{
                    width: "100%",
                    height: "40px",
                    fontSize: "14px",
                    fontWeight: "400",
                    borderRadius: "0 8px 8px 0",
                  }}
                  buttonStyle={{
                    borderRadius: "8px 0 0 8px",
                    borderRight: "none",
                    backgroundColor: "#fafafa",
                  }}
                  onChange={(phone, countryData) => {
                    setPhoneError("");
                    const countryCode = countryData.dialCode
                      ? `+${countryData.dialCode}`
                      : "";
                    const fullNumber = phone;
                    const phoneNumberOnly = countryData.dialCode
                      ? fullNumber.replace(countryData.dialCode, "")
                      : fullNumber;
                    const cleanedPhoneNumber = phoneNumberOnly.replace(
                      /\D/g,
                      ""
                    );

                    form.setFieldsValue({
                      country_code: countryCode,
                      phone_number: cleanedPhoneNumber,
                      phone_input: phone,
                    });
                  }}
                  preferredCountries={["in", "us", "np", "gb"]}
                  autoFormat={true}
                  placeholder="Enter contact number"
                  disableSearchIcon={false}
                  searchPlaceholder="Search country"
                />
              </Form.Item>

              {/* Hidden fields for country code and phone number */}
              <Form.Item name="country_code" hidden={true}>
                <Input />
              </Form.Item>

              <Form.Item name="phone_number" hidden={true}>
                <Input />
              </Form.Item>
            </Col>
          </Row>
        </Card>

        {/* Submit Button */}
        <div style={{ textAlign: "center" }}>
          <Button
            type="primary"
            htmlType="submit"
            loading={loading}
            size="large"
            icon={<ArrowRightOutlined />}
            className="professional-button"
            style={{
              height: "40px",
              paddingLeft: "24px",
              paddingRight: "24px",
              fontSize: "14px",
              fontWeight: "500",
              borderRadius: "6px",
            }}
          >
            Continue to AI Setup
          </Button>
        </div>
      </Form>
    </div>
  );
};

export default Stage1BusinessInfo;
