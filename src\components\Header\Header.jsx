import { Layout, Tag } from "antd";
import { useNavigate } from "react-router-dom";
import { useAppDispatch, useAppSelector } from "../../hooks/reduxHooks";
import { getAIStatusApi } from "../../services/aiStatus.service";
import { useEffect } from "react";
import { storeAiStatus } from "../../store/slices/profile.slice";
import logoImg from "../../assets/images/eko-favicon.png";

const { Header } = Layout;

const AppHeader = () => {
  const navigate = useNavigate();
  const tenant_label = localStorage.getItem("tenant_label");
  const dispatch = useAppDispatch();

  const { aiStatus } = useAppSelector((state) => state.profile);

  useEffect(() => {
    dispatch(
      getAIStatusApi({
        finalCallback: () => {},
        successCallback: (response) => {
          dispatch(storeAiStatus(response?.is_currently_enabled));
        },
        failureCallback: () => {},
      })
    );
  }, []);

  return (
    <Header className="protected-header">
      <div
        style={{
          display: "flex",
          flexDirection: "row",
          justifyContent: "center",
          alignItems: "center",
        }}
        className="logo"
        onClick={() => {
          navigate("/dashboard");
        }}
      >
        <img
          src={logoImg}
          alt="Next AI"
          style={{ marginTop: "5px", padding: 8 }}
        />
        <span>
          EKO
          {tenant_label && (
            <Tag
              color="blue"
              style={{
                fontSize: "12px",
                padding: "0px 7px",
                marginRight: "0px",
                verticalAlign: "super",
              }}
            >
              {tenant_label}
            </Tag>
          )}
        </span>
      </div>

      {aiStatus ? (
        <div
          style={{
            display: "flex",
            alignItems: "center",
            cursor: localStorage.getItem("role") === "admin" ? "pointer" : null,
          }}
          onClick={() => {
            if (localStorage.getItem("role") === "admin") {
              navigate("/settings?tab=ai-settings");
            }
          }}
        >
          <div
            style={{
              width: 8,
              height: 8,
              backgroundColor: "#52c41a", // Green dot
              borderRadius: "50%",
            }}
          />

          <span
            style={{
              marginLeft: 8,
              fontWeight: "500",
              color: "#777777",
              fontSize: 12,
            }}
          >
            Live
          </span>
        </div>
      ) : (
        <div
          style={{ display: "flex", alignItems: "center", cursor: "pointer" }}
          onClick={() => {
            if (localStorage.getItem("role") === "admin") {
              navigate("/settings?tab=ai-settings");
            }
          }}
        >
          <div
            style={{
              width: 8,
              height: 8,
              backgroundColor: "#bfbfbf", // Gray dot
              borderRadius: "50%",
            }}
          />

          <span
            style={{
              marginLeft: 8,
              fontWeight: "500",
              color: "#bfbfbf",
              fontSize: 12,
            }}
          >
            Offline
          </span>
        </div>
      )}
    </Header>
  );
};

export default AppHeader;
