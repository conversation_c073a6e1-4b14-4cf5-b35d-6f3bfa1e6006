import React, { useEffect, useRef } from "react";
import * as echarts from "echarts";
import { useNavigate } from "react-router-dom";
import { Empty, Space, Typography } from "antd";

export default function CtaTopics({ data, topicCount }) {
  const navigate = useNavigate();
  const chartRef = useRef(null);

  const aggregateTopicCounts = (originalData, dailyData) => {
    const result = JSON.parse(JSON.stringify(originalData));

    for (const date in dailyData) {
      if (dailyData.hasOwnProperty(date)) {
        const topics = dailyData[date].topics;

        topics.forEach((dailyTopic) => {
          const matchingTopic = result.find(
            (topic) => topic.topic === dailyTopic.topic
          );
          if (matchingTopic) {
            matchingTopic.count += dailyTopic.count;
            if (dailyTopic.sub_topics) {
              if (!matchingTopic.sub_topics) {
                matchingTopic.sub_topics = [];
              }
              dailyTopic.sub_topics.forEach((dailySubtopic) => {
                if (dailySubtopic.name) {
                  const matchingSubtopic = matchingTopic.sub_topics.find(
                    (sub) => sub.name === dailySubtopic.name
                  );
                  if (matchingSubtopic) {
                    matchingSubtopic.count += dailySubtopic.count;
                  } else {
                    matchingTopic.sub_topics.push({ ...dailySubtopic });
                  }
                }
              });
            }
          }
        });
      }
    }

    return result;
  };

  useEffect(() => {
    if (!chartRef.current || !data || data.length === 0) return;

    const aggregatedData = aggregateTopicCounts(data, topicCount);
    const topics = aggregatedData.map((item) => item.topic.trim());
    const counts = aggregatedData.map((item) => item.count);

    const chart = echarts.init(chartRef.current);

    const option = {
      title: {
        text: "Conversation Topics",
        left: "center",
        textStyle: {
          fontSize: 18,
          fontWeight: "600",
          color: "#0D47A1",
          fontFamily: "Arial, sans-serif",
        },
        padding: [10, 0, 20, 0],
      },
      tooltip: {
        trigger: "axis",
        axisPointer: {
          type: "shadow",
        },
        formatter: (params) => {
          const topic = params[0].name;
          const count = params[0].value;
          const topicData = aggregatedData.find(
            (item) => item.topic.trim() === topic
          );
          let tooltipContent = `<div style="font-size: 14px; color: #2d3436;">
            <strong>${topic}</strong><br/>
            Total Count: <b>${count}</b><br/>`;

          if (
            topicData?.sub_topics?.length &&
            topicData.sub_topics.some((sub) => sub.name)
          ) {
            tooltipContent += "<br/><strong>Sub Topics</strong><br/>";
            topicData.sub_topics.forEach((subtopic) => {
              if (subtopic.name) {
                tooltipContent += `${subtopic.name}: <b>${subtopic.count}</b><br/>`;
              }
            });
          }

          tooltipContent += "</div>";
          return tooltipContent;
        },
        backgroundColor: "rgba(255,255,255,0.95)",
        borderColor: "#dfe6e9",
        borderWidth: 1,
        padding: [8, 12],
        textStyle: {
          color: "#2d3436",
        },
      },
      grid: {
        left: "3%",
        right: "3%",
        top: "20%",
        bottom: "3%",
        containLabel: true,
      },
      xAxis: {
        type: "category",
        data: topics,
        axisLabel: {
          color: "#636e72",
          fontSize: 12,
          rotate: 30,
          margin: 15,
          interval: 0,
          formatter: (value) => {
            return value.length > 15 ? value.substring(0, 15) + "..." : value;
          },
        },
        axisLine: {
          lineStyle: {
            color: "#dfe6e9",
          },
        },
        axisTick: {
          alignWithLabel: true,
          lineStyle: {
            color: "#dfe6e9",
          },
        },
      },
      yAxis: {
        type: "value",
        axisLabel: {
          color: "#636e72",
          fontSize: 12,
        },
        splitLine: {
          lineStyle: {
            color: "#f5f6fa",
            type: "dashed",
          },
        },
        axisLine: {
          show: false,
        },
        axisTick: {
          show: false,
        },
      },
      series: [
        {
          name: "Count",
          type: "bar",
          barWidth: "40%",
          data: counts.map((count, index) => ({
            value: count,
            itemStyle: {
              borderRadius: [4, 4, 0, 0],
            },
          })),
          label: {
            show: true,
            position: "top",
            color: "#2d3436",
            fontWeight: "bold",
            fontSize: 12,
            formatter: "{c}",
          },
          emphasis: {
            itemStyle: {
              shadowBlur: 10,
              shadowOffsetX: 0,
              shadowColor: "rgba(0, 0, 0, 0.2)",
            },
          },
        },
      ],
    };

    chart.setOption(option);
    chart.on("click", "series.bar", (params) => {
      navigate("/channels", {
        state: {
          topic: params.name,
        },
      });
    });

    const handleResize = () => chart.resize();
    window.addEventListener("resize", handleResize);

    return () => {
      chart.dispose();
      window.removeEventListener("resize", handleResize);
    };
  }, [data, topicCount]);

  return (
    <div
      style={{
        width: "100%",
        background: "#fff",
        borderRadius: "12px",
        boxShadow: "0 4px 12px rgba(0, 0, 0, 0.05)",
        padding: "16px",
        marginTop: "24px",
      }}
    >
      {data.length > 0 ? (
        <div
          ref={chartRef}
          style={{
            width: "100%",
            height: "400px",
          }}
        />
      ) : (
        <div
          style={{
            height: "100%",
            display: "flex",
            alignItems: "center",
            justifyContent: "center",
          }}
        >
          <Empty
            image={Empty.PRESENTED_IMAGE_SIMPLE}
            description={
              <Space direction="vertical" size="small">
                <Typography.Text type="secondary">
                  No topics to show.
                </Typography.Text>
              </Space>
            }
          />
        </div>
      )}
    </div>
  );
}
