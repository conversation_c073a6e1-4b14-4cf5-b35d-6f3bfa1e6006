// src/hooks/useConversations.js
import { useState, useEffect } from 'react';
import { fetchConversations } from '../utils/conversations';
import conversationsData from '../data/conversations.json';

const useConversations = (
  channel,
  dateRange,
  searchTerm,
  sortOption,
  useDummyData = true
) => {
  const [conversations, setConversations] = useState([]);
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    const getConversations = async () => {
      setLoading(true);
      try {
        let data;
        if (useDummyData) {
          data = conversationsData[channel] || [];
        } else {
          data = await fetchConversations(channel);
        }

        // Apply search filter
        if (searchTerm) {
          data = data.filter((customer) =>
            customer.name.toLowerCase().includes(searchTerm.toLowerCase())
          );
        }

        // Apply date range filter
        if (dateRange && dateRange.length === 2) {
          const [startDate, endDate] = dateRange;
          data = data.filter((customer) =>
            customer.messages.some((message) => {
              const messageDate = new Date(message.timestamp);
              return (
                messageDate >= startDate.startOf('day') &&
                messageDate <= endDate.endOf('day')
              );
            })
          );
        }

        // Apply sorting
        if (sortOption === 'asc') {
          data.sort((a, b) => a.name.localeCompare(b.name));
        } else if (sortOption === 'desc') {
          data.sort((a, b) => b.name.localeCompare(a.name));
        }

        setConversations(data);
      } catch (error) {
        console.error('Error fetching conversations:', error);
      } finally {
        setLoading(false);
      }
    };

    getConversations();
  }, [channel, dateRange, searchTerm, sortOption, useDummyData]);

  return { conversations, loading };
};

export default useConversations;
