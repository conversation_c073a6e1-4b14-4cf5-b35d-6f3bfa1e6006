import React, { useEffect, useState } from "react";
import { <PERSON><PERSON>, <PERSON>, message, Button, Col, Row } from "antd";
import EditableParagraph from "../editableParagraph";
import { useAppDispatch } from "../../hooks/reduxHooks";
import { messageVerificationApi } from "../../services/channels.service";

const CtaModal = ({
  isModalOpen,
  setIsModalOpen,
  customerMetaData,
  messageId,
  ctaMediaURIs,
  ctaQuestion,
  selectedCustomerId,
  fetchCustomerMessagesById,
  textToBeVerified,
  setTextToBeVerified,
}) => {
  const dispatch = useAppDispatch();

  // CTA Modal Edit btn
  const [isParagraphEdited, setIsParagraphEdited] = useState(false);
  const [initialContent, setInitialContent] = useState(textToBeVerified); // Keeps the original content for comparison

  const handleOnOk = () => {
    dispatch(
      messageVerificationApi({
        data: {
          response_id: messageId,
          is_edited: isParagraphEdited,
          verified: true,
          verified_answer: textToBeVerified,
          verified_by: "admin",
          user_id: customerMetaData?.customer_id,
        },
        finalCallback: () => {
          setIsModalOpen(false);
        },
        successCallback: (response) => {
          if (response) {
            message.success("Message Verified Successfully!");
            fetchCustomerMessagesById(selectedCustomerId);
          } else {
            message.error("Message is not Verified!, Something went wrong.");
          }
        },
        failureCallback: (errors) => {},
      })
    );
  };

  return (
    <>
      <Modal
        open={isModalOpen}
        onCancel={() => {
          setIsParagraphEdited(false);
          setTextToBeVerified(initialContent);
          setIsModalOpen(false);
        }}
        centered
        width={ctaMediaURIs?.length === 0 ? 650 : 1200}
        footer={[
          <div
            style={{
              textAlign: "center",
              width: "100%",
            }}
          >
            <Button key="approve" type="primary" onClick={handleOnOk}>
              Approve
            </Button>
          </div>,
        ]}
      >
        <Row gutter={18}>
          {ctaMediaURIs?.length > 0 && (
            <Col
              span={12}
              style={{
                display: "flex",
                justifyContent: "center",
                alignItems: "center",
                marginTop: "32px",
                maxHeight: "580px",
                overflowY: "auto",
              }}
            >
              {ctaMediaURIs ? (
                ctaMediaURIs?.map((item) => (
                  <img
                    src={item}
                    alt="Not Found!"
                    width={550}
                    style={{
                      objectFit: "contain",
                    }}
                  />
                ))
              ) : (
                <span>No Image Found!</span>
              )}
            </Col>
          )}

          <Col span={ctaMediaURIs?.length === 0 ? 24 : 12}>
            <div
              style={{
                maxHeight: "580px",
                wordBreak: "break-word",
                whiteSpace: "pre-wrap",
                marginTop: "32px",
              }}
            >
              <Card
                title={ctaQuestion ?? "N/A"}
                style={{
                  borderRadius: 0,
                }}
              >
                <EditableParagraph
                  textToBeVerified={textToBeVerified}
                  setTextToBeVerified={setTextToBeVerified}
                  setIsParagraphEdited={setIsParagraphEdited}
                  initialContent={initialContent}
                />
              </Card>
            </div>
          </Col>
        </Row>
      </Modal>
    </>
  );
};
export default CtaModal;
