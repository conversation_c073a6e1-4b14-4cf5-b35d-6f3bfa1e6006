import { <PERSON><PERSON>, Card, Col, Form, Input, message, Row } from "antd";
import React, { useState } from "react";
import SegmentedOption from "../../components/common/segmentedOption";
import DataTable from "../../components/common/dataTable";
import EditableDataTable from "../../components/common/EditableDataTable";
import SelectOption from "../../components/common/selectOption";
import FileUpload from "../../components/common/FileUpload";
import SupportSection from "./sections/supportSection";
import ProductSection from "./sections/productSection";
import SalesSection from "./sections/salesSection";
import SetupDetailSection from "./sections/setupDetailSection";

const SetupComponent = ({ businessTypeOptionList }) => {
  // Segmented Options
  const [option, setOption] = useState("sales");

  // Sales Table Data
  const [salesTableData, setSalesTableData] = useState();

  const segmentedOptionsList = [
    { label: "Sales", value: "sales" },
    { label: "Support", value: "support" },
  ];

  return (
    <div>
      <Row gutter={16}>
        <Col span={16}>
          <Card style={styles.card} bordered={false}>
            <ProductSection businessTypeOptionList={businessTypeOptionList} />
            <Card
              style={{
                marginBottom: "48px",
              }}
            >
              <div
                style={{
                  display: "flex",
                  justifyContent: "center",
                  marginBottom: "48px",
                }}
              >
                <SegmentedOption
                  optionsList={segmentedOptionsList}
                  option={option}
                  setOption={setOption}
                />
              </div>
              {option === "sales" ? (
                <>
                  <SalesSection />
                </>
              ) : (
                <>
                  <SupportSection />
                </>
              )}
            </Card>
          </Card>
        </Col>
        <Col span={8}>
          <Card style={styles.card} bordered={false}>
            <SetupDetailSection />
          </Card>
        </Col>
      </Row>
    </div>
  );
};

// Styling for the page
const styles = {
  container: {
    display: "flex",
    justifyContent: "center",
  },

  card: {
    padding: "10px 20px",
    boxShadow: "0 4px 12px rgba(0, 0, 0, 0.1)",
    borderRadius: "8px",
    backgroundColor: "#fff",
    minHeight: "80vh",
  },

  button: {
    backgroundColor: "#1890ff",
    borderColor: "#1890ff",
    fontWeight: "bold",
  },
};

export default SetupComponent;
