import { useState, useEffect } from "react";
import {
  Card,
  Upload,
  Button,
  List,
  Progress,
  message,
  Steps,
  Select,
  Space,
  Input,
  Divider,
} from "antd";
import {
  InboxOutlined,
  DeleteOutlined,
  UploadOutlined,
  LoadingOutlined,
  CheckOutlined,
  PlusOutlined,
  CloseOutlined,
  FileOutlined,
  GlobalOutlined,
} from "@ant-design/icons";
import { useFileUpload } from "../utils/httpBase";
import { connectWebSocket } from "../utils/websocket";
import { useNavigate } from "react-router-dom";
import { checkBalance } from "../../../helpers/checkBalance";
const { Dragger } = Upload;
const { Step } = Steps;

function FileUploadPanel({
  setOnUploadComplete,
  setActiveTab,
  fetchExistingFiles,
  page,
  onClose,
  fetchSources,
  fetchTOC,
}) {
  const [fileList, setFileList] = useState([]);
  const [urls, setUrls] = useState([""]);
  const [status, setStatus] = useState({
    currentStep: 0,
    stepStatuses: {}, // Store step statuses
  });
  const navigate = useNavigate();

  const [processing, setProcessing] = useState(false);
  const [completed, setCompleted] = useState(false); // Track completion

  const { startProcessing } = useFileUpload(setStatus);

  useEffect(() => {
    console.log("pagee", page);

    setCompleted(false);
    if (processing === true) {
      const socket = connectWebSocket((newStatus) => {
        console.log("status..", newStatus);
        setStatus((prevStatus) => ({
          ...prevStatus,
          stepStatuses: newStatus,
        }));
      });

      // Cleanup on unmount or when processing becomes false
      return () => {
        socket.close();
        console.log("WebSocket closed");
      };
    }
  }, [processing]); // Add processing as a dependency

  // Update the current step based on the WebSocket status
  useEffect(() => {
    const completedSteps = Object.entries(status.stepStatuses).filter(
      ([_, value]) => value === "Completed"
    );
    setStatus((prevStatus) => ({
      ...prevStatus,
      currentStep: completedSteps.length,
    }));
  }, [status.stepStatuses]);

  const handleClose = () => {
    onClose(); // Close the drawer
    setProcessing(false);
  };

  const handleFinish = () => {
    setProcessing(false);
    setCompleted(false);
  };

  // Check if the process is completed and set the completed state
  useEffect(() => {
    const steps = Object.keys(status.stepStatuses);
    const isComplete =
      steps.length > 0 &&
      Object.values(status.stepStatuses).every(
        (stepStatus) => stepStatus === "Completed"
      );
    if (isComplete) {
      fetchSources();
      fetchTOC();
      setFileList([]);
      setUrls([]);
      fetchExistingFiles(page);
      setActiveTab("2");
      setProcessing(false);
      setCompleted(true);
      setStatus({
        currentStep: 0,
        stepStatuses: {}, // Reset step statuses
      });
    }
  }, [status.stepStatuses, page]);

  // For multi upload
  // const handleBeforeUpload = (file) => {
  //   setFileList((prev) => [...prev, file]);
  //   return false;
  // };
  const handleBeforeUpload = (file) => {
    if (file.type === "application/pdf") {
      // Replace the entire fileList instead of appending
      setFileList([file]);
      return false;
    } else {
      message.warning("Please upload a pdf file!");
    }
  };

  // const handleRemove = (file) => {
  //   setFileList((prev) => prev.filter((f) => f.uid !== file.uid));
  // };
  const handleRemove = (file) => {
    // Clear the entire fileList when removing the single file
    setFileList([]);
  };

  // Handle URL Change
  const handleUrlChange = (index, value) => {
    const newUrls = [...urls];
    newUrls[index] = value;
    setUrls(newUrls);
  };

  const addNewField = () => {
    setUrls([...urls, ""]);
  };
  const removeField = (index) => {
    if (urls.length === 1) return;
    const newUrls = urls.filter((_, i) => i !== index);
    setUrls(newUrls);
  };

  const draggerProps = {
    name: "file",
    multiple: false, // This restricts to single file upload
    beforeUpload: handleBeforeUpload,
    onRemove: handleRemove,
    fileList,
    showUploadList: false,
    accept: ".pdf", // Add this to only accept PDF files
  };

  const uploadFilesAndProcess = async () => {
    const balance = await checkBalance();

    if (balance < 3) {
      // Show an error message when balance is zero
      message.error(
        "You don't have enough credits. Please recharge your account."
      );
      return;
    }

    setProcessing(true);
    const success = await startProcessing(fileList, urls);
    console.log("urls test", urls);
    if (success) {
      message.success("Files uploaded successfully!");
    } else {
      message.error("File upload failed");
    }
  };

  const getIconForItem = (item) => {
    if (fileList.includes(item)) {
      // If the item is in fileList, show PDF logo
      return <FileOutlined style={{ color: "#1890ff", fontSize: 20 }} />;
    }
    if (urls.includes(item)) {
      // If the item is in urls, show Web logo
      return <GlobalOutlined style={{ color: "#1890ff", fontSize: 20 }} />;
    }
    return null;
  };

  const getStepIcon = (step) => {
    const stepStatus = status.stepStatuses[step];
    if (stepStatus === "Completed") {
      return <CheckOutlined style={{ color: "#52c41a" }} />;
    }
    if (stepStatus === "In Progress") {
      return <LoadingOutlined style={{ color: "#1890ff" }} />;
    }
    return null;
  };

  return (
    <div
      style={{
        height: "60vh",
        overflow: "auto",
        display: "flex",
        justifyContent: "center",
        alignItems: "center",
      }}
    >
      <Card
        style={{
          width: "35vw",
          // margin: "10px auto",
          boxShadow: "0 4px 12px rgba(0, 0, 0, 0.15)",
          textAlign: "center",
          borderRadius: "8px",
          // height: "100%",
        }}
      >
        {!processing && (
          <div
            style={{
              width: "100%",
              display: "flex",
              flexDirection: "column",
              justifyContent: "center",
            }}
          >
            <Dragger {...draggerProps} style={{ padding: "10px" }}>
              <p className="ant-upload-drag-icon">
                <InboxOutlined style={{ fontSize: "48px", color: "#1890ff" }} />
              </p>
              <p
                className="ant-upload-text"
                style={{ fontSize: "16px", marginBottom: "8px" }}
              >
                Drag & drop your files here
              </p>
              <p className="ant-upload-hint" style={{ fontSize: "14px" }}>
                Or click to select files (Select only PDF file.)
              </p>
            </Dragger>
            <div style={{ padding: "10px", marginTop: "10px" }}>
              {urls.map((url, index) => (
                <Space
                  key={index}
                  width="100%"
                  style={{
                    width: "100%",
                    display: "flex",
                    // marginBottom: 8,
                    justifyContent: "center",
                  }}
                  // size="middle"
                >
                  <Input
                    placeholder={`Enter URL ${index + 1}`}
                    value={url}
                    onChange={(e) => handleUrlChange(index, e.target.value)}
                    prefix={
                      <GlobalOutlined
                        style={{ color: "#1890ff", fontSize: "20px" }}
                      />
                    }
                    style={{
                      width: "25vw",
                      height: "36px",
                      flex: 1,
                      border: "1px solid primary",
                    }}
                    allowClear
                  />
                  {urls.length > 1 && (
                    // <CloseOutlined onClick={() => removeField(index)} />
                    <Button
                      type="text"
                      icon={<CloseOutlined style={{ color: "#ff4d4f" }} />}
                      onClick={() => removeField(index)}
                      style={{ marginLeft: "4px" }}
                    />
                  )}
                </Space>
              ))}

              {/* <Space style={{ marginTop: "10px" }}>
              <Button
                type="dashed"
                onClick={addNewField}
                icon={<PlusOutlined />}
                style={{ color: "blue" }}
              >
                Add More
              </Button>
            </Space> */}
            </div>
          </div>
        )}

        {/* {(fileList.length > 0 || urls.length > 0) && !processing && ( */}
        {(fileList.length > 0 || urls.some((url) => url.trim() !== "")) &&
          !processing && (
            <>
              <Divider style={{ margin: "10px 0" }}>Selected Sources</Divider>

              <List
                style={{ marginTop: 10, textAlign: "left" }}
                dataSource={[
                  ...fileList,
                  ...urls.filter((url) => url.trim() !== ""),
                ]}
                renderItem={(item, index) => (
                  <List.Item
                    actions={[
                      <Button
                        key="delete"
                        type="text"
                        icon={<DeleteOutlined style={{ color: "red" }} />}
                        onClick={() => {
                          if (item.name) {
                            handleRemove(item);
                          } else {
                            setUrls(urls.filter((url) => url !== item));
                          }
                        }}
                      />,
                    ]}
                  >
                    <Space>
                      {getIconForItem(item)}
                      <span>
                        {(item.name || item).length > 40
                          ? (item.name || item).substring(0, 40) + "..."
                          : item.name || item}
                      </span>
                    </Space>
                  </List.Item>
                )}
              />
              <Button
                type="primary"
                icon={<UploadOutlined />}
                onClick={uploadFilesAndProcess}
                style={{ marginTop: 20 }}
              >
                Upload and Process Files
              </Button>
            </>
          )}

        {processing && (
          <div style={{ marginTop: 20, textAlign: "left" }}>
            <h2 style={{ textAlign: "center" }}>
              Please take your time, we appreciate your patience
            </h2>
            <Steps
              direction="vertical"
              current={status.currentStep}
              style={{
                position: "relative",
                zIndex: 2,
              }}
            >
              {Object.keys(status.stepStatuses).map((step, index) => (
                <Step
                  key={index}
                  title={
                    <span
                      style={{
                        fontSize: "14px",
                        lineHeight: "20px",
                        position: "relative",
                        zIndex: 2,
                        background: "white",
                        padding: "0 5px",
                      }}
                    >
                      {step}
                    </span>
                  }
                  icon={getStepIcon(step)}
                  style={{
                    position: "relative",
                  }}
                />
              ))}
            </Steps>
            <Progress
              percent={Math.round(
                (status.currentStep / Object.keys(status.stepStatuses).length) *
                  100
              )}
              style={{
                marginTop: 10,
                position: "relative",
                zIndex: 1,
              }}
            />
            {completed && (
              <Button
                type="primary"
                // onClick={() => window.location.reload()}
                onClick={handleFinish}
                style={{ marginTop: 20 }}
              >
                Finish
              </Button>
            )}
          </div>
        )}
      </Card>
    </div>
  );
}

export default FileUploadPanel;
