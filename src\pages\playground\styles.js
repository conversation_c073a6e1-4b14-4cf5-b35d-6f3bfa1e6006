export const styles = {
  layout: {
    height: "90vh",
  },
  content: {
    paddingLeft: "4px",
    paddingRight: "4px",
  },
  container: {
    height: "100%",
  },
  chatCard: {
    borderRadius: "10px",
  },
  chatHeader: {
    padding: "8px",
    borderBottom: "1px solid #f0f0f0",
  },
  chatMessages: {
    maxHeight: "70vh",
    overflowY: "auto",
    padding: "16px",
  },
  inputContainer: {
    padding: "16px",
    borderTop: "1px solid #f0f0f0",
    display: "flex",
    alignItems: "center",
  },
  messageContainer: {
    marginBottom: "16px",
    display: "flex",
  },
  userMessageContainer: {
    display: "flex",
    justifyContent: "flex-end",
  },
  aiMessageContent: {
    backgroundColor: "#e6f7ff",
    borderRadius: "8px",
    padding: "16px",
    maxWidth: "80%",
  },
  userMessageContent: {
    backgroundColor: "rgb(204, 239, 174)",
    borderRadius: "8px",
    padding: "16px",
    maxWidth: "800px",
  },
  messageTime: { marginTop: "4px" },
  metadataCard: {
    marginBottom: "16px",
    height: "calc(33.33% - 11px)",
    overflow: "hidden",
    display: "flex",
    flexDirection: "column",
  },
  metadataContent: {
    flex: 1,
    // overflowY: "auto",
  },
  metadataList: { marginBottom: 0 },
  infoGatheringItem: { display: "flex", alignItems: "center" },
  marginRight: { marginRight: "8px" },
  marginLeft: { marginLeft: "8px" },
  textArea: {
    // flex: 1,
    marginRight: "20px",
    height: "25px",
    border: "0px solid grey",
    padding: "8px",
    maxWidth: "98%",
    borderRadius: "5px",
    transition: "all 0.3s ease-in-out",
    backgroundColor: "rgba(154, 152, 152, 0.16)",
  },
  uploadButton: {
    display: "flex",
    alignItems: "center",
    justifyContent: "center",
    backgroundColor: "rgb(0, 21, 41)",
    color: "white",
    height: "35px",
    minWidth: "40px",
    marginRight: "20px",
  },
  sendButton: {
    display: "flex",
    alignItems: "center",
    justifyContent: "center",
    backgroundColor: "rgb(0, 21, 41)",
    color: "white",
    width: "80px",
    height: "35px",
    marginLeft: "15px",
  },
  selectOption: {
    minWidth: "120px",
    "& .ant-select-selector": {
      border: "none !important",
      backgroundColor: "rgba(154, 152, 152, 0.16) !important",
      borderRadius: "5px !important",
      height: "35px !important",
      display: "flex !important",
      alignItems: "center !important",
    },
    "& .ant-select-selection-item": {
      lineHeight: "33px !important",
      fontSize: "14px !important",
    }
  },
  dataColumn: {
    height: "100%",
    display: "flex",
    flexDirection: "column",
  },
};
