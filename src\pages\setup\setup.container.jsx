import React, { useEffect, useState } from "react";
import SetupComponent from "./setup.component";
import { useAppDispatch } from "../../hooks/reduxHooks";
import { businessTypeOptionApi } from "../../services/setup.service";
import { transformString } from "../../helpers/channelNameTransformation";

const SetupContainer = () => {
  const dispatch = useAppDispatch();

  // States
  const [businessTypeOptionList, setBusinessTypeOptionList] = useState();

  // Product Logics
  const fetchBusinessTypeOptions = () => {
    dispatch(
      businessTypeOptionApi({
        finalCallback: () => {},
        successCallback: (response) => {
          const options = response?.map((item) => {
            return {
              label: item,
              value: item,
            };
          });
          setBusinessTypeOptionList(options);
        },
        failureCallback: () => {},
      })
    );
  };

  // Sales Logics
  // const fetchSalesPreSetup = () => {
  //   dispatch(
  //     salesPreSetupApi({
  //       finalCallback: () => {},
  //       successCallback: () => {},
  //       failureCallback: () => {},
  //     })
  //   );
  // };

  useEffect(() => {
    fetchBusinessTypeOptions();
    // fetchSalesPreSetup();
  }, []);

  return <SetupComponent businessTypeOptionList={businessTypeOptionList} />;
};

export default SetupContainer;
