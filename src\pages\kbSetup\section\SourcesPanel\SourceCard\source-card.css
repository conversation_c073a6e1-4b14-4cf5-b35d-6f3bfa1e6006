/* Base card styling */
.source-card {
  transition: all 0.3s ease;
  overflow: hidden;
}

.source-card:hover {
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15) !important;
}

/* Content area styling */
.content-container {
  position: relative;
  background: #f8f9fa;
  padding: 16px;
  border-radius: 8px;
  margin-bottom: 16px;
  overflow: hidden;
}

/* Highlighted text styling */
.highlight {
  background-color: #ffff00;
  padding: 0 2px;
  border-radius: 2px;
  transition: background-color 0.3s ease;
}

.highlight:hover {
  background-color: #ffd700;
}

/* Expanded content highlight */
.expanded-content {
  animation: fadeIn 0.5s ease;
  background-color: rgba(240, 249, 255, 0.7);
  padding: 4px;
  border-radius: 4px;
  border-left: 3px solid #1890ff;
}

@keyframes fadeIn {
  from {
    opacity: 0;
  }
  to {
    opacity: 1;
  }
}

/* Read more button styling */
.read-more-button {
  position: relative;
  transition: all 0.3s ease;
  border-radius: 6px;
  display: inline-flex;
  align-items: center;
  gap: 4px;
}

.read-more-button:hover {
  background-color: #e6f7ff;
  border-color: #1890ff;
  transform: translateY(-1px);
}

.read-more-button:after {
  content: "";
  position: absolute;
  bottom: -5px;
  left: 0;
  width: 100%;
  height: 3px;
  background: linear-gradient(90deg, transparent, #1890ff, transparent);
  opacity: 0;
  transition: opacity 0.3s ease;
}

.read-more-button:hover:after {
  opacity: 1;
}

/* Fade gradient for truncated text */
.truncated-content {
  position: relative;
}

.truncated-content:after {
  content: "";
  position: absolute;
  bottom: 0;
  right: 0;
  width: 30%;
  height: 20px;
  background: linear-gradient(
    90deg,
    rgba(248, 249, 250, 0),
    rgba(248, 249, 250, 1)
  );
  pointer-events: none;
}

/* Card title styling */
.card-title {
  font-size: 20px;
  font-weight: 600;
  margin: 0;
  transition: color 0.3s ease;
}

.card-title:hover {
  color: #1890ff;
}

/* Action buttons styling */
.action-button {
  border-radius: 6px;
  transition: all 0.3s ease;
}

.action-button:hover {
  transform: translateY(-2px);
}

.edit-button:hover {
  background-color: #e6f7ff;
  border-color: #1890ff;
  color: #1890ff;
}

.delete-button:hover {
  background-color: #fff1f0;
  border-color: #ff4d4f;
  color: #ff4d4f;
}

/* Image gallery styling */
.image-gallery {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(130px, 1fr));
  gap: 12px;
  margin-top: 16px;
}

.image-container {
  position: relative;
  border-radius: 8px;
  overflow: hidden;
  transition: transform 0.3s ease;
}

.image-container:hover {
  transform: scale(1.05);
}

.image-preview {
  object-fit: cover;
  border-radius: 8px;
  width: 100%;
  height: 100px;
}

.delete-image-button {
  position: absolute;
  top: 4px;
  right: 4px;
  background: rgba(255, 255, 255, 0.9);
  border-radius: 50%;
  width: 24px;
  height: 24px;
  display: flex;
  align-items: center;
  justify-content: center;
  opacity: 0;
  transition: opacity 0.3s ease;
}

.image-container:hover .delete-image-button {
  opacity: 1;
}

/* Add image button styling */
.add-image-button {
  width: 100%;
  height: 100px;
  border-radius: 8px;
  display: flex;
  align-items: center;
  justify-content: center;
  flex-direction: column;
  background: #f8f9fa;
  color: #8c8c8c;
  transition: all 0.3s ease;
}

.add-image-button:hover {
  background: #e6f7ff;
  border-color: #1890ff;
  color: #1890ff;
}

.add-image-button:hover .anticon {
  transform: rotate(90deg);
  transition: transform 0.3s ease;
}

/* Responsive adjustments */
@media (max-width: 768px) {
  .card-header {
    flex-direction: column;
    align-items: flex-start;
  }

  .card-actions {
    margin-top: 12px;
    width: 100%;
    justify-content: flex-end;
  }

  .image-gallery {
    grid-template-columns: repeat(auto-fill, minmax(100px, 1fr));
  }

  .image-preview {
    height: 80px;
  }
}

@media (max-width: 480px) {
  .content-container {
    padding: 12px;
  }

  .card-title {
    font-size: 18px;
  }

  .image-gallery {
    grid-template-columns: repeat(auto-fill, minmax(80px, 1fr));
  }

  .image-preview {
    height: 70px;
  }
}

/* Override the padding and border-radius */
.css-dev-only-do-not-override-apn68 .ant-card .ant-card-body {
  padding: 0 !important; /* Remove padding */
  border-radius: 0 !important; /* Remove border radius */
}
