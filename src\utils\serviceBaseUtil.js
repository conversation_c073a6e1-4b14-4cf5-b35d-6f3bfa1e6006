import axios from "axios";
import { message } from "antd";

let authToken = localStorage.getItem("authToken");

let config = {
  baseUrl: process.env.REACT_APP_API_BASE_URL,
  headers: {
    Authorization: `Bearer ${authToken}`,
  },
  method: "post",
};

export const updateAuthToken = (token) => {
  authToken = token;
  config = {
    ...config,
    headers: {
      Authorization: `Bearer ${authToken}`,
    },
  };
};

export const apiService = async ({ url, data, method }) => {
  return await axios({
    ...config,
    method: method ?? "get",
    url: `${config.baseUrl}/${url}`,
    data,
  })
    .then((response) => {
      return {
        status: response?.status,
        data: response?.data,
      };
    })
    .catch((error) => {
      console.log("error ", error);
      if (error?.response?.status === 401) {
        if (url?.includes("auth/login")) {
          message.error("Invalid username and/or password");
          return;
        }
        handle401Error();
      }
      return {
        status: error?.response?.status,
        data: error?.response?.data,
      };
    });
};

const handle401Error = async () => {
  let userDataStr = localStorage.getItem("userData");
  let userData = userDataStr ? JSON.parse(userDataStr) : {};
  if (userData?.refresh_token) {
    // refreshTokenApi(userData?.refresh_token)
  } else {
    // handle token expiration without refresh
  }
  console.log("user data here", userData);
};

export const exportApiService = async ({ url, data, method }) => {
  return fetch(`${process.env.REACT_APP_API_BASE_URL}/${url}`, {
    method: method ?? "POST",
    headers: {
      "Content-Type": "application/json",
      Authorization: `Bearer ${localStorage.getItem("authToken")}`,
    },
    body: JSON.stringify(data),
  })
    .then((response) => {
      return response.blob();
    })
    .catch((error) => {
      console.log("error at export", error);
    });
};

export const fetchAuthUrl = async ({ url, successCallback }) => {
  return await axios({
    ...config,
    method: "get",
    url: `${url}`,
    responseType: "blob",
  })
    .then((response) => {
      if (response?.status === 200) {
        successCallback(response?.data);
      }
    })
    .catch((error) => {
      console.log("error at export", error);
    });
};
