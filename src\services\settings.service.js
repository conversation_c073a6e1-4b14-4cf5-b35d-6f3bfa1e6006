import { ALL_FILTERS } from "../constants/url";
import httpBase from "../utils/http.utils";

export const getAllSettingsApi = ({
  params,
  finalCallback,
  successCallback,
  failureCallback,
}) => {
  return async (dispatch) => {
    try {
      const response = await httpBase().get(ALL_FILTERS, {
        params: params,
      });
      successCallback(response?.data);
    } catch (e) {
      failureCallback(e);
    } finally {
      finalCallback();
    }
  };
};
