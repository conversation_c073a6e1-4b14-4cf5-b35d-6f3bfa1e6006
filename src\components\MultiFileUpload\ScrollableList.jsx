import React, { useEffect, useState } from "react";
import {
  Card,
  Input,
  Upload,
  Modal,
  message,
  Space,
  Divider,
  Statistic,
  Tag,
  Button,
} from "antd";
import {
  EditOutlined,
  DeleteOutlined,
  UploadOutlined,
  FileOutlined,
  DatabaseOutlined,
  FileImageOutlined,
  FolderOpenOutlined,
} from "@ant-design/icons";
import { Typography } from "antd";
import ImagePreview from "../common/ImagePreview";

const { Text, Title } = Typography;
const { TextArea } = Input;

const ScrollableList = ({
  listData,
  setUploadStatus,
  setShowExistingFiles,
  simulateData,
}) => {
  const [cardData, setCardData] = useState();

  const [editState, setEditState] = useState({
    id: null,
    field: null,
  });

  useEffect(() => {
    setCardData(listData);
  }, [listData]);

  const [previewVisible, setPreviewVisible] = useState(false);
  const [previewImage, setPreviewImage] = useState("");

  const handleEdit = (id, field, value) => {
    setCardData((prevData) =>
      prevData.map((card) => {
        if (card.id_ === id) {
          if (field === "text") {
            return { ...card, text: value };
          } else {
            return {
              ...card,
              extra_info: {
                ...card.extra_info,
                [field]: value,
              },
            };
          }
        }
        return card;
      })
    );
    setEditState({ id: null, field: null });
  };

  const handleImageUpload = (id, { file, fileList }) => {
    // Generate a preview URL before uploading
    const previewUrl = URL.createObjectURL(file.originFileObj);

    // Temporarily add the image URL to the state (before API call)
    setCardData((prevData) =>
      prevData.map((card) => {
        if (card.id_ === id) {
          return {
            ...card,
            extra_info: {
              ...card.extra_info,
              images: [...card.extra_info.images, previewUrl], // Add preview URL
            },
          };
        }
        return card;
      })
    );
    if (file.status === "done") {
      setCardData((prevData) =>
        prevData.map((card) => {
          if (card.id_ === id) {
            return {
              ...card,
              extra_info: {
                ...card.extra_info,
                images: [...card.extra_info.images, file.response.url],
              },
            };
          }
          return card;
        })
      );
      message.success(`${file.name} file uploaded successfully`);
    } else if (file.status === "error") {
      message.error(`${file.name} file upload failed.`);
    }
  };

  const handleImageDelete = (id, imageUrl) => {
    setCardData((prevData) =>
      prevData.map((card) => {
        if (card.id_ === id) {
          return {
            ...card,
            extra_info: {
              ...card.extra_info,
              images: card.extra_info.images.filter((url) => url !== imageUrl),
            },
          };
        }
        return card;
      })
    );
    message.success("Image deleted successfully");
  };

  const handlePreview = (imageUrl) => {
    setPreviewImage(imageUrl);
    setPreviewVisible(true);
  };

  return (
    <div
      style={{
        height: "100vh",
        overflow: "auto",
        padding: "20px",
      }}
    >
      <div
        style={{
          display: "flex",
          justifyContent: "space-between",
          marginBottom: "24px",
        }}
      >
        <div>
          <div style={{ display: "flex" }}>
            <p
              style={{
                color: "#999999",
                fontWeight: "500",
                marginBottom: "8px",
              }}
            >
              <DatabaseOutlined style={{ marginRight: "8px" }} />
              Questions Generated:{" "}
              <Tag style={{ color: "#444444", marginLeft: "6px" }}>1000</Tag>
            </p>
            <p style={{ color: "#999999", fontWeight: "500" }}>
              <FileOutlined style={{ marginRight: "8px" }} />
              Conflicts:{" "}
              <Tag style={{ color: "#444444", marginLeft: "6px" }}>30</Tag>
            </p>
            <p style={{ color: "#999999", fontWeight: "500" }}>
              <FileOutlined style={{ marginRight: "8px" }} />
              Duplicate:{" "}
              <Tag style={{ color: "#444444", marginLeft: "6px" }}>30</Tag>
            </p>
          </div>
          <div style={{ display: "flex" }}>
            <p
              style={{
                color: "#999999",
                fontWeight: "500",
                marginBottom: "8px",
              }}
            >
              <DatabaseOutlined style={{ marginRight: "8px" }} />
              Source Files:{" "}
              <Tag style={{ color: "#444444", marginLeft: "6px" }}>1000</Tag>
            </p>
            <p style={{ color: "#999999", fontWeight: "500" }}>
              <FileOutlined style={{ marginRight: "8px" }} />
              Knowledge Maps:{" "}
              <Tag style={{ color: "#444444", marginLeft: "6px" }}>30</Tag>
            </p>
          </div>
        </div>

        <div>
          <Button
            icon={<FolderOpenOutlined />}
            onClick={() => {
              setShowExistingFiles(true);
              setUploadStatus(null);
            }}
          >
            File Management
          </Button>
        </div>
      </div>
      {simulateData && (
        <div style={{ marginBottom: "24px" }}>
          <Card>{simulateData?.response}</Card>
        </div>
      )}
      {cardData?.map((card) => (
        <Card key={card?.id_} style={{ marginBottom: "16px" }}>
          {/* Title */}
          {editState?.id === card?.id_ && editState?.field === "title" ? (
            <Input
              defaultValue={card?.extra_info?.title}
              onPressEnter={(e) =>
                handleEdit(card?.id_, "title", e.target.value)
              }
              onBlur={(e) => handleEdit(card?.id_, "title", e.target.value)}
              autoFocus
            />
          ) : (
            <Title
              level={4}
              onClick={() => setEditState({ id: card.id_, field: "title" })}
            >
              {card?.extra_info?.title ?? "Title not available"}
              <Button type="text" icon={<EditOutlined />} />
            </Title>
          )}

          {/* Source Link */}
          <a
            href={card.extra_info.source}
            target="_blank"
            rel="noopener noreferrer"
            style={{
              display: "inline-flex",
              alignItems: "center",
            }}
          >
            <FileOutlined style={{ marginRight: "8px" }} />
            View Source Document
          </a>

          {/* Page Number Section */}
          {card?.extra_info?.page_number && (
            <p style={{ color: "#777777" }}>
              Page number :{" "}
              <span style={{ color: "#444444" }}>
                {card?.extra_info?.page_number}
              </span>
            </p>
          )}

          <Divider />
          {/* Text Content */}

          {editState?.id === card?.id_ && editState?.field === "text" ? (
            <TextArea
              defaultValue={card?.text}
              onPressEnter={(e) =>
                handleEdit(card?.id_, "text", e.target.value)
              }
              onBlur={(e) => handleEdit(card?.id_, "text", e.target.value)}
              autoFocus
              rows={18}
              style={{ whiteSpace: "pre-wrap" }}
            />
          ) : (
            <div style={{ position: "relative" }}>
              <Text
                onClick={() => setEditState({ id: card?.id_, field: "text" })}
                style={{
                  whiteSpace: "pre-wrap",
                  display: "block",
                  marginBottom: "16px",
                  marginRight: "32px",
                }}
              >
                {card?.text}
              </Text>

              <Button
                type="text"
                icon={<EditOutlined />}
                style={{
                  position: "absolute",
                  top: "0",
                  right: "0",
                  padding: "4px",
                  height: "auto",
                }}
                onClick={() => setEditState({ id: card?.id_, field: "text" })}
              />
            </div>
          )}
          <Divider />

          {/* Images */}
          <div style={{ marginTop: "16px" }}>
            <Space wrap>
              {card?.extra_info?.images?.map((imageUrl, index) => (
                <div key={index} style={{ position: "relative" }}>
                  <img
                    src={imageUrl}
                    alt={`Image ${index + 1}`}
                    style={{
                      width: 180,
                      height: 180,
                      objectFit: "cover",
                      cursor: "pointer",
                    }}
                    onClick={() => handlePreview(imageUrl)}
                  />
                  <DeleteOutlined
                    style={{
                      position: "absolute",
                      top: 5,
                      right: 5,
                      background: "white",
                      padding: 4,
                      borderRadius: "50%",
                      cursor: "pointer",
                      boxShadow: "0 2px 4px rgba(0,0,0,0.2)",
                    }}
                    onClick={() => handleImageDelete(card?.id_, imageUrl)}
                  />
                </div>
              ))}
              <Upload
                name="file"
                listType="picture-card"
                showUploadList={false}
                onChange={(info) => handleImageUpload(card?.id_, info)}
                action="/api/upload" // Replace with your upload endpoint
              >
                <div>
                  <UploadOutlined />
                  <div style={{ marginTop: 8 }}>Upload</div>
                </div>
              </Upload>
            </Space>
          </div>
        </Card>
      ))}

      <ImagePreview
        previewVisible={previewVisible}
        handleCancel={() => setPreviewVisible(false)}
        imgSrc={previewImage}
      />
    </div>
  );
};

export default ScrollableList;
