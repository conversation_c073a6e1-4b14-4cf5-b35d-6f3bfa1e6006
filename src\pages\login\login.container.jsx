import React, { useState } from "react";
import LoginComponent from "./login.component.jsx";
import { useNavigate, useParams } from "react-router-dom";
import { useAppDispatch } from "../../hooks/reduxHooks.js";
import { loginApi } from "../../services/auth.service.js";

const LoginContainer = () => {
  const [submitting, setSubmitting] = useState(false);
  const navigate = useNavigate();
  const dispatch = useAppDispatch();

  // Extracting slug from browser url
  const { id } = useParams();

  const handleLogin = (values) => {
    console.log("values -> ", values);
    console.log("id -> ", id);
    setSubmitting(true);
    const newObj = { ...values, client_id: id };

    dispatch(
      loginApi({
        data: newObj,
        finalCallback: () => {
          setSubmitting(false);
        },
        successCallback: (res) => {
          navigate("/dashboard", { replace: true });
        },
      })
    );
  };

  return <LoginComponent handleLogin={handleLogin} submitting={submitting} />;
};

export default LoginContainer;
