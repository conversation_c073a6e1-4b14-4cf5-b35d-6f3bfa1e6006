import React, { useState } from "react";
import { Select, Tag, Space, Typography, Divider } from "antd";
import { FilterOutlined } from "@ant-design/icons";

const { Option } = Select;

const FilterMultiSelect = ({ options, handleChange }) => {
  return (
    <div>
      <Select
        allowClear
        placeholder="Filters"
        style={{ width: "100%" }}
        onChange={handleChange}
        showSearch
        filterOption={(input, option) =>
          option.children.toLowerCase().indexOf(input.toLowerCase()) >= 0
        }
      >
        {options.map((option) => (
          <Option key={option.value} value={option.value}>
            {option.label}
          </Option>
        ))}
      </Select>
    </div>
  );
};

export default FilterMultiSelect;
