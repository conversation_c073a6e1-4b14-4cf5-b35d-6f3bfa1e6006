import {
  AGENTS_INVITE,
  AGENTS_REGISTER,
  CHANGE_PASSWORD,
  DELETE_USER,
  GET_ALL_USERS,
  USER_RESET_PASSWORD,
  USER_RESET_ROLE,
} from "../constants/url";
import httpBase, { handleApiExceptions } from "../utils/http.utils";

export const getAllUsersApi = ({
  params,
  finalCallback,
  successCallback,
  failureCallback,
}) => {
  return async (dispatch) => {
    try {
      const response = await httpBase().get(GET_ALL_USERS, {
        params: params,
      });
      successCallback(response?.data);
    } catch (e) {
      failureCallback(e);
      handleApiExceptions(e);
    } finally {
      finalCallback();
    }
  };
};

export const userPasswordResetApi = ({
  params,
  reqData,
  finalCallback,
  successCallback,
  failureCallback,
}) => {
  return async (dispatch) => {
    try {
      const response = await httpBase().post(USER_RESET_PASSWORD, reqData);
      successCallback(response?.data);
    } catch (e) {
      failureCallback(e);
      handleApiExceptions(e);
    } finally {
      finalCallback();
    }
  };
};

export const changePasswordAPI = ({
  params,
  finalCallback,
  successCallback,
  failureCallback,
}) => {
  return async (dispatch) => {
    try {
      const response = await httpBase().post(CHANGE_PASSWORD, params);

      if (response.data.status === 200) {
        successCallback(response);
      } else {
        failureCallback(response);
      }
    } catch (e) {
      failureCallback(e);
      handleApiExceptions(e);
    } finally {
      finalCallback();
    }
  };
};

export const changeUserRoleApi = ({
  params,
  finalCallback,
  successCallback,
  failureCallback,
}) => {
  return async (dispatch) => {
    try {
      const response = await httpBase().put(USER_RESET_ROLE, "", {
        params: params,
      });
      successCallback(response?.data);
    } catch (e) {
      failureCallback(e);
      handleApiExceptions(e);
    } finally {
      finalCallback();
    }
  };
};

export const userInvitationApi = ({
  data,
  finalCallback,
  successCallback,
  failureCallback,
}) => {
  return async (dispatch) => {
    try {
      const response = await httpBase().post(AGENTS_INVITE, data);
      successCallback(response?.data);
    } catch (e) {
      failureCallback(e);
      handleApiExceptions(e);
    } finally {
      finalCallback();
    }
  };
};

export const userRegistrationApi = ({
  data,
  finalCallback,
  successCallback,
  failureCallback,
}) => {
  return async (dispatch) => {
    try {
      const response = await httpBase().post(AGENTS_REGISTER, data);
      successCallback(response?.data);
    } catch (e) {
      failureCallback(e);
      handleApiExceptions(e);
    } finally {
      finalCallback();
    }
  };
};

export const userDeletionApi = ({
  params,
  finalCallback,
  successCallback,
  failureCallback,
}) => {
  return async (dispatch) => {
    try {
      const response = await httpBase().delete(DELETE_USER, { params: params });
      successCallback(response?.data);
    } catch (e) {
      failureCallback(e);
      handleApiExceptions(e);
    } finally {
      finalCallback();
    }
  };
};
