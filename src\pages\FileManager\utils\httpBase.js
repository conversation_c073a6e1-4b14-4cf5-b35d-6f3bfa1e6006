import { message } from "antd";
import httpBase from "../../../utils/http.utils";
import axios from "axios";

export const useFileUpload = (setStatus) => {
  const startProcessing = async (fileList, urls) => {
    try {
      // Construct form data with files and URLs
      const formData = new FormData();
      fileList.forEach((file) => {
        formData.append("files", file);
      });
      formData.append("urls", urls);
      formData.append("test", true); // For test mode

      // Create an axios instance from httpBase
      const api = httpBase();

      // Adjust URL based on your backend API
      // const apiUrl = "/process-documents"; // Relative URL
      const apiUrl = `${process.env.REACT_APP_WS_URL}/process-documents`

      // Use the created instance to make the POST request
      const response = await axios.post(apiUrl, formData, {
        headers: {
          'Content-Type': 'multipart/form-data',
          'Authorization': `Bearer ${localStorage.getItem('authToken')}`
        }
      });

      // Handle the response
      if (response.status === 200) {
        message.success("Files processed successfully!");
        return response.data; // Return the result (processed files, etc.)
      } else {
        message.error("File upload failed");
        return false;
      }
    } catch (error) {
      console.error("Error during file upload:", error);
      message.error("File upload failed");
      return false;
    }
  };

  return { startProcessing };
};
