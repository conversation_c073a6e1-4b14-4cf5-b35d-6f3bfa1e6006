.chat-window {
  height: 79vh;
  /* width: 400px; */
  border: 1px solid #e8e8e8;
  border-radius: 8px;
  overflow: hidden;
  display: flex;
  flex-direction: column;
  background-color: #f0f2f5;
}

.chat-content {
  display: flex;
  flex-direction: column;
  height: 100%;
  /* flex: 1; */
}

.messages-container {
  flex-grow: 1;
  overflow-y: auto;
  padding: 20px;
  display: flex;
  flex-direction: column;
  scrollbar-color: #888 #f1f1f1;
  scrollbar-width: thin;
  scroll-behavior: smooth;
  max-height: 70vh;
}

.message-item {
  display: flex;
  margin-bottom: 12px;
  transition: all 0.3s ease;
  opacity: 0;
  transform: translateY(20px);
  animation: fadeIn 0.3s ease forwards;
}

@keyframes fadeIn {
  to {
    opacity: 1;
    transform: translateY(0);
  }
}
.message-item {
  font-size: 15px;
  text-align: justify;
}
.message-item.user {
  justify-content: flex-end;
}

.message-item.ai {
  justify-content: flex-start;
  display: block;
  width: auto;
}

.message-bubble {
  padding: 8px 12px;
  border-radius: 18px;
  max-width: 55%;
  /* word-wrap: break-word; */
}

.message-bubble.user {
  background-color: #1890ff;
  color: white;
  border-bottom-right-radius: 4px;
  max-width: 60vw;
  /* word-wrap: break-word; */
  /* word-break: break-word; */
}

.message-bubble.ai {
  background-color: white;
  color: rgba(0, 0, 0, 0.85);
  border-bottom-left-radius: 4px;
  margin-bottom: 8px;
  padding: 8px 12px;
  border: 1px solid #ccc;
  display: block;
  width: fit-content;
  max-width: 55%;
  word-wrap: break-word;
  white-space: pre-wrap;
}

.message_bubble_ai {
  background-color: rgb(208, 200, 200);
  color: rgba(0, 0, 0, 0.85);

  /* border-bottom-right-radius: 10px; */
  border-radius: 10px 10px 10px 0;

  margin-bottom: 8px;
  padding: 8px 12px;
  border: 1px solid #ccc;
  display: inline-block;
  width: fit-content;
}

.ai-avatar {
  margin-right: 8px;
  align-self: flex-start;
  /* align-self: center; */
}

.input-area {
  display: flex;
  justify-content: center;
  align-items: center;
  padding: 10px;
  background-color: white;
  border-top: 1px solid #e8e8e8;
  transition: all 0.3s ease;
}

.message-input {
  flex-grow: 1;
  margin-right: 12px;
}

.send-button {
  transition: all 0.3s ease;
}

.send-button:hover {
  transform: scale(1.05);
}
