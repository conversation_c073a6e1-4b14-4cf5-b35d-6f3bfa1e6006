import React, { useEffect } from "react";
import { Button, Card, Form, Input, message, Space, Spin } from "antd";
import logoIcon from "../../assets/images/eko-favicon.png";
import { useLocation } from "react-router-dom";

const LoginComponent = (props) => {
  const { handleLogin, submitting } = props;
  const location = useLocation();

  const onFinish = (val) => {
    handleLogin(val);
  };

  useEffect(() => {
    const { source } = location.state || {};
    if (source === "expired") {
      message.warning("Session expired. Please log in again.");
    }
  }, []);

  return (
    <div style={styles.container}>
      <Spin spinning={submitting} tip="Logging in..." size="large">
        {/* Spin component */}
        <Card style={styles.card} bordered={false}>
          <Space direction="vertical" size="middle" style={{ display: "flex" }}>
            {/* Company logo */}
            <img src={logoIcon} alt="Company Logo" style={styles.logo} />

            {/* Login form */}
            <Form name="login_form" layout="vertical" onFinish={onFinish}>
              <Form.Item
                name="username"
                label="Username"
                rules={[
                  { required: true, message: "Please input your username!" },
                ]}
              >
                <Input size="large" placeholder="Enter your username" />
              </Form.Item>

              <Form.Item
                name="password"
                label="Password"
                rules={[
                  { required: true, message: "Please input your password!" },
                ]}
              >
                <Input.Password
                  size="large"
                  placeholder="Enter your password"
                />
              </Form.Item>

              <Form.Item>
                <Button
                  type="primary"
                  htmlType="submit"
                  loading={submitting}
                  block
                  size="large"
                  style={styles.button}
                >
                  Log in
                </Button>
              </Form.Item>
            </Form>
          </Space>
        </Card>
      </Spin>
    </div>
  );
};

// Styling for the page
const styles = {
  container: {
    display: "flex",
    justifyContent: "center",
    alignItems: "center",
    height: "100vh",
  },
  card: {
    width: 400,
    padding: "40px 20px",
    boxShadow: "0 4px 12px rgba(0, 0, 0, 0.1)",
    borderRadius: "8px",
    backgroundColor: "#fff",
  },
  logo: {
    display: "block",
    margin: "0 auto",
    height: 120,
  },
  button: {
    backgroundColor: "#1890ff",
    borderColor: "#1890ff",
    fontWeight: "bold",
  },
};

export default LoginComponent;
