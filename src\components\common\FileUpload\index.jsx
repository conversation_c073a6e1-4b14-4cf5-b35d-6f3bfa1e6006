// import React, { useState } from "react";
// import { UploadOutlined } from "@ant-design/icons";
// import { Button, message, Upload } from "antd";
// import { useAppDispatch } from "../../../hooks/reduxHooks";
// import { uploadFileApi } from "../../../services/setup.service";

// const FileUpload = ({ url, setResponseData }) => {
//   const dispatch = useAppDispatch();
//   const [fileList, setFileList] = useState([]);
//   const [uploading, setUploading] = useState(false);

//   const handleUpload = () => {
//     const formData = new FormData();
//     fileList.forEach((file) => {
//       formData.append("file", file);
//     });

//     setUploading(true);
//     dispatch(
//       uploadFileApi({
//         url: url,
//         formData: formData,
//         finalCallback: () => {
//           setUploading(false);
//         },
//         successCallback: (response) => {
//           setFileList([]);
//           if (response?.support) {
//             setResponseData(response?.support);
//           } else {
//             setResponseData(response);
//           }
//           message.success("File uploaded successfully!");
//         },
//         failureCallback: () => {
//           message.error("File Uploading failed!");
//         },
//       })
//     );
//   };

//   const props = {
//     onRemove: (file) => {
//       const index = fileList.indexOf(file);
//       const newFileList = fileList.slice();
//       newFileList.splice(index, 1);
//       setFileList(newFileList);
//     },
//     beforeUpload: (file) => {
//       setFileList([...fileList, file]);
//       return false;
//     },
//     fileList,
//   };

//   return (
//     <div>
//       <div
//         style={{
//           display: "flex",
//           justifyContent: "center",
//           alignItems: "center",
//         }}
//       >
//         <Upload {...props}>
//           <Button icon={<UploadOutlined />}>Select File</Button>
//         </Upload>
//       </div>
//       <div
//         style={{
//           display: "flex",
//           justifyContent: "center",
//           alignItems: "center",
//         }}
//       >
//         <Button
//           type="primary"
//           onClick={handleUpload}
//           disabled={fileList.length === 0}
//           loading={uploading}
//           style={{
//             marginTop: 16,
//           }}
//         >
//           {uploading ? "Analyzing" : "Start Upload"}
//         </Button>
//       </div>
//     </div>
//   );
// };
// export default FileUpload;

// import React, { useState } from "react";
// import { UploadOutlined } from "@ant-design/icons";
// import { Button, message, Upload } from "antd";
// import { useAppDispatch } from "../../../hooks/reduxHooks";
// import { uploadFileApi } from "../../../services/setup.service";

// const FileUpload = ({ url, setResponseData }) => {
//   const dispatch = useAppDispatch();
//   const [fileList, setFileList] = useState([]);
//   const [uploading, setUploading] = useState(false);
//   const [uploadedFile, setUploadedFile] = useState(null);

//   const handleUpload = () => {
//     const formData = new FormData();
//     fileList.forEach((file) => {
//       formData.append("file", file);
//     });

//     setUploading(true);
//     dispatch(
//       uploadFileApi({
//         url: url,
//         formData: formData,
//         finalCallback: () => {
//           setUploading(false);
//         },
//         successCallback: (response) => {
//           setUploadedFile(fileList[0].name);
//           setFileList([]);
//           if (response?.support) {
//             setResponseData(response?.support);
//           } else {
//             setResponseData(response);
//           }
//           message.success("File uploaded successfully!");
//         },
//         failureCallback: () => {
//           message.error("File Uploading failed!");
//         },
//       })
//     );
//   };

//   const props = {
//     onRemove: (file) => {
//       const index = fileList.indexOf(file);
//       const newFileList = fileList.slice();
//       newFileList.splice(index, 1);
//       setFileList(newFileList);
//     },
//     beforeUpload: (file) => {
//       setFileList([...fileList, file]);
//       return false;
//     },
//     fileList,
//   };

//   return (
//     <div>
//       <div
//         style={{
//           display: "flex",
//           justifyContent: "center",
//           alignItems: "center",
//         }}
//       >
//         <Upload {...props}>
//           <Button icon={<UploadOutlined />}>Select File</Button>
//         </Upload>
//       </div>
//       <div
//         style={{
//           display: "flex",
//           justifyContent: "center",
//           alignItems: "center",
//           flexDirection: "column",
//           gap: "8px",
//         }}
//       >
//         <Button
//           type="primary"
//           onClick={handleUpload}
//           disabled={fileList.length === 0}
//           loading={uploading}
//           style={{
//             marginTop: 16,
//           }}
//         >
//           {uploading ? "Analyzing" : "Start Upload"}
//         </Button>
//         {uploadedFile && (
//           <div style={{ marginTop: 8 }}>Last uploaded file: {uploadedFile}</div>
//         )}
//       </div>
//     </div>
//   );
// };

// export default FileUpload;

import React, { useState } from "react";
import { UploadOutlined, FileOutlined } from "@ant-design/icons";
import { Button, message, Upload, Card, Typography } from "antd";
import { useAppDispatch } from "../../../hooks/reduxHooks";
import { uploadFileApi } from "../../../services/setup.service";

const { Text } = Typography;

const FileUpload = ({ url, setResponseData }) => {
  const dispatch = useAppDispatch();
  const [fileList, setFileList] = useState([]);
  const [uploading, setUploading] = useState(false);
  const [uploadedFile, setUploadedFile] = useState(null);

  const handleUpload = () => {
    const formData = new FormData();
    fileList.forEach((file) => {
      formData.append("file", file);
    });

    setUploading(true);
    dispatch(
      uploadFileApi({
        url: url,
        formData: formData,
        finalCallback: () => {
          setUploading(false);
        },
        successCallback: (response) => {
          setUploadedFile(fileList[0].name);
          setFileList([]);
          if (response?.support) {
            setResponseData(response?.support);
          } else {
            setResponseData(response);
          }
          message.success("File uploaded successfully!");
        },
        failureCallback: () => {
          message.error("File Uploading failed!");
        },
      })
    );
  };

  const props = {
    onRemove: (file) => {
      const index = fileList.indexOf(file);
      const newFileList = fileList.slice();
      newFileList.splice(index, 1);
      setFileList(newFileList);
    },
    beforeUpload: (file) => {
      setFileList([...fileList, file]);
      return false;
    },
    fileList,
  };

  return (
    <Card
      style={{
        maxWidth: 400,
        margin: "0 auto",
        // boxShadow: "0 4px 6px rgba(0, 0, 0, 0.1)",
        marginBottom: "48px",
      }}
      bodyStyle={{
        padding: 24,
        display: "flex",
        flexDirection: "column",
        gap: 16,
        alignItems: "center",
      }}
    >
      <Upload {...props}>
        <Button
          icon={<UploadOutlined />}
          size="large"
          style={{
            width: 200,
            // height: 45,
          }}
        >
          Select File
        </Button>
      </Upload>

      <Button
        type="primary"
        onClick={handleUpload}
        disabled={fileList.length === 0}
        loading={uploading}
        size="large"
        style={{
          width: 200,
          // height: 45,
        }}
      >
        {uploading ? "Analyzing" : "Start Upload"}
      </Button>

      {uploadedFile && (
        <div
          style={{
            display: "flex",
            alignItems: "center",
            justifyContent: "center",
            gap: 8,
            background: "#f0f2f5",
            padding: "8px 16px",
            borderRadius: 6,
            width: "100%",
          }}
        >
          <FileOutlined style={{ color: "#1890ff" }} />
          <Text ellipsis style={{ maxWidth: 280, textAlign: "center" }}>
            {uploadedFile}
          </Text>
        </div>
      )}
    </Card>
  );
};

export default FileUpload;
