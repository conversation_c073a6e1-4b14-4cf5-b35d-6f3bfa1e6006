import React, { useState, useEffect, useRef } from "react";
import { Card, Input, Button, Select, message } from "antd";
import { SendOutlined, CloseCircleOutlined } from "@ant-design/icons";
import "./ChatBubble.css";
import { RetreiveChat } from "../../hook/fetchdata";
import EmptyChat from "./EmptyChat";

function ChatPanel({
  onClearChat,
  onAssistantMessageClick,
  selectedAssistantIndex,
  setHighlight,
}) {
  const { Option } = Select;
  const [chatMessages, setChatMessages] = useState([]);
  const [query, setQuery] = useState("");
  const [loading, setLoading] = useState(false);
  const [typing, setTyping] = useState(false); // New state for handling typing animation
  const [detailLevel, setDetailLevel] = useState("level-1");
  const messagesEndRef = useRef(null);

  useEffect(() => {
    const storedChatMessages = JSON.parse(
      sessionStorage.getItem("chatMessages")
    );
    if (storedChatMessages) {
      setChatMessages(storedChatMessages);
    }
  }, []);

  useEffect(() => {
    if (messagesEndRef.current) {
      messagesEndRef.current.scrollIntoView({ behavior: "smooth" });
    }
  }, [chatMessages]);

  const handleSendQuery = async (query, detailLevel) => {
    setLoading(true);
    setTyping(true); // Show typing animation

    const userMessage = {
      role: "user",
      text: query,
      detailLevel,
      timestamp: Date.now(),
    };

    const updatedChats = [...chatMessages, userMessage];
    setChatMessages(updatedChats);
    sessionStorage.setItem("chatMessages", JSON.stringify(updatedChats));

    // Simulate typing animation
    setTimeout(async () => {
      try {
        const response = await RetreiveChat(query, detailLevel);
        const assistantMessage = {
          role: "assistant",
          text: response.response,
          detailLevel,
          sources: response.source_nodes,
          timestamp: Date.now(),
        };

        // Replace the typing message with the real assistant message
        const finalChats = [...updatedChats, assistantMessage];
        setChatMessages(finalChats);
        sessionStorage.setItem("chatMessages", JSON.stringify(finalChats));
        setTyping(false); // Stop the typing animation
      } catch (error) {
        console.error("Error fetching API:", error);
        setTyping(false); // Stop typing animation in case of an error
      }
      setLoading(false);
    }, 1500); // Delay before showing assistant's message (typing simulation)
  };

  const handleSend = async () => {
    if (query.trim()) {
      setQuery("");
      await handleSendQuery(query, detailLevel);
    }
  };

  const handleClearChat = () => {
    setHighlight(false);
    setChatMessages([]);
    sessionStorage.removeItem("chatMessages");
    onClearChat();
  };

  return (
    <Card
      className="chat-panel"
      style={{
        height: "100%",
        display: "flex",
        flexDirection: "column",
        maxWidth: "1200px",
        margin: "0 ",
        boxShadow: "0 4px 12px rgba(0,0,0,0.1)",
        borderRadius: "12px",
        minWidth: "600px",
      }}
      bodyStyle={{
        display: "flex",
        flexDirection: "column",
        height: "100%",
        padding: 0,
      }}
    >
      {/* Header */}
      <div
        style={{
          padding: "10px 24px",
          borderBottom: "1px solid #f0f0f0",
          display: "flex",
          justifyContent: "space-between",
          alignItems: "center",
        }}
      >
        <h2 style={{ margin: 0, fontWeight: "600" }}>Facts</h2>
        <Button
          onClick={handleClearChat}
          icon={<CloseCircleOutlined />}
          type="text"
          danger
          disabled={!chatMessages?.length > 0}
        >
          Clear Facts
        </Button>
      </div>

      {/* Messages Area */}
      <div
        style={{
          flex: 1,
          overflowY: "auto",
          padding: "24px",
          background: "#f9f9f9",
        }}
      >
        {chatMessages.length === 0 && !typing ? (
          <EmptyChat />
        ) : (
          <div className="chat-messages-container">
            {chatMessages.map((msg, index) => {
              if (!msg) return null;
              const bubbleClass =
                msg.role === "user" ? "user-bubble" : "assistant-bubble";
              return (
                <div key={index} className={`message-wrapper ${msg.role}`}>
                  <div
                    className={`${bubbleClass}`}
                    onClick={() =>
                      msg.role === "assistant" &&
                      onAssistantMessageClick(index, msg.sources)
                    }
                    style={{
                      backgroundColor:
                        selectedAssistantIndex === index ? "#e6f7ff" : "",
                      cursor: msg.role === "assistant" ? "pointer" : "default",
                    }}
                  >
                    <div className="message-content">{msg.text}</div>
                  </div>
                </div>
              );
            })}
            {typing && (
              <div className="message-wrapper assistant">
                <div className="assistant-bubble">
                  <div className="message-content typing-animation">
                    AI is typing
                  </div>
                </div>
              </div>
            )}

            <div ref={messagesEndRef} />
          </div>
        )}
      </div>

      {/* Input Area */}
      <div
        style={{
          padding: "16px 24px",
          background: "#fff",
          borderTop: "1px solid #f0f0f0",
        }}
      >
        <div style={{ display: "flex", alignItems: "center", gap: "8px" }}>
          <div style={{ flex: 1, position: "relative" }}>
            <Input
              placeholder="Type your message..."
              value={query}
              onChange={(e) => setQuery(e.target.value)}
              onKeyPress={(e) =>
                e.key === "Enter" && !e.shiftKey && handleSend()
              }
              style={{
                width: "100%",
                padding: "8px 120px 8px 16px",
                borderRadius: "8px",
              }}
            />
          </div>
          {/* Level selector */}
          <Select
            defaultValue="level-1"
            style={{
              width: "115px",
              borderRadius: "8px",
              height: "38px",
            }}
            onChange={setDetailLevel} // Use the function reference directly
          >
            <Option value="level-1">Simplified</Option>
            <Option value="level-2">Detailed</Option>
            <Option value="level-3">Explained</Option>
          </Select>

          <Button
            type="primary"
            onClick={handleSend}
            loading={loading}
            // icon={<SendOutlined />}
            style={{
              borderRadius: "8px",
              height: "38px",
              width: "38px",
              display: "flex",
              justifyContent: "center",
              alignItems: "center",
            }}
          >
            <iconify-icon
              icon="majesticons:send-line"
              width="24"
              height="24"
            ></iconify-icon>
          </Button>
        </div>
      </div>
    </Card>
  );
}

export default ChatPanel;
