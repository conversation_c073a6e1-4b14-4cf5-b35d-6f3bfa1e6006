import React, { useState } from "react";
import { useAppDispatch } from "../../hooks/reduxHooks";
import { markCtaAsResolvedApi } from "../../services/cta.service";

import { Modal, Descriptions, Tag, Avatar, Typography, Input } from "antd";
import { UserOutlined, TagOutlined, PhoneOutlined, MailOutlined } from "@ant-design/icons";
import { useNavigate } from "react-router-dom";
import { logout } from "../../store/slices/profile.slice";
const { Text } = Typography;

const ResolveCtaDialog = ({ visible, ctaData, onClose, onSuccess }) => {
  const dispatch = useAppDispatch();
  const navigate = useNavigate();
  const [remarks, setRemarks] = useState("");

  if (!visible || !ctaData) return null;

  const handleResolveWithRemarks = () => {
    dispatch(
      markCtaAsResolvedApi({
        data: { ...ctaData, remarks },
        finalCallback: () => {
          setRemarks("");
          onClose();
        },
        successCallback: () => {
          onSuccess();
        },
        failureCallback: (errors) => {},
      })
    );
  };

  return (
    <Modal
      transitionName=""
      maskTransitionName=""
      title="Mark As Resolved"
      open={visible}
      onOk={handleResolveWithRemarks}
      width={700}
      onCancel={() => {
        setRemarks("");
        onClose();
      }}
      okText="Resolve"
      cancelText="Cancel"
      okButtonProps={{
        style: {
          backgroundColor: "#3D90D7",
          borderColor: "#3D90D7",
          color: "#fff",
          zIndex: "10001",
        },
      }}
    >
      {ctaData ? (
        <div>
          <Descriptions
            bordered
            column={1}
            labelStyle={{
              width: "30%",
              fontWeight: 500,
            }}
            contentStyle={{
              width: "70%",
            }}
          >
            <Descriptions.Item label="Customer Name">
              <div
                style={{ display: "flex", alignItems: "center", gap: "8px" }}
              >
                <Avatar icon={<UserOutlined />} />
                <Text strong>{ctaData.customer_name || "N/A"}</Text>
              </div>
            </Descriptions.Item>
            {ctaData.customer_phone_number ? (
              <Descriptions.Item label="Contact Number">
                <Text>
                  <PhoneOutlined style={{ marginRight: "8px" }} />
                  {ctaData.customer_phone_number || "N/A"}
                </Text>
              </Descriptions.Item>
            ) : null}
            {ctaData.customer_email ? (
              <Descriptions.Item label="Email Address">
                <Text>
                  <MailOutlined style={{ marginRight: "8px" }} />
                  {ctaData.customer_email || "N/A"}
                </Text>
              </Descriptions.Item>
            ) : null}
            <Descriptions.Item label="Description">
              <Text>{ctaData.description || "No description provided"}</Text>
            </Descriptions.Item>
            <Descriptions.Item label="Type">
              <Tag icon={<TagOutlined />}>{ctaData.type || "N/A"}</Tag>
            </Descriptions.Item>
          </Descriptions>
        </div>
      ) : (
        <Text>No data available.</Text>
      )}
      <Text
        orientation="left"
        strong
        style={{
          fontSize: "16px",
          marginTop: "20px",
          marginBottom: "8px",
          display: "block",
          fontWeight: 500,
        }}
      >
        Remarks (optional)
      </Text>
      <Input.TextArea
        rows={4}
        value={remarks}
        onChange={(e) => setRemarks(e.target.value)}
        placeholder="Enter remarks for resolving the ticket"
      />
    </Modal>
  );
};

export default ResolveCtaDialog;
