import React, { useState } from "react";
import { Row, Col, Drawer } from "antd";
import ChatWindowSection from "./sections/ChatWindowSection";
import MetadataSection from "./sections/MetaDataSection";

const PlaygroundComponent = ({
  responseData,
  setResponseData,
  spinning,
  setSpinning,
  drawerData,
  setDrawerData,
}) => {
  const [visible, setVisible] = useState(false);
  const onClose = () => {
    setVisible(false);
  };
  return (
    <div>
      <Row style={{ height: "90vh" }}>
        <Col span={24}>
          <ChatWindowSection
            setVisible={setVisible}
            responseData={responseData}
            setResponseData={setResponseData}
            spinning={spinning}
            setSpinning={setSpinning}
            setDrawerData={setDrawerData}
          />
        </Col>
        <Col span={8}>
          <Drawer
            visible={visible}
            onClose={onClose}
            title={<div>Gathered Information</div>}
            placement="right"
            width={580}
          >
            <MetadataSection drawerData={drawerData} />
          </Drawer>
        </Col>
      </Row>
    </div>
  );
};

export default PlaygroundComponent;
