import httpBase from "../utils/http.utils";
import {
  FETCH_QUESTIONS,
  GET_ALL_SECTIONS,
  GET_ANSWER_BY_ID,
  UPDATE_SOURCE_NODES,
  GENERATE_CONFLICTS,
  REGENERATE_QNA,
  GET_ALL_SECTIONS_FILTERS,
} from "../constants/url";

export const getAllQuestionsApi = ({
  data,
  params,
  finalCallback,
  successCallback,
  failureCallback,
}) => {
  return async (dispatch) => {
    try {
      const response = await httpBase().get(`${FETCH_QUESTIONS}/${data}`, {
        params: params,
      });
      successCallback(response?.data);
    } catch (e) {
      failureCallback(e);
    } finally {
      finalCallback();
    }
  };
};

export const getAllSectionsApi = ({
  finalCallback,
  successCallback,
  failureCallback,
}) => {
  return async (dispatch) => {
    try {
      const response = await httpBase().post(GET_ALL_SECTIONS);
      successCallback(response?.data);
    } catch (e) {
      failureCallback(e);
    } finally {
      finalCallback();
    }
  };
};

export const getAnswerByIdApi = ({
  id,
  finalCallback,
  successCallback,
  failureCallback,
}) => {
  return async (dispatch) => {
    try {
      const response = await httpBase().get(`${GET_ANSWER_BY_ID}/${id}`);
      successCallback(response?.data);
    } catch (e) {
      failureCallback(e);
    } finally {
      finalCallback();
    }
  };
};

export const updateSourceNodeApi = ({
  data,
  params,
  finalCallback,
  successCallback,
  failureCallback,
}) => {
  return async (dispatch) => {
    try {
      const response = await httpBase().post(UPDATE_SOURCE_NODES, data, {
        params: params,
      });
      successCallback(response?.data);
    } catch (e) {
      failureCallback(e);
    } finally {
      finalCallback();
    }
  };
};

export const generateConflictsApi = ({
  data,
  params,
  finalCallback,
  successCallback,
  failureCallback,
}) => {
  return async (dispatch) => {
    try {
      const response = await httpBase().post(
        `${GENERATE_CONFLICTS}/${data}`,
        {},
        { params }
      );
      successCallback(response?.data);
    } catch (e) {
      failureCallback(e);
    } finally {
      finalCallback();
    }
  };
};

export const regenerateQuestionAPI = ({
  params,
  finalCallback,
  successCallback,
  failureCallback,
}) => {
  return async (dispatch) => {
    try {
      const response = await httpBase().post(`${REGENERATE_QNA}/${params}`);
      successCallback(response?.data);
    } catch (e) {
      failureCallback(e);
    } finally {
      finalCallback();
    }
  };
};

export const getAllFiltersApi = ({
  finalCallback,
  successCallback,
  failureCallback,
}) => {
  return async (dispatch) => {
    try {
      const response = await httpBase().get(GET_ALL_SECTIONS_FILTERS);
      successCallback(response?.data);
    } catch (e) {
      failureCallback(e);
    } finally {
      finalCallback();
    }
  };
};
