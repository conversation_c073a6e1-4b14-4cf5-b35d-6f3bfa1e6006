import React, { useState } from "react";
import RangePickerComponent from "../../components/common/datePickerRange";
import { useAppSelector } from "../../hooks/reduxHooks";
import MainCards from "./sections/MainCards";
import MessageAndUserTrendChart from "./sections/MessageAndUserTrendChart";
import LanguageCountChart from "./sections/LanguageCountChart";
import HumanInterventionTrend from "./sections/HumanInterventionTrend";
import ConversationTopics from "./sections/ConversationTopics";
import dayjs from "dayjs";

const DashboardComponent = ({ data }) => {
    const [dateRangeAntdValue, setDateRangeAntdValue] = useState([
      dayjs().subtract(7, "day"),
      dayjs(),
    ]);
  
  return (
    <div
      style={{
        margin: "10px 5px 10px 5px",
      }}>
      <div
        style={{
          paddingBottom: "20px",
          display: "flex",
          justifyContent: "space-between",
          alignItems: "center",
          flexWrap: "wrap",
        }}>
        <h1
          style={{
            fontWeight: 600,
            margin: 0,
            paddingRight: "20px",
          }}
        >
          Welcome, {localStorage.getItem("username")}!
        </h1>
        <RangePickerComponent
          value={dateRangeAntdValue}
          setValue={setDateRangeAntdValue}
          width="300px"
          additionalStyle={{
            borderRadius: "18px",
            border: "0px solid #d9d9d9",
            padding: "0 10px",
            height: "40px",
            fontSize: "14px",
            color: "#000",
            fontWeight: 500,
    
            boxShadow: "0 2px 8px rgba(0, 0, 0, 0.1)",
          }}
          allowClear={false}
        />
      </div>



      {/* here starts the msin content */}
      <MainCards ctaCount={data.ctaCount} creditBalance={data.creditBalance} />

      <div
        style={{
          display: "flex",
          justifyContent: "space-between",
          gap: "24px",
        }}>
        <ConversationTopics data={data.ctaTopics} topicCount={data.topicCount} />
        <HumanInterventionTrend data={data.messageCount || []} />
      </div>

      <div
        style={{
          display: "flex",
          justifyContent: "space-between",
          gap: "24px",
        }}>
        <MessageAndUserTrendChart messageData={data.messageCount} userData={data.uniqueUsersPerDay} />
        <LanguageCountChart data={data.languageCount} />
      </div>

    </div>
  );
};

export default DashboardComponent;
