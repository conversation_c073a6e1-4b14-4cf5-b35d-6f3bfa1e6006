import React, { useState } from "react";
import { Form, Input, Radio, Button, Typography, Card, message } from "antd";
import FileUpload from "../../../../components/common/FileUpload";
import { useAppDispatch } from "../../../../hooks/reduxHooks";
import { supportSaveApi } from "../../../../services/setup.service";
import QAList from "./qaList";

const SupportForm = () => {
  const dispatch = useAppDispatch();

  const { Text, Title } = Typography;

  const [form] = Form.useForm();
  const [loading, setLoading] = useState(false);

  // ...
  const [responseData, setResponseData] = useState();

  const onFinish = (values) => {
    setLoading(true);
    const finalObj = {
      ...values,
      text: responseData,
      parent_id: null,
    };

    dispatch(
      supportSaveApi({
        data: finalObj,
        finalCallback: () => {
          setLoading(false);
        },
        successCallback: (response) => {
          message.success(response?.message);
        },
        failureCallback: () => {
          message.error("Something went wrong!");
        },
      })
    );
  };

  return (
    <Card
      title="Support Configuration"
      style={{ maxWidth: 700, margin: "0 auto" }}
    >
      <Form form={form} layout="vertical" onFinish={onFinish} size="large">
        <Form.Item
          name="name"
          label="Support Name"
          rules={[
            {
              required: true,
              message: "Please enter support name",
            },
          ]}
        >
          <Input placeholder="Enter support name" style={{ borderRadius: 6 }} />
        </Form.Item>

        <Form.Item
          name="reply_type"
          label=" Support Type"
          rules={[
            {
              required: true,
              message: "Please select support type",
            },
          ]}
        >
          <Radio.Group size="small">
            <Radio value="direct_ai">AI Reply</Radio>
            <Radio value="saved_reply">Saved Reply</Radio>
            <Radio value="documents">Document Reply</Radio>
          </Radio.Group>
        </Form.Item>

        <Form.Item
          noStyle
          shouldUpdate={(prevValues, currentValues) =>
            prevValues.reply_type !== currentValues.reply_type
          }
        >
          {({ getFieldValue }) =>
            getFieldValue("reply_type") === "documents" && (
              <Form.Item
                rules={[
                  {
                    required: true,
                    message: "Please upload a document",
                  },
                ]}
              >
                {!responseData && (
                  <Title
                    strong
                    style={{
                      textAlign: "center",
                      fontSize: "18px",
                      color: "#BBBBBB",
                    }}
                  >
                    Upload support document
                  </Title>
                )}
                <FileUpload
                  url="/support_setup"
                  setResponseData={setResponseData}
                />
              </Form.Item>
            )
          }
        </Form.Item>

        <div>
          {responseData && (
            <>
              <Title
                style={{
                  fontSize: "21px",
                  textAlign: "center",
                  color: "#777777",
                }}
              >
                Generated questions and answers
              </Title>

              <QAList
                responseData={responseData}
                setResponseData={setResponseData}
              />
            </>
          )}
        </div>

        <Form.Item style={{ marginTop: 40, textAlign: "center" }}>
          <Button
            type="primary"
            htmlType="submit"
            size="large"
            loading={loading}
            style={{
              minWidth: 180,
              height: 35,
              borderRadius: 6,
              fontSize: 16,
            }}
          >
            Submit
          </Button>
        </Form.Item>
      </Form>
    </Card>
  );
};

export default SupportForm;
