import React, { useState } from "react";
import {
  <PERSON>,
  Button,
  Row,
  Col,
  Typography,
  <PERSON>,
  Badge,
  List,
  Divider,
} from "antd";
import {
  CheckOutlined,
  StarFilled,
  RobotOutlined,
  TeamOutlined,
  ThunderboltOutlined,
} from "@ant-design/icons";
import StageNavigation from "./StageNavigation";

const { Title, Text, Paragraph } = Typography;

const Stage2Selection = ({
  options,
  onSelect,
  onGoBack,
  loading,
  companyData,
}) => {
  const [selectedOption, setSelectedOption] = useState(null);
  const [hoveredCard, setHoveredCard] = useState(null);

  const getIcon = (iconType) => {
    switch (iconType) {
      case "🤖":
        return <RobotOutlined style={{ fontSize: 32, color: "#1890ff" }} />;
      case "👥":
        return <TeamOutlined style={{ fontSize: 32, color: "#52c41a" }} />;
      case "⚡":
        return (
          <ThunderboltOutlined style={{ fontSize: 32, color: "#fa8c16" }} />
        );
      default:
        return <RobotOutlined style={{ fontSize: 32, color: "#1890ff" }} />;
    }
  };

  const handleCardClick = (option) => {
    setSelectedOption(option.id);
  };

  const handleConfirmSelection = () => {
    const selected = options.find((opt) => opt.id === selectedOption);
    if (selected) {
      onSelect(selected);
    }
  };

  return (
    <div className="stage2-selection">
      <div className="stage2-selection__header">
        <Title level={2} style={{ marginBottom: 8, color: "#1890ff" }}>
          Choose Your Approach
        </Title>
        <Paragraph
          style={{ fontSize: "16px", color: "#595959", marginBottom: 24 }}
        >
          Based on your company profile, we've customized these solutions for{" "}
          <Text strong style={{ color: "#1890ff" }}>
            {companyData?.companyName}
          </Text>
          . Select the approach that best fits your needs.
        </Paragraph>
      </div>

      <Row gutter={[24, 24]} style={{ marginBottom: 32 }}>
        {options.map((option) => (
          <Col xs={24} lg={8} key={option.id}>
            <Card
              className={`option-card ${
                selectedOption === option.id ? "option-card--selected" : ""
              } ${hoveredCard === option.id ? "option-card--hovered" : ""}`}
              hoverable
              onClick={() => handleCardClick(option)}
              onMouseEnter={() => setHoveredCard(option.id)}
              onMouseLeave={() => setHoveredCard(null)}
              style={{
                height: "100%",
                borderRadius: 16,
                border:
                  selectedOption === option.id
                    ? "2px solid #1890ff"
                    : "1px solid #f0f0f0",
                boxShadow:
                  selectedOption === option.id
                    ? "0 8px 24px rgba(24, 144, 255, 0.2)"
                    : hoveredCard === option.id
                    ? "0 8px 24px rgba(0, 0, 0, 0.1)"
                    : "0 2px 8px rgba(0, 0, 0, 0.06)",
                transition: "all 0.3s ease",
                cursor: "pointer",
                position: "relative",
                overflow: "hidden",
              }}
              bodyStyle={{ padding: 24, height: "100%" }}
            >
              {/* Recommended Badge */}
              {option.recommended && (
                <Badge.Ribbon
                  text={
                    <Space>
                      <StarFilled />
                      Recommended
                    </Space>
                  }
                  color="gold"
                  style={{ top: 16, right: -8 }}
                />
              )}

              {/* Selection Indicator */}
              {selectedOption === option.id && (
                <div
                  style={{
                    position: "absolute",
                    top: 16,
                    left: 16,
                    width: 24,
                    height: 24,
                    borderRadius: "50%",
                    backgroundColor: "#1890ff",
                    display: "flex",
                    alignItems: "center",
                    justifyContent: "center",
                    zIndex: 1,
                  }}
                >
                  <CheckOutlined style={{ color: "white", fontSize: 14 }} />
                </div>
              )}

              <div
                style={{
                  display: "flex",
                  flexDirection: "column",
                  height: "100%",
                }}
              >
                {/* Icon and Title */}
                <div style={{ textAlign: "center", marginBottom: 16 }}>
                  <div style={{ marginBottom: 12 }}>{getIcon(option.icon)}</div>
                  <Title
                    level={4}
                    style={{ marginBottom: 8, color: "#262626" }}
                  >
                    {option.title}
                  </Title>
                  <Text type="secondary" style={{ fontSize: 14 }}>
                    {option.description}
                  </Text>
                </div>

                <Divider style={{ margin: "16px 0" }} />

                {/* Features List */}
                <div style={{ flex: 1 }}>
                  <Text strong style={{ color: "#595959", fontSize: 14 }}>
                    Key Features:
                  </Text>
                  <List
                    size="small"
                    dataSource={option.features}
                    renderItem={(feature) => (
                      <List.Item style={{ padding: "4px 0", border: "none" }}>
                        <Space>
                          <CheckOutlined
                            style={{ color: "#52c41a", fontSize: 12 }}
                          />
                          <Text style={{ fontSize: 13, color: "#595959" }}>
                            {feature}
                          </Text>
                        </Space>
                      </List.Item>
                    )}
                  />
                </div>

                {/* Select Button */}
                <Button
                  type={selectedOption === option.id ? "primary" : "default"}
                  block
                  style={{
                    marginTop: 16,
                    borderRadius: 8,
                    height: 40,
                    fontWeight: 500,
                    border:
                      selectedOption === option.id
                        ? "none"
                        : "1px solid #d9d9d9",
                  }}
                  onClick={(e) => {
                    e.stopPropagation();
                    handleCardClick(option);
                  }}
                >
                  {selectedOption === option.id ? "Selected" : "I prefer this"}
                </Button>
              </div>
            </Card>
          </Col>
        ))}
      </Row>

      {/* Navigation */}
      <StageNavigation
        currentStage={2}
        totalStages={2}
        onPrevious={onGoBack}
        onComplete={handleConfirmSelection}
        nextDisabled={!selectedOption}
        loading={loading}
        completeText="Complete Setup"
      />
    </div>
  );
};

export default Stage2Selection;
