import React from "react";
import {
  Layout,
  Typography,
  Form,
  Input,
  Select,
  Button,
  List,
  Popconfirm,
  Modal,
  Space,
  Divider,
} from "antd";
import {
  PlusOutlined,
  SaveOutlined,
  DeleteOutlined,
  ReloadOutlined,
  EditOutlined,
} from "@ant-design/icons";

const { Content } = Layout;
const { Title } = Typography;
const { TextArea } = Input;

const DocumentEditor = ({
  handleSave,
  handleUpdate,
  handleDelete,
  handleEdit,
  handleAddNew,
  getProductName,
  getCategoryName,
  handleCancel,
  editingDocument,
  modalVisible,
  documents,
  form,
}) => {
  return (
    <Layout style={{ margin: "0" }}>
      <Content style={{ padding: "0 10px" }}>
        <div style={{ background: "#fff", padding: 24, minHeight: 280 }}>
          <Title level={2}>
            <div style={{ display: "flex", justifyContent: "space-between" }}>
              Knowledge Base Editor
              {/* <div>
                <Button
                  type="primary"
                  icon={<PlusOutlined />}
                  onClick={handleAddNew}
                  style={{
                    backgroundColor: "rgb(100, 209, 11)",
                    // borderColor: "rgb(173, 251, 109)",
                    color: "white",
                    marginBottom: 16,
                  }}
                >
                  Add New Knowledge Base
                </Button>
              </div> */}
            </div>
          </Title>
          <Divider />
          <List
            itemLayout="vertical"
            dataSource={documents}
            renderItem={(doc) => (
              <List.Item
                style={{ padding: "10px", margin: "1px" }}
                key={doc.id}
                // actions={[
                //   <Button
                //     key="edit"
                //     icon={<EditOutlined />}
                //     onClick={() => handleEdit(doc)}
                //   >
                //     Edit
                //   </Button>,
                //   <Popconfirm
                //     key="delete"
                //     title="Are you sure you want to delete this document?"
                //     onConfirm={() => handleDelete(doc.id)}
                //     okText="Yes"
                //     cancelText="No"
                //   >
                //     <Button icon={<DeleteOutlined />} danger>
                //       Delete
                //     </Button>
                //   </Popconfirm>,
                // ]}
              >
                <Title level={5}>
                  <div
                    style={{ display: "flex", justifyContent: "space-between" }}
                  >
                    <div>
                      {doc.categories
                        ?.map((id) => getCategoryName(id))
                        .join(", ")}
                    </div>
                    <div>
                      {[
                        <Space>
                          <Button
                            key="edit"
                            icon={<EditOutlined />}
                            type="primary"
                            onClick={() => handleEdit(doc)}
                          >
                            Edit
                          </Button>

                          <Popconfirm
                            key="delete"
                            title="Are you sure you want to delete this document?"
                            onConfirm={() => handleDelete(doc.id)}
                            okText="Yes"
                            cancelText="No"
                          >
                            <Button icon={<DeleteOutlined />} danger>
                              Delete
                            </Button>
                          </Popconfirm>
                        </Space>,
                      ]}
                    </div>
                  </div>
                </Title>
                {/* <div>
                  <strong>Products:</strong>{" "}
                  {doc.products?.map((id) => getProductName(id)).join(", ")}
                </div>
               */}
              </List.Item>
            )}
            pagination
          />
        </div>
      </Content>
      <Modal
        title={
          editingDocument ? "Edit Knowledge Base" : "Add New Knowledge Base"
        }
        visible={modalVisible}
        onCancel={handleCancel}
        footer={null}
      >
        <Form
          form={form}
          layout="vertical"
          onFinish={editingDocument ? handleUpdate : handleSave}
        >
          <Form.Item
            name="title"
            label="Document Name"
            rules={[{ required: true }]}
          >
            <Input />
          </Form.Item>

          <Form.Item name="text" label="Document Content">
            <TextArea rows={6} />
          </Form.Item>
          <Form.Item>
            <Button type="primary" htmlType="submit" icon={<SaveOutlined />}>
              {editingDocument ? "Update" : "Save"}
            </Button>
            <Button style={{ marginLeft: 8 }} onClick={handleCancel}>
              Cancel
            </Button>
          </Form.Item>
        </Form>
      </Modal>
    </Layout>
  );
};

export default DocumentEditor;
