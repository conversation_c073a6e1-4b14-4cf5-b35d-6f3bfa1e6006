// Minimal Tenant Setup Styles - Matching Application Design
.tenant-setup {
  min-height: 100vh;
  background-color: #f2f2f2;
  // padding: 20px;

  &__container {
    width: 100%;
    padding-left: 10%;
    padding-right: 10%;
    // max-width: 1000px;
    margin: 0 auto;
  }

  &__header {
    text-align: center;
    margin-bottom: 32px;
    padding: 24px 0;
  }

  &__title {
    color: #262626 !important;
    font-size: 2rem !important;
    font-weight: 600 !important;
    margin-bottom: 8px !important;
    letter-spacing: -0.5px;
  }

  &__subtitle {
    color: #8c8c8c !important;
    font-size: 1rem;
    font-weight: 400;
  }

  &__progress {
    margin-bottom: 32px;
    padding-bottom: 24px;
    border-bottom: 1px solid #f0f0f0;
  }

  &__content {
    margin-top: 8px;
  }

  &__steps {
    margin-bottom: 24px;

    .ant-steps-item {
      &-title {
        font-weight: 500 !important;
        color: #262626 !important;
        font-size: 14px !important;
      }

      &-description {
        color: #8c8c8c !important;
        font-size: 12px !important;
      }

      &-icon {
        border-color: #d9d9d9 !important;
        background: #fafafa !important;

        .anticon {
          color: #8c8c8c !important;
        }
      }

      &-process &-icon {
        background: #1890ff !important;
        border-color: #1890ff !important;

        .anticon {
          color: white !important;
        }
      }

      &-finish &-icon {
        background: #52c41a !important;
        border-color: #52c41a !important;

        .anticon {
          color: white !important;
        }
      }
    }
  }

  &__progress-bar {
    margin-top: 8px;

    .ant-progress-bg {
      border-radius: 2px;
    }
  }

  &__progress-text {
    text-align: center;
    margin-top: 12px;
  }

  &__main-card {
    background: #ffffff !important;
    border-radius: 8px !important;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.06) !important;
    border: 1px solid #f0f0f0 !important;
    transition: all 0.2s ease;

    &:hover {
      box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1) !important;
    }
  }

  &__footer {
    text-align: center;
    margin-top: 24px;
    color: #8c8c8c;
  }
}

// Stage-specific styles
.stage1-business-info {
  &__header {
    margin-bottom: 24px;
    text-align: center;
  }

  .form-section-card {
    border-radius: 8px !important;
    box-shadow: 0 1px 4px rgba(0, 0, 0, 0.04) !important;
    border: 1px solid #f0f0f0 !important;
    margin-bottom: 16px;
    transition: all 0.2s ease;

    &:hover {
      box-shadow: 0 2px 8px rgba(0, 0, 0, 0.08) !important;
    }

    .ant-card-head {
      border-bottom: 1px solid #f0f0f0;
      padding: 16px 20px;

      .ant-card-head-title {
        font-size: 15px;
        font-weight: 500;
        color: #262626;
      }
    }

    .ant-card-body {
      padding: 20px;
    }
  }

  .professional-input,
  .professional-select,
  .professional-textarea {
    border-radius: 6px !important;
    border: 1px solid #d9d9d9 !important;
    transition: all 0.2s ease !important;

    &:hover {
      border-color: #40a9ff !important;
    }

    &:focus,
    &.ant-input-focused,
    &.ant-select-focused .ant-select-selector {
      border-color: #1890ff !important;
      box-shadow: 0 0 0 2px rgba(24, 144, 255, 0.1) !important;
    }
  }

  .professional-button {
    transition: all 0.2s ease;
    background: #1890ff;
    border-color: #1890ff;

    &:hover {
      background: #40a9ff !important;
      border-color: #40a9ff !important;
      transform: translateY(-1px);
    }
  }
}

.stage2-ai-response {
  &__header {
    margin-bottom: 24px;
    text-align: center;
  }

  .ai-response-card {
    transition: all 0.2s ease;
    border-radius: 8px;

    &--selected {
      border-color: #1890ff !important;
      box-shadow: 0 4px 12px rgba(24, 144, 255, 0.15) !important;

      .ant-card-body {
        background: rgba(24, 144, 255, 0.02);
      }
    }

    &--hovered:not(.ai-response-card--selected) {
      box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1) !important;
      transform: translateY(-1px);
    }

    .ant-badge-ribbon {
      .ant-badge-ribbon-text {
        font-size: 11px;
        font-weight: 500;
      }
    }
  }
}

.stage3-completion {
  &__header {
    margin-bottom: 24px;
    text-align: center;
  }

  .summary-card {
    border-radius: 8px !important;
    box-shadow: 0 1px 4px rgba(0, 0, 0, 0.04) !important;
    border: 1px solid #f0f0f0 !important;
    margin-bottom: 16px;
    transition: all 0.2s ease;

    &:hover {
      box-shadow: 0 2px 8px rgba(0, 0, 0, 0.08) !important;
    }

    .ant-card-head {
      border-bottom: 1px solid #f0f0f0;
      padding: 16px 20px;

      .ant-card-head-title {
        font-size: 15px;
        font-weight: 500;
        color: #262626;
      }
    }

    .ant-card-body {
      padding: 20px;
    }
  }
}

// Responsive Design
@media (max-width: 768px) {
  .tenant-setup {
    padding: 16px;

    &__title {
      font-size: 1.5rem !important;
    }

    &__main-card {
      .ant-card-body {
        padding: 24px 16px !important;
      }
    }

    .form-section-card,
    .summary-card {
      .ant-card-head {
        padding: 12px 16px;
      }

      .ant-card-body {
        padding: 16px;
      }
    }
  }
}

@media (max-width: 480px) {
  .tenant-setup {
    padding: 12px;

    &__title {
      font-size: 1.3rem !important;
    }
  }
}
