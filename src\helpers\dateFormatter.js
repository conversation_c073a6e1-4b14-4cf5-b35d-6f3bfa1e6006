// export const dateFormatter = (dateTime) => {
//   const date = new Date(dateTime);
//   const formattedDate = date.toISOString().split("T")[0];
//   return formattedDate;
// };

export const dateFormatter = (dateString) => {
  const date = new Date(dateString);

  // Extract date part
  const dateOptions = { month: "short", day: "numeric", year: "numeric" };
  const datePart = new Intl.DateTimeFormat("en-US", dateOptions).format(date);

  // Extract time part
  const timeOptions = { hour: "2-digit", minute: "2-digit", hour12: true };
  const timePart = new Intl.DateTimeFormat("en-US", timeOptions).format(date);

  return { datePart, timePart };
};
