import React, { useEffect, useRef, useState } from "react";
import { Drawer, <PERSON>tar, Tag, Button } from "antd";
import {
  UserOutlined,
  ArrowRightOutlined,
  ClockCircleOutlined,
} from "@ant-design/icons";
import { useDispatch } from "react-redux";
import { getUserWindowApi } from "../../services/userwindow.service";
import { transformString } from "../../helpers/channelNameTransformation";
import TimelineAlternative from "../timelineAlternative";
import { customerSummaryFlowApi } from "../../services/customers.service";
import { dateFormatter } from "../../helpers/dateFormatter";
import { debounceScroll } from "../../helpers/debounceScroll";

const CustomerProfileDrawer = ({
  visible,
  onClose,
  customer,
  selectedCustomerId,
}) => {
  const dispatch = useDispatch();
  const scrollRef = useRef(null);

  const [apiResponse, setApiResponse] = useState(null);

  const [timelineData, setTimelineData] = useState([]);

  const [totalPages, setTotalPages] = useState();

  const [page, setPage] = useState(1);

  const [showBackTopButton, setShowBackTopButton] = useState(false); //control visibility of the back to top button

  // Scroll to top
  const scrollToTop = () => {
    scrollRef.current.scrollTo({
      top: 0,
      behavior: "smooth",
    });
  };

  const fetchTimelineData = () => {
    dispatch(
      customerSummaryFlowApi({
        params: {
          customer_id: selectedCustomerId,
          page: page,
          page_size: "10",
        },
        finalCallback: () => {},
        successCallback: (response) => {
          const list = response?.data.map((item) => {
            return {
              label: (
                <>
                  <p
                    style={{
                      marginRight: "24px",
                      color: "#444444",
                      marginBottom: 0,
                      fontSize: "13px",
                    }}
                  >
                    {dateFormatter(item?.created_at).datePart}
                  </p>
                  <p
                    style={{
                      marginRight: "24px",
                      color: "#444444",
                      fontSize: "12px",
                    }}
                  >
                    {dateFormatter(item?.created_at).timePart}
                  </p>
                  {item?.categories.map((item) => (
                    <>
                      <Tag color="#8c8c8c" style={{ marginBottom: "8px" }}>
                        {transformString(item)}
                      </Tag>
                    </>
                  ))}
                </>
              ),
              children: (
                <div style={{}}>
                  <p style={{ color: "#444444" }}>{item?.contextualize}</p>
                </div>
              ),
              dot: (
                <ClockCircleOutlined
                  style={{
                    fontSize: "20px",
                  }}
                />
              ),
              style: { paddingBottom: "72px" },
            };
          });

          setPage((prevPage) => prevPage + 1);

          setTotalPages(response?.total_pages);

          setTimelineData((prevList) => [...prevList, ...list]);
        },
        failureCallback: (errors) => {},
      })
    );
  };

  const fetchInitialTimelineData = () => {
    dispatch(
      customerSummaryFlowApi({
        params: {
          customer_id: selectedCustomerId,
          page: 1,
          page_size: "10",
        },
        finalCallback: () => {},
        successCallback: (response) => {
          const list = response?.data.map((item) => {
            return {
              label: (
                <>
                  <p
                    style={{
                      marginRight: "24px",
                      color: "#444444",
                      marginBottom: 0,
                      fontSize: "13px",
                    }}
                  >
                    {dateFormatter(item?.created_at).datePart}
                  </p>
                  <p
                    style={{
                      marginRight: "24px",
                      color: "#444444",
                      fontSize: "12px",
                    }}
                  >
                    {dateFormatter(item?.created_at).timePart}
                  </p>
                  {item?.categories.map((item) => (
                    <>
                      <Tag color="#8c8c8c" style={{ marginBottom: "8px" }}>
                        {transformString(item)}
                      </Tag>
                    </>
                  ))}
                </>
              ),
              children: (
                <div style={{}}>
                  <p style={{ color: "#444444" }}>{item?.contextualize}</p>
                </div>
              ),
              dot: (
                <ClockCircleOutlined
                  style={{
                    fontSize: "20px",
                  }}
                />
              ),
              style: { paddingBottom: "72px" },
            };
          });

          setPage(2);

          setTotalPages(response?.total_pages);

          setTimelineData(list);
        },
        failureCallback: (errors) => {},
      })
    );
  };

  const handleScroll = debounceScroll(() => {
    const { scrollTop, scrollHeight, clientHeight } = scrollRef.current;

    // Show the back to top button when user scrolls
    if (scrollTop > 200) {
      setShowBackTopButton(true);
    } else {
      setShowBackTopButton(false);
    }

    if (scrollTop + clientHeight + 60 >= scrollHeight && page <= totalPages) {
      fetchTimelineData();
    }
  }, 100);

  useEffect(() => {
    if (customer?.customer_id && visible) {
      const successCallback = (response) => {
        setApiResponse(response);
      };

      const failureCallback = (error) => {
        console.error("Failed to fetch customer data:", error);
      };

      const finalCallback = () => {
        console.log("API call completed.");
      };

      dispatch(
        getUserWindowApi({
          userId: customer.customer_id,
          successCallback,
          failureCallback,
          finalCallback,
        })
      );
    }
  }, [customer, dispatch, visible]);

  useEffect(() => {
    fetchInitialTimelineData();
  }, [selectedCustomerId]);

  return (
    <Drawer
      title="Customer Profile"
      placement="right"
      onClose={onClose}
      visible={visible}
      width={450}
      mask={false}
      closeIcon={<ArrowRightOutlined />}
      bodyStyle={{
        padding: 0, // Remove padding here
      }}
    >
      <div
        ref={scrollRef}
        onScroll={handleScroll}
        style={{
          height: "100%",
          overflowY: "auto",
          overflowX: "hidden",
          paddingRight: "36px",
          paddingLeft: "24px",
        }}
      >
        <div style={{ textAlign: "center", marginBottom: "24px" }}>
          <Avatar
            size={64}
            icon={<UserOutlined />}
            style={{ marginBottom: "16px", marginTop: "18px" }}
          />
          <h3 style={{ color: "#444444" }}>{customer?.customer_name}</h3>
        </div>
        {apiResponse && (
          <div>
            <div
              style={{
                marginBottom: "16px",
                display: "flex",
                justifyContent: "space-between",
              }}
            >
              <h4 style={{ fontWeight: "bold", color: "#777777" }}>Product:</h4>
              <div>
                {apiResponse.trending_product?.map(() => {
                  return (
                    <p style={{ color: "#444444" }}>
                      {apiResponse.trending_product || "N/A"}
                    </p>
                  );
                })}
              </div>
            </div>

            <div
              style={{
                marginBottom: "16px",
                display: "flex",
                justifyContent: "space-between",
              }}
            >
              {/* <h4 style={{ fontWeight: "bold", color: "#777777" }}>Sentiment:</h4> */}
              <h4 style={{ fontWeight: "bold", color: "#777777" }}>
                Sentiment:
              </h4>
              {apiResponse?.sentiment && (
                <Tag color="gray">{apiResponse.sentiment}</Tag>
              )}
            </div>

            <div
              style={{
                marginBottom: "16px",
                display: "flex",
                justifyContent: "space-between",
              }}
            >
              <h4 style={{ color: "#777777", fontWeight: "bold" }}>
                Language:
              </h4>
              <p style={{ color: "#444444" }}>
                {apiResponse.language || "N/A"}
              </p>
            </div>

            <div
              style={{
                marginBottom: "16px",
                display: "flex",
                justifyContent: "space-between",
              }}
            >
              <h4 style={{ color: "#777777", fontWeight: "bold" }}>
                Phone Number:
              </h4>
              <p style={{ color: "#444444" }}>
                {apiResponse.additional_information?.phone_number || "N/A"}
              </p>
            </div>

            <div
              style={{
                marginBottom: "16px",
                display: "flex",
                justifyContent: "space-between",
              }}
            >
              <h4 style={{ fontWeight: "bold", color: "#777777" }}>
                Course Activated:
              </h4>
              <p style={{ color: "#444444" }}>
                {apiResponse.additional_information?.is_course_activated
                  ? "Yes"
                  : "No"}
              </p>
            </div>

            <div
              style={{
                marginBottom: "16px",
                display: "flex",
                justifyContent: "space-between",
              }}
            >
              <h4 style={{ fontWeight: "bold", color: "#777777" }}>
                Payment Done:
              </h4>
              <p style={{ color: "#444444" }}>
                {apiResponse.additional_information?.is_payment_done
                  ? "Yes"
                  : "No"}
              </p>
            </div>

            <div style={{ marginBottom: "16px" }}>
              <h4 style={{ fontWeight: "bold", color: "#777777" }}>
                Latest Summary:
              </h4>
              <p style={{ color: "#444444" }}>
                {apiResponse.latest_summary || "N/A"}
              </p>
            </div>
            <hr
              style={{
                marginTop: "36px",
                marginBottom: "36px",
              }}
            />
            <div style={{}}>
              <h4
                style={{
                  fontWeight: "bold",
                  marginBottom: "42px",
                  textAlign: "center",
                  fontSize: "18px",
                  color: "#444444",
                }}
              >
                Activity Log
              </h4>
              {timelineData?.length > 0 ? (
                <TimelineAlternative timelineData={timelineData} />
              ) : (
                <p
                  style={{
                    textAlign: "center",
                    color: "#C0C0C0",
                  }}
                >
                  Activity log is not available
                </p>
              )}
            </div>
          </div>
        )}
      </div>
      {showBackTopButton && (
        <Button
          type="primary"
          shape="circle"
          icon={<span style={{ fontSize: 18 }}>↑</span>}
          onClick={() => {
            scrollToTop();
          }}
          className="back-to-top-button"
        />
      )}
    </Drawer>
  );
};

export default CustomerProfileDrawer;
