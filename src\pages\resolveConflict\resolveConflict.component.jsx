import React from "react";
import { Col, Row } from "antd";
import QuestionsSection from "./sections/QuestionsSection";
import QuestionAnswerSection from "./sections/QuestionAnswerSection";

const ResolveConflictComponent = ({
  question,
  setQuestion,
  questionId,
  setQuestionId,
  setAnswer,
  answer,
  setSources,
  sources,
}) => {
  return (
    <div
      style={{
        display: "flex",
        justifyContent: "center",
      }}
    >
      <Row
        gutter={24}
        style={{
          width: "99.5%",
        }}
      >
        <Col span={10}>
          <div
            style={{
              height: "90vh",
              borderRadius: "8px",
              display: "flex",
              flexDirection: "column",
              justifyContent: "space-between",
              position: "sticky",
            }}
          >
            <QuestionsSection
              setQuestion={setQuestion}
              questionId={questionId}
              setQuestionId={setQuestionId}
              setAnswer={setAnswer}
              setSources={setSources}
            />
          </div>
        </Col>
        <Col span={14}>
          <div
            style={{
              height: "90vh",
              borderRadius: "8px",
              overflow: "auto",
            }}
          >
            <QuestionAnswerSection
              question={question}
              questionId={questionId}
              answer={answer}
              setAnswer={setAnswer}
              sources={sources}
              setSources={setSources}
            />
          </div>
        </Col>
      </Row>
    </div>
  );
};

export default ResolveConflictComponent;
