import React from "react";
import { Card, Steps, Row, Col, Typography, Progress } from "antd";
import Stage1Form from "./components/Stage1Form";
import Stage2Selection from "./components/Stage2Selection";
import "./tenantSetup.styles.scss";

const { Title, Text } = Typography;
const { Step } = Steps;

const TenantSetupComponent = ({
  currentStage,
  setupData,
  responseOptions,
  loading,
  onStage1Submit,
  onStage2Submit,
  onGoBack
}) => {
  const steps = [
    {
      title: "Company Information",
      description: "Tell us about your business"
    },
    {
      title: "Choose Your Approach",
      description: "Select your preferred solution"
    }
  ];

  const renderStageContent = () => {
    switch (currentStage) {
      case 1:
        return (
          <Stage1Form
            initialData={setupData.stage1}
            onSubmit={onStage1Submit}
            loading={loading}
          />
        );
      case 2:
        return (
          <Stage2Selection
            options={responseOptions}
            onSelect={onStage2Submit}
            onGoBack={onGoBack}
            loading={loading}
            companyData={setupData.stage1}
          />
        );
      default:
        return null;
    }
  };

  return (
    <div className="tenant-setup">
      <div className="tenant-setup__container">
        {/* Header Section */}
        <div className="tenant-setup__header">
          <Title level={1} className="tenant-setup__title">
            Welcome to Echo AI
          </Title>
          <Text className="tenant-setup__subtitle">
            Let's set up your workspace in just a few steps
          </Text>
        </div>

        {/* Progress Section */}
        <div className="tenant-setup__progress">
          <Steps
            current={currentStage - 1}
            size="small"
            className="tenant-setup__steps"
          >
            {steps.map((step, index) => (
              <Step
                key={index}
                title={step.title}
                description={step.description}
              />
            ))}
          </Steps>
          
          <div className="tenant-setup__progress-bar">
            <Progress
              percent={(currentStage / steps.length) * 100}
              showInfo={false}
              strokeColor={{
                '0%': '#108ee9',
                '100%': '#87d068',
              }}
            />
          </div>
        </div>

        {/* Main Content */}
        <Row justify="center">
          <Col xs={24} sm={20} md={16} lg={14} xl={12}>
            <Card
              className="tenant-setup__card"
              bordered={false}
              bodyStyle={{ padding: '40px' }}
            >
              {renderStageContent()}
            </Card>
          </Col>
        </Row>

        {/* Footer */}
        <div className="tenant-setup__footer">
          <Text type="secondary">
            Step {currentStage} of {steps.length}
          </Text>
        </div>
      </div>
    </div>
  );
};

export default TenantSetupComponent;
