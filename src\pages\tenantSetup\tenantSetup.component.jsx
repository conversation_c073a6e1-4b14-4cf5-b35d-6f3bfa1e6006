import { Card, Steps, Row, Col, Typography, Progress, Space } from "antd";
import { CheckCircleOutlined, FormOutlined, MessageOutlined, RocketOutlined } from "@ant-design/icons";
import Stage1BusinessInfo from "./components/Stage1BusinessInfo";
import Stage2AIResponse from "./components/Stage2AIResponse";
import Stage3Completion from "./components/Stage3Completion";
import "./tenantSetup.styles.scss";

const { Title, Text } = Typography;

const TenantSetupComponent = ({
  currentStage,
  setupData,
  staticData,
  aiResponses,
  loading,
  onStage1Submit,
  onStage2Submit,
  onStage3Submit,
  onStageNavigation
}) => {
  const steps = [
    {
      title: "Business Information",
      description: "Tell us about your business",
      icon: <FormOutlined />,
      status: currentStage > 1 ? "finish" : currentStage === 1 ? "process" : "wait"
    },
    {
      title: "AI Response Style",
      description: "Choose your preferred response type",
      icon: <MessageOutlined />,
      status: currentStage > 2 ? "finish" : currentStage === 2 ? "process" : "wait"
    },
    {
      title: "Setup Complete",
      description: "Review and confirm your settings",
      icon: <RocketOutlined />,
      status: currentStage === 3 ? "process" : "wait"
    }
  ];

  const renderStageContent = () => {
    switch (currentStage) {
      case 1:
        return (
          <Stage1BusinessInfo
            initialData={setupData.stage1}
            staticData={staticData}
            onSubmit={onStage1Submit}
            loading={loading}
          />
        );
      case 2:
        return (
          <Stage2AIResponse
            aiResponses={aiResponses}
            userQuestion={setupData.stage1?.userQuestion}
            onSelect={onStage2Submit}
            onBack={() => onStageNavigation("back")}
            loading={loading}
          />
        );
      case 3:
        return (
          <Stage3Completion
            setupData={setupData}
            onComplete={onStage3Submit}
            onBack={() => onStageNavigation("back")}
            loading={loading}
          />
        );
      default:
        return null;
    }
  };

  const getProgressPercent = () => {
    return Math.round((currentStage / 3) * 100);
  };

  return (
    <div className="tenant-setup">
      <div className="tenant-setup__background">
        <div className="tenant-setup__background-pattern"></div>
      </div>
      
      <div className="tenant-setup__container">
        {/* Header Section */}
        <div className="tenant-setup__header">
          <Space direction="vertical" size="small" style={{ textAlign: "center", width: "100%" }}>
            <Title level={1} className="tenant-setup__title">
              Welcome to Echo AI
            </Title>
            <Text className="tenant-setup__subtitle">
              Let's set up your AI assistant in just a few simple steps
            </Text>
          </Space>
        </div>

        {/* Progress Section */}
        <div className="tenant-setup__progress">
          <Card className="tenant-setup__progress-card" bordered={false}>
            <Steps
              current={currentStage - 1}
              size="default"
              className="tenant-setup__steps"
              items={steps}
            />
            
            <div className="tenant-setup__progress-bar">
              <Progress
                percent={getProgressPercent()}
                showInfo={false}
                strokeColor={{
                  '0%': '#1890ff',
                  '50%': '#52c41a',
                  '100%': '#722ed1',
                }}
                strokeWidth={6}
                trailColor="#f0f0f0"
              />
              <div className="tenant-setup__progress-text">
                <Text type="secondary">
                  Step {currentStage} of 3 - {getProgressPercent()}% Complete
                </Text>
              </div>
            </div>
          </Card>
        </div>

        {/* Main Content */}
        <Row justify="center">
          <Col xs={24} sm={22} md={20} lg={18} xl={16}>
            <Card
              className="tenant-setup__main-card"
              bordered={false}
              styles={{ 
                body: { 
                  padding: "48px 40px",
                  minHeight: "500px"
                } 
              }}
            >
              {renderStageContent()}
            </Card>
          </Col>
        </Row>

        {/* Footer */}
        <div className="tenant-setup__footer">
          <Space>
            <CheckCircleOutlined style={{ color: "#52c41a" }} />
            <Text type="secondary" style={{ fontSize: "14px" }}>
              Your data is secure and encrypted
            </Text>
          </Space>
        </div>
      </div>
    </div>
  );
};

export default TenantSetupComponent;
