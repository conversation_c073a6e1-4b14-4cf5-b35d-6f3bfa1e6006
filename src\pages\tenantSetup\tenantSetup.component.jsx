import { Card, Steps, Row, Col, Typography, Progress } from "antd";
import {
  CheckCircleOutlined,
  FormOutlined,
  MessageOutlined,
  RocketOutlined,
} from "@ant-design/icons";
import Stage1BusinessInfo from "./components/Stage1BusinessInfo";
import Stage2AIGoalAndResponse from "./components/Stage2AIGoalAndResponse";
import Stage3Completion from "./components/Stage3Completion";
import "./tenantSetup.minimal.scss";

const { Title, Text } = Typography;

const TenantSetupComponent = ({
  currentStage,
  setupData,
  staticData,
  loading,
  onStage1Submit,
  onStage2Submit,
  onStage3Submit,
  onStageNavigation,
}) => {
  const steps = [
    {
      title: "Business Information",
      description: "Tell us about your business",
      icon: <FormOutlined />,
      status:
        currentStage > 1 ? "finish" : currentStage === 1 ? "process" : "wait",
    },
    {
      title: "AI Response Style",
      description: "Choose your preferred response type",
      icon: <MessageOutlined />,
      status:
        currentStage > 2 ? "finish" : currentStage === 2 ? "process" : "wait",
    },
    {
      title: "Setup Complete",
      description: "Review and confirm your settings",
      icon: <RocketOutlined />,
      status: currentStage === 3 ? "process" : "wait",
    },
  ];

  const renderStageContent = () => {
    switch (currentStage) {
      case 1:
        return (
          <Stage1BusinessInfo
            initialData={setupData.stage1}
            staticData={staticData}
            onSubmit={onStage1Submit}
            loading={loading}
          />
        );
      case 2:
        return (
          <Stage2AIGoalAndResponse
            staticData={staticData}
            onSubmit={onStage2Submit}
            onBack={() => onStageNavigation("back")}
            loading={loading}
          />
        );
      case 3:
        return (
          <Stage3Completion
            setupData={setupData}
            onComplete={onStage3Submit}
            onBack={() => onStageNavigation("back")}
            loading={loading}
          />
        );
      default:
        return null;
    }
  };

  const getProgressPercent = () => {
    return Math.round((currentStage / 3) * 100);
  };

  return (
    <div className="tenant-setup">
      <div className="tenant-setup__container">
        {/* Main Content Card with Integrated Header and Progress */}
        <Row justify="center" style={{ margin: "40px 0" }}>
          <Col span={22}>
            <Card
              className="tenant-setup__main-card"
              bordered={false}
              styles={{
                body: {
                  padding: "32px 24px",
                  minHeight: "500px",
                },
              }}
            >
              {/* Header Section */}
              <div className="tenant-setup__header">
                <Title level={1} className="tenant-setup__title">
                  Eko Tenant Setup
                </Title>
                <Text className="tenant-setup__subtitle">
                  Let's set up your AI assistant in just a few simple steps
                </Text>
              </div>

              {/* Progress Section */}
              <div className="tenant-setup__progress">
                <Steps
                  current={currentStage - 1}
                  size="default"
                  className="tenant-setup__steps"
                  items={steps}
                />

                <div className="tenant-setup__progress-bar">
                  <Progress
                    percent={getProgressPercent()}
                    showInfo={false}
                    strokeColor={{
                      "0%": "#1890ff",
                      "100%": "#52c41a",
                    }}
                    strokeWidth={6}
                    trailColor="#f0f0f0"
                    strokeLinecap="round"
                  />
                  <div className="tenant-setup__progress-text">
                    <Text strong style={{ fontSize: "14px", color: "#888888" }}>
                      Step {currentStage} of 3 - {getProgressPercent()}%
                      Complete
                    </Text>
                  </div>
                </div>
              </div>

              {/* Stage Content */}
              <div className="tenant-setup__content">
                {renderStageContent()}
              </div>
            </Card>
          </Col>
        </Row>

        {/* Footer */}
        <div className="tenant-setup__footer">
          <Text type="secondary" style={{ fontSize: "13px" }}>
            <CheckCircleOutlined
              style={{ color: "#52c41a", marginRight: "6px" }}
            />
            Your data is secure and encrypted
          </Text>
        </div>
      </div>
    </div>
  );
};

export default TenantSetupComponent;
