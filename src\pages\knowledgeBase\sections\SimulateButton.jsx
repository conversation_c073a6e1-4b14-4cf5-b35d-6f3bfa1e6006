import React, { useState } from "react";
import { Button } from "antd";

const SimulateButton = ({ isHighlighted, setIsHighlighted }) => {
  const handleClick = () => {
    setIsHighlighted(!isHighlighted);
  };

  return (
    <Button
      type={isHighlighted ? "primary" : "default"}
      onClick={handleClick}
      style={{
        borderRadius: "18px",
        background: isHighlighted ? "#BDDCF4" : "#fff",
        color: isHighlighted ? "#0290FC" : "grey",
        border: isHighlighted ? "none" : "solid 1px lightgrey",
      }}
    >
      Simulate
    </Button>
  );
};

export default SimulateButton;
