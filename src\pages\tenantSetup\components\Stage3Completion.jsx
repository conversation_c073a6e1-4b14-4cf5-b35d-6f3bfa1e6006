import {
  But<PERSON>,
  Row,
  Col,
  Typography,
  Space,
  Card,
  Divider,
  Tag,
  Result,
} from "antd";
import {
  ArrowLeftOutlined,
  RocketOutlined,
  CheckCircleOutlined,
  BankOutlined,
  AimOutlined,
  ThunderboltOutlined,
} from "@ant-design/icons";

const { Title, Text, Paragraph } = Typography;

const Stage3Completion = ({ setupData, onComplete, onBack, loading }) => {
  const handleComplete = () => {
    onComplete({
      completedAt: new Date().toISOString(),
    });
  };

  const getBusinessTypeLabel = (value) => {
    const types = {
      ecommerce: "E-commerce",
      saas: "SaaS/Technology",
      service: "Service Business",
      retail: "Retail",
      healthcare: "Healthcare",
      education: "Education",
      finance: "Finance",
      consulting: "Consulting",
      other: "Other",
    };
    return types[value] || value;
  };

  const getBusinessGoalLabel = (value) => {
    const goals = {
      improve_support: "Improve Customer Support",
      reduce_costs: "Reduce Operational Costs",
      scale_business: "Scale Business Operations",
      enhance_experience: "Enhance Customer Experience",
      automate_processes: "Automate Business Processes",
      increase_efficiency: "Increase Team Efficiency",
    };
    return goals[value] || value;
  };

  const getLanguageLabel = (value) => {
    const languages = {
      english: "English",
      nepali: "Nepali",
      auto: "Auto-Detect",
    };
    return languages[value] || value;
  };

  const getCTALabel = (value) => {
    const ctas = {
      contact_support: "Contact Support",
      schedule_demo: "Schedule Demo",
      get_quote: "Get Quote",
      learn_more: "Learn More",
      start_trial: "Start Free Trial",
      book_consultation: "Book Consultation",
    };
    return ctas[value] || value;
  };

  return (
    <div className="stage3-completion">
      <div className="stage3-completion__header">
        <Result
          icon={<CheckCircleOutlined style={{ color: "#52c41a" }} />}
          title={
            <Title level={2} style={{ color: "#262626", marginBottom: 8 }}>
              Setup Almost Complete!
            </Title>
          }
          subTitle={
            <Paragraph
              style={{ fontSize: "15px", color: "#8c8c8c", marginBottom: 24 }}
            >
              Please review your configuration below and confirm to complete the
              setup
            </Paragraph>
          }
        />
      </div>

      {/* Setup Summary */}
      <Row gutter={[24, 24]} style={{ marginBottom: 32 }}>
        {/* Business Information */}
        <Col xs={24} lg={12}>
          <Card
            title={
              <Space>
                <BankOutlined style={{ color: "#1890ff" }} />
                <Text strong>Business Information</Text>
              </Space>
            }
            className="summary-card"
            style={{ height: "100%" }}
          >
            <Space direction="vertical" size={12} style={{ width: "100%" }}>
              <div>
                <Text type="secondary" style={{ fontSize: 12 }}>
                  Business Name
                </Text>
                <div>
                  <Text strong>{setupData.stage1?.businessName}</Text>
                </div>
              </div>

              <div>
                <Text type="secondary" style={{ fontSize: 12 }}>
                  Business Type
                </Text>
                <div>
                  <Tag color="blue">
                    {getBusinessTypeLabel(setupData.stage1?.businessType)}
                  </Tag>
                </div>
              </div>

              <div>
                <Text type="secondary" style={{ fontSize: 12 }}>
                  Contact Number
                </Text>
                <div>
                  <Text>{setupData.stage1?.contactNumber}</Text>
                </div>
              </div>

              <div>
                <Text type="secondary" style={{ fontSize: 12 }}>
                  Description
                </Text>
                <div>
                  <Text style={{ fontSize: 13 }}>
                    {setupData.stage1?.businessDescription}
                  </Text>
                </div>
              </div>
            </Space>
          </Card>
        </Col>

        {/* Business Goal & Preferences */}
        <Col xs={24} lg={12}>
          <Card
            title={
              <Space>
                <AimOutlined style={{ color: "#52c41a" }} />
                <Text strong>Goals & Preferences</Text>
              </Space>
            }
            className="summary-card"
            style={{ height: "100%" }}
          >
            <Space direction="vertical" size={12} style={{ width: "100%" }}>
              <div>
                <Text type="secondary" style={{ fontSize: 12 }}>
                  Business Goal
                </Text>
                <div>
                  <Tag color="green">
                    {getBusinessGoalLabel(setupData.stage1?.businessGoal)}
                  </Tag>
                </div>
              </div>

              <div>
                <Text type="secondary" style={{ fontSize: 12 }}>
                  Languages
                </Text>
                <div>
                  <Space wrap>
                    {setupData.stage1?.languages?.map((lang) => (
                      <Tag key={lang} color="purple">
                        {getLanguageLabel(lang)}
                      </Tag>
                    ))}
                  </Space>
                </div>
              </div>

              <div>
                <Text type="secondary" style={{ fontSize: 12 }}>
                  Call to Actions
                </Text>
                <div>
                  <Space wrap>
                    {setupData.stage1?.callToActions?.map((cta) => (
                      <Tag key={cta} color="orange">
                        {getCTALabel(cta)}
                      </Tag>
                    ))}
                  </Space>
                </div>
              </div>
            </Space>
          </Card>
        </Col>

        {/* Selected AI Response Style */}
        <Col xs={24}>
          <Card
            title={
              <Space>
                <ThunderboltOutlined style={{ color: "#722ed1" }} />
                <Text strong>Selected AI Response Style</Text>
              </Space>
            }
            className="summary-card"
          >
            <Row gutter={16} align="middle">
              <Col>
                <div style={{ fontSize: "24px" }}>
                  {setupData.stage2?.selectedResponse?.icon}
                </div>
              </Col>
              <Col flex={1}>
                <Space direction="vertical" size={4}>
                  <Text strong style={{ fontSize: 16 }}>
                    {setupData.stage2?.selectedResponse?.title}
                  </Text>
                  <Text type="secondary" style={{ fontSize: 14 }}>
                    {setupData.stage2?.selectedResponse?.description}
                  </Text>
                  <Space wrap>
                    {setupData.stage2?.selectedResponse?.features?.map(
                      (feature, idx) => (
                        <Tag
                          key={idx}
                          color="geekblue"
                          style={{ fontSize: 11 }}
                        >
                          {feature}
                        </Tag>
                      )
                    )}
                  </Space>
                </Space>
              </Col>
            </Row>
          </Card>
        </Col>
      </Row>

      <Divider />

      {/* Navigation Buttons */}
      <div
        style={{
          display: "flex",
          justifyContent: "space-between",
          alignItems: "center",
        }}
      >
        <Button
          icon={<ArrowLeftOutlined />}
          onClick={onBack}
          size="middle"
          style={{
            borderRadius: "6px",
            height: "40px",
            paddingLeft: "20px",
            paddingRight: "20px",
            fontSize: "14px",
          }}
        >
          Back to AI Response
        </Button>

        <Button
          type="primary"
          icon={<RocketOutlined />}
          onClick={handleComplete}
          loading={loading}
          size="middle"
          style={{
            borderRadius: "6px",
            height: "40px",
            paddingLeft: "24px",
            paddingRight: "24px",
            fontSize: "14px",
            fontWeight: "500",
          }}
        >
          Complete Setup & Launch
        </Button>
      </div>

      {/* Success Message */}
      <div style={{ marginTop: 24, textAlign: "center" }}>
        <Text type="secondary" style={{ fontSize: 14 }}>
          🎉 You're all set! Your AI assistant will be ready to use once setup
          is complete.
        </Text>
      </div>
    </div>
  );
};

export default Stage3Completion;
