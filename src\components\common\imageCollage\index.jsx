import React from "react";
import Masonry from "react-masonry-css";

const ImageCollage = ({ data, setIsChatBubbleModalOpen }) => {
  const breakpointColumns = {
    default: 3,
    1100: 2,
    700: 1,
  };

  return (
    <Masonry
      breakpointCols={breakpointColumns}
      className="masonry-grid"
      columnClassName="masonry-grid_column"
      onClick={() => {
        setIsChatBubbleModalOpen(true);
      }}
    >
      {data.map((src, index) => (
        <div key={index} className="masonry-item">
          {/* width: 150 height: 150 200 250 300 350 */}
          <img src={src} alt={`Masonry ${index}`} width={250} height={250} />
        </div>
      ))}
    </Masonry>
  );
};

export default ImageCollage;
