import React, { useState } from "react";
import { Row, Col, Typography, Input, Card } from "antd";
import MultiFileUpload from "../../components/MultiFileUpload";
import { InboxOutlined, SearchOutlined } from "@ant-design/icons";
import FixedSearchbar from "./sections/FixedSearchbar";
import TocComponent from "./sections/TocComponent";
import ScrollableList from "../../components/MultiFileUpload/ScrollableList";

const KnowledgebaseComponent = () => {
  const { Text } = Typography;
  const [endpointResponse, setEndpointResponse] = useState(null);
  const [tocList, setTocList] = useState();
  const [simulateData, setSimulateData] = useState();
  // ...
  const [uploadStatus, setUploadStatus] = useState(null);
  const [showExistingFiles, setShowExistingFiles] = useState(false);

  return (
    <div
      style={{
        position: "relative",
      }}
    >
      <Row
        style={{
          minHeight: "70vh",
        }}
        gutter={24}
      >
        <Col xs={24} md={8}>
          {!tocList ? (
            <>
              <div
                style={{
                  height: "100%",
                  padding: "24px",
                  display: "flex",
                  alignItems: "center",
                  justifyContent: "center",
                  background: "#fafafa",
                  borderRadius: "8px",
                }}
              >
                <div
                  style={{
                    display: "flex",
                    flexDirection: "column",
                    alignItems: "center",
                    justifyContent: "center",
                  }}
                >
                  <InboxOutlined
                    style={{
                      fontSize: "64px",
                      color: "#bfbfbf",
                      marginBottom: "16px",
                    }}
                  />
                  <Text
                    style={{
                      fontSize: "16px",
                      color: "#595959",
                      textAlign: "center",
                      marginBottom: "8px",
                    }}
                  >
                    Table of content will appear here
                  </Text>
                  <Text
                    type="secondary"
                    style={{
                      fontSize: "14px",
                      textAlign: "center",
                      maxWidth: "300px",
                    }}
                  >
                    After you upload documents table of content will appear
                    here.
                  </Text>
                </div>
              </div>
            </>
          ) : (
            <>
              <TocComponent
                tocData={tocList}
                setEndpointResponse={setEndpointResponse}
              />
            </>
          )}
        </Col>
        <Col xs={24} md={16}>
          <div
            style={{
              height: "100%",
              background: "#fcfcfc",
              borderRadius: "8px",
            }}
          >
            {endpointResponse ? (
              <>
                <Card style={{ width: "100%" }}>
                  <ScrollableList
                    listData={endpointResponse}
                    setUploadStatus={setUploadStatus}
                    setShowExistingFiles={setShowExistingFiles}
                    // simulateData={simulateData}
                  />
                </Card>
              </>
            ) : (
              <>
                <MultiFileUpload
                  endpointResponse={endpointResponse}
                  setEndpointResponse={setEndpointResponse}
                  setTocList={setTocList}
                  simulateData={simulateData}
                  uploadStatus={uploadStatus}
                  setUploadStatus={setUploadStatus}
                  showExistingFiles={showExistingFiles}
                  setShowExistingFiles={setShowExistingFiles}
                />
              </>
            )}
          </div>
        </Col>
      </Row>

      <FixedSearchbar
        endpointResponse={endpointResponse}
        setEndpointResponse={setEndpointResponse}
        simulateData={simulateData}
        setSimulateData={setSimulateData}
      />
    </div>
  );
};

export default KnowledgebaseComponent;
