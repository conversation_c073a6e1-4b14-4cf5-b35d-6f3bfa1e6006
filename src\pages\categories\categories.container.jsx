import React, { useEffect, useState } from "react";
import CategoriesComponent from "./categories.component";
import { <PERSON><PERSON>, Popconfirm } from "antd";
import { DeleteOutlined, EditOutlined } from "@ant-design/icons";
import { useAppDispatch } from "../../hooks/reduxHooks";
import {
  deleteCategoryByIdApi,
  getCategoriesListApi,
} from "../../services/categories.service";

const CategoriesContainer = () => {
  const dispatch = useAppDispatch();
  // ...
  const [categoriesList, setCategoriesList] = useState();

  // Table
  const columns = [
    {
      title: "Name",
      dataIndex: "name",
      key: "name",
      sorter: (a, b) => a.name.localeCompare(b.name),
    },
    {
      title: "Requirements",
      dataIndex: "requirements",
      key: "requirements",
      render: (requirements) => (
        // <ul>
        //   {Object.entries(requirements).map(([key, value]) => (
        //     <li key={key}>
        //       {key}: {value ? "Yes" : "No"}
        //     </li>
        //   ))}
        // </ul>
        <>
          {Object.entries(requirements).map(([key, value]) => (
            <p key={key}>{key}</p>
          ))}
        </>
      ),
    },
    {
      title: "Personality",
      dataIndex: "personality",
      key: "personality",
    },
    {
      title: "Language",
      dataIndex: "language",
      key: "language",
    },
    {
      title: "Properties",
      dataIndex: "properties",
      key: "properties",
      render: (properties) => <>{properties?.description ?? "N/A"}</>,
    },
    {
      title: "Reply From",
      dataIndex: "reply_from",
      key: "reply_from",
    },
    {
      title: "Actions",
      key: "actions",
      width: 100,
      render: (text, record) => (
        <>
          <Button
            type="link"
            icon={<EditOutlined />}
            onClick={() => handleEdit(record)}
          />

          <Popconfirm
            title="Are you sure to delete this record?"
            onConfirm={() => handleDelete(record?._id)}
            okText="Yes"
            cancelText="No"
          >
            <Button type="link" icon={<DeleteOutlined />} danger />
          </Popconfirm>
        </>
      ),
    },
  ];

  // Fetch Categories
  const fetchCategoriesList = () => {
    // setLoading(true);
    dispatch(
      getCategoriesListApi({
        finalCallback: () => {
          // setLoading(false);
        },
        successCallback: (response) => {
          if (response) {
            setCategoriesList(response);
          }
        },
        failureCallback: (errors) => {},
      })
    );
  };

  const deleteCategoryById = (id) => {
    dispatch(
      deleteCategoryByIdApi({
        id,
        finalCallback: () => {},
        successCallback: (response) => {
          if (response) {
            console.log("RESPONSE DELETE_CATEGORY_BY_ID -> ", response);
          }
        },
        failureCallback: (errors) => {},
      })
    );
  };

  const handleEdit = (item) => {};
  const handleDelete = (id) => {
    deleteCategoryById(id);
  };

  useEffect(() => {
    fetchCategoriesList();
  }, []);

  return <CategoriesComponent data={categoriesList} columns={columns} />;
};

export default CategoriesContainer;
