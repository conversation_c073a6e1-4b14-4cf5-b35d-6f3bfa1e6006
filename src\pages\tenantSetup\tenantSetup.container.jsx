import { useState, useEffect } from "react";
import { useNavigate } from "react-router-dom";
import { message } from "antd";
import TenantSetupComponent from "./tenantSetup.component";

const TenantSetupContainer = () => {
  const navigate = useNavigate();

  // State management for three-stage setup
  const [currentStage, setCurrentStage] = useState(1);
  const [setupData, setSetupData] = useState({
    stage1: {},
    stage2: {},
    stage3: {}
  });
  const [loading, setLoading] = useState(false);
  const [aiResponses, setAiResponses] = useState([]);

  // Static data for form options
  const staticData = {
    businessTypes: [
      { value: "ecommerce", label: "E-commerce" },
      { value: "saas", label: "SaaS/Technology" },
      { value: "service", label: "Service Business" },
      { value: "retail", label: "Retail" },
      { value: "healthcare", label: "Healthcare" },
      { value: "education", label: "Education" },
      { value: "finance", label: "Finance" },
      { value: "consulting", label: "Consulting" },
      { value: "other", label: "Other" }
    ],
    businessGoals: [
      { value: "improve_support", label: "Improve Customer Support" },
      { value: "reduce_costs", label: "Reduce Operational Costs" },
      { value: "scale_business", label: "Scale Business Operations" },
      { value: "enhance_experience", label: "Enhance Customer Experience" },
      { value: "automate_processes", label: "Automate Business Processes" },
      { value: "increase_efficiency", label: "Increase Team Efficiency" }
    ],
    languages: [
      { value: "english", label: "English" },
      { value: "nepali", label: "Nepali" },
      { value: "auto", label: "Auto-Detect" }
    ],
    callToActions: [
      { value: "contact_support", label: "Contact Support" },
      { value: "schedule_demo", label: "Schedule Demo" },
      { value: "get_quote", label: "Get Quote" },
      { value: "learn_more", label: "Learn More" },
      { value: "start_trial", label: "Start Free Trial" },
      { value: "book_consultation", label: "Book Consultation" }
    ]
  };

  // Check if tenant setup is already completed
  useEffect(() => {
    const setupCompleted = localStorage.getItem("tenantSetupCompleted");
    if (setupCompleted === "true") {
      navigate("/dashboard");
    }
  }, [navigate]);

  // Handle stage 1 form submission
  const handleStage1Submit = (formData) => {
    setSetupData(prev => ({
      ...prev,
      stage1: formData
    }));

    // Generate AI responses based on the user's question
    generateAIResponses(formData.userQuestion);
    setCurrentStage(2);
  };

  // Generate AI responses based on user's question (static demo)
  const generateAIResponses = (question) => {
    const responses = [
      {
        id: 1,
        type: "Simplified",
        title: "Simple & Direct",
        description: "Clear, concise answers that get straight to the point",
        response: `Here's a simple answer to "${question}": This is a straightforward solution that addresses your main concern directly. We recommend taking the most direct approach to solve this efficiently.`,
        features: ["Easy to understand", "Quick to read", "Action-focused"],
        icon: "🎯"
      },
      {
        id: 2,
        type: "Detailed",
        title: "Comprehensive & Thorough",
        description: "In-depth explanations with context and background information",
        response: `Regarding "${question}": Let me provide you with a comprehensive explanation. This involves multiple factors that need to be considered. First, we need to understand the context and background. Then, we can explore various approaches and their implications. The recommended solution takes into account best practices and potential challenges you might face.`,
        features: ["Complete information", "Context provided", "Multiple perspectives"],
        icon: "📋"
      },
      {
        id: 3,
        type: "Elaborated",
        title: "Expert & Technical",
        description: "Technical details with examples and step-by-step guidance",
        response: `For your question "${question}": Here's an expert-level analysis with technical insights. This solution involves several technical components and implementation steps. Step 1: Initial assessment and planning. Step 2: Technical configuration and setup. Step 3: Testing and validation. Step 4: Deployment and monitoring. Each step includes specific technical requirements and best practices to ensure optimal results.`,
        features: ["Technical depth", "Step-by-step guide", "Expert insights"],
        icon: "🔧"
      }
    ];

    setAiResponses(responses);
  };

  // Handle stage 2 response selection
  const handleStage2Submit = (selectedResponse) => {
    setSetupData(prev => ({
      ...prev,
      stage2: { selectedResponse }
    }));
    setCurrentStage(3);
  };

  // Handle final setup completion
  const handleStage3Submit = (confirmationData) => {
    const finalSetupData = {
      ...setupData,
      stage3: confirmationData,
      completedAt: new Date().toISOString()
    };

    setLoading(true);
    
    // Simulate setup completion
    setTimeout(() => {
      console.log("Complete setup data:", finalSetupData);
      message.success("🎉 Tenant setup completed successfully!");
      localStorage.setItem("tenantSetupCompleted", "true");
      localStorage.setItem("tenantSetupData", JSON.stringify(finalSetupData));
      setLoading(false);
      navigate("/dashboard");
    }, 2000);
  };

  // Handle navigation between stages
  const handleStageNavigation = (direction) => {
    if (direction === "next" && currentStage < 3) {
      setCurrentStage(currentStage + 1);
    } else if (direction === "back" && currentStage > 1) {
      setCurrentStage(currentStage - 1);
    }
  };

  return (
    <TenantSetupComponent
      currentStage={currentStage}
      setupData={setupData}
      staticData={staticData}
      aiResponses={aiResponses}
      loading={loading}
      onStage1Submit={handleStage1Submit}
      onStage2Submit={handleStage2Submit}
      onStage3Submit={handleStage3Submit}
      onStageNavigation={handleStageNavigation}
    />
  );
};

export default TenantSetupContainer;
