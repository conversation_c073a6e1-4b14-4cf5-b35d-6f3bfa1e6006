import { useState, useEffect } from "react";
import { useNavigate } from "react-router-dom";
import { message } from "antd";
import TenantSetupComponent from "./tenantSetup.component";

const TenantSetupContainer = () => {
  const navigate = useNavigate();

  // State management for three-stage setup
  const [currentStage, setCurrentStage] = useState(1);
  const [setupData, setSetupData] = useState({
    stage1: {},
    stage2: {},
    stage3: {},
  });
  const [loading, setLoading] = useState(false);

  // Static data for form options
  const staticData = {
    businessTypes: [
      { value: "ecommerce", label: "E-commerce" },
      { value: "saas", label: "SaaS/Technology" },
      { value: "service", label: "Service Business" },
      { value: "retail", label: "Retail" },
      { value: "healthcare", label: "Healthcare" },
      { value: "education", label: "Education" },
      { value: "finance", label: "Finance" },
      { value: "consulting", label: "Consulting" },
      { value: "other", label: "Other" },
    ],
    businessGoals: [
      { value: "improve_support", label: "Improve Customer Support" },
      { value: "reduce_costs", label: "Reduce Operational Costs" },
      { value: "scale_business", label: "Scale Business Operations" },
      { value: "enhance_experience", label: "Enhance Customer Experience" },
      { value: "automate_processes", label: "Automate Business Processes" },
      { value: "increase_efficiency", label: "Increase Team Efficiency" },
    ],
    languages: [
      { value: "english", label: "English" },
      { value: "nepali", label: "Nepali" },
      { value: "auto", label: "Auto-Detect" },
    ],
    callToActions: [
      { value: "contact_support", label: "Contact Support" },
      { value: "schedule_demo", label: "Schedule Demo" },
      { value: "get_quote", label: "Get Quote" },
      { value: "learn_more", label: "Learn More" },
      { value: "start_trial", label: "Start Free Trial" },
      { value: "book_consultation", label: "Book Consultation" },
    ],
  };

  // Check if tenant setup is already completed
  useEffect(() => {
    const setupCompleted = localStorage.getItem("tenantSetupCompleted");
    if (setupCompleted === "true") {
      navigate("/dashboard");
    }
  }, [navigate]);

  // Handle stage 1 form submission
  const handleStage1Submit = (formData) => {
    setSetupData((prev) => ({
      ...prev,
      stage1: formData,
    }));
    setCurrentStage(2);
  };

  // Handle stage 2 AI goal and response selection
  const handleStage2Submit = (stage2Data) => {
    setSetupData((prev) => ({
      ...prev,
      stage2: stage2Data,
    }));
    setCurrentStage(3);
  };

  // Handle final setup completion
  const handleStage3Submit = (confirmationData) => {
    const finalSetupData = {
      ...setupData,
      stage3: confirmationData,
      completedAt: new Date().toISOString(),
    };

    setLoading(true);

    // Simulate setup completion
    setTimeout(() => {
      console.log("Complete setup data:", finalSetupData);
      message.success("🎉 Tenant setup completed successfully!");
      localStorage.setItem("tenantSetupCompleted", "true");
      localStorage.setItem("tenantSetupData", JSON.stringify(finalSetupData));
      setLoading(false);
      navigate("/dashboard");
    }, 2000);
  };

  // Handle navigation between stages
  const handleStageNavigation = (direction) => {
    if (direction === "next" && currentStage < 3) {
      setCurrentStage(currentStage + 1);
    } else if (direction === "back" && currentStage > 1) {
      setCurrentStage(currentStage - 1);
    }
  };

  return (
    <TenantSetupComponent
      currentStage={currentStage}
      setupData={setupData}
      staticData={staticData}
      loading={loading}
      onStage1Submit={handleStage1Submit}
      onStage2Submit={handleStage2Submit}
      onStage3Submit={handleStage3Submit}
      onStageNavigation={handleStageNavigation}
    />
  );
};

export default TenantSetupContainer;
