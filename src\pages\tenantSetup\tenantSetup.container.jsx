import { useState, useEffect } from "react";
import { useNavigate } from "react-router-dom";
import { message } from "antd";
import { useSelector } from "react-redux";
import TenantSetupComponent from "./tenantSetup.component";
import { useAppDispatch } from "../../hooks/reduxHooks";
import {
  submitTenantSetupApi,
  checkTenantSetupStatusApi,
} from "../../services/tenantSetup.service";
import {
  setCurrentStage,
  updateStage1Data,
  updateStage2Data,
  setSetupCompleted,
} from "../../store/slices/tenantSetup.slice";

const TenantSetupContainer = () => {
  const navigate = useNavigate();
  const dispatch = useAppDispatch();

  // Redux state
  const { currentStage, setupData, isSetupCompleted, loading } = useSelector(
    (state) => state.tenantSetup
  );

  // Local state for response options
  const [responseOptions, setResponseOptions] = useState([]);

  // Check if tenant setup is already completed
  useEffect(() => {
    const setupCompleted = localStorage.getItem("tenantSetupCompleted");
    if (setupCompleted === "true" || isSetupCompleted) {
      navigate("/dashboard");
    } else {
      // Check setup status from server
      dispatch(
        checkTenantSetupStatusApi({
          successCallback: (response) => {
            if (response.isCompleted) {
              localStorage.setItem("tenantSetupCompleted", "true");
              dispatch(setSetupCompleted(true));
              navigate("/dashboard");
            }
          },
          failureCallback: (error) => {
            console.error("Failed to check setup status:", error);
          },
        })
      );
    }
  }, [navigate, dispatch, isSetupCompleted]);

  // Handle stage 1 form submission
  const handleStage1Submit = (formData) => {
    dispatch(updateStage1Data(formData));

    // Generate response options based on form data
    generateResponseOptions(formData);
    dispatch(setCurrentStage(2));
  };

  // Generate response options based on stage 1 data
  const generateResponseOptions = (formData) => {
    const { businessType, teamSize } = formData;

    // Mock response options - in real app, this would come from API
    const options = [
      {
        id: 1,
        title: "AI-Powered Customer Support",
        description:
          "Automated responses with intelligent routing and escalation",
        features: [
          "24/7 automated responses",
          "Smart ticket routing",
          "Sentiment analysis",
          "Multi-language support",
        ],
        recommended: businessType === "ecommerce" || teamSize > 50,
        icon: "🤖",
      },
      {
        id: 2,
        title: "Human-First Approach",
        description: "Personal touch with AI assistance for efficiency",
        features: [
          "Human agent priority",
          "AI-suggested responses",
          "Customer history tracking",
          "Performance analytics",
        ],
        recommended: businessType === "service" || teamSize <= 20,
        icon: "👥",
      },
      {
        id: 3,
        title: "Hybrid Solution",
        description: "Best of both worlds with intelligent handoffs",
        features: [
          "AI handles simple queries",
          "Seamless human handoff",
          "Advanced analytics",
          "Custom workflows",
        ],
        recommended: teamSize > 20 && teamSize <= 50,
        icon: "⚡",
      },
    ];

    setResponseOptions(options);
  };

  // Handle stage 2 option selection
  const handleStage2Submit = (selectedOption) => {
    const stage2Data = {
      selectedOption,
      timestamp: new Date().toISOString(),
    };

    dispatch(updateStage2Data(stage2Data));

    const finalSetupData = {
      ...setupData,
      stage2: stage2Data,
    };

    submitTenantSetup(finalSetupData);
  };

  // Submit final setup data
  const submitTenantSetup = (finalData) => {
    dispatch(
      submitTenantSetupApi({
        data: finalData,
        finalCallback: () => {},
        successCallback: () => {
          message.success("Tenant setup completed successfully!");
          localStorage.setItem("tenantSetupCompleted", "true");
          dispatch(setSetupCompleted(true));
          navigate("/dashboard");
        },
        failureCallback: (error) => {
          message.error("Setup failed. Please try again.");
          console.error("Setup error:", error);
        },
      })
    );
  };

  // Handle going back to previous stage
  const handleGoBack = () => {
    if (currentStage > 1) {
      dispatch(setCurrentStage(currentStage - 1));
    }
  };

  return (
    <TenantSetupComponent
      currentStage={currentStage}
      setupData={setupData}
      responseOptions={responseOptions}
      loading={loading.submit}
      onStage1Submit={handleStage1Submit}
      onStage2Submit={handleStage2Submit}
      onGoBack={handleGoBack}
    />
  );
};

export default TenantSetupContainer;
