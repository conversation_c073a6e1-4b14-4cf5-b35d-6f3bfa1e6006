import React, { useState, useEffect } from "react";
import ChatPanel from "./section/chatPanel/chatPanel.component";
import SourcesPanel from "./section/SourcesPanel/SourcesPanel.component";
import { message } from "antd";
import { fetch_docs } from "./hook/fetchdata";

const KbSetupComponent = () => {
  const [subsources, setSubSources] = useState([]);
  const [selectedAssistantIndex, setSelectedAssistantIndex] = useState(null);
  const [defaultSources, setDefaultSources] = useState([]);
  const [sourceFilter, setSourceFilter] = useState(null);
  const [filtersTOC, setFiltersTOC] = useState(null);
  const [offsetValue, setOffsetValue] = useState(null);
  const [sourcesLoading, setSourcesLoading] = useState(false);
  const [highlight, setHighlight] = useState(false);
  useEffect(() => {
    if (!subsources.sentence || subsources.sentence.length === 0) {
      setHighlight(false);
    }
  }, [subsources.sentence]);

  useEffect(() => {
    fetchDefaultSources();
  }, []);

  useEffect(() => {
    if (sourceFilter) {
      fetchDefaultSources(offsetValue, sourceFilter);
    }
  }, [sourceFilter, offsetValue]);

  const fetchDefaultSources = async (offsetValue, sourceFilter) => {
    setSourcesLoading(true);
    try {
      const response = await fetch_docs(offsetValue, sourceFilter);

      const newSources = response?.response ?? [];
      setDefaultSources(newSources);
      setSubSources(newSources);
      setOffsetValue(response?.next_offset);
    } catch (error) {
      console.error("Error fetching default sources:", error);
      message.error("Failed to load default sources");
    } finally {
      setSourcesLoading(false);
    }
  };

  const handleAssistantMessageClick = (index, sources) => {
    if (selectedAssistantIndex === index) {
      setSelectedAssistantIndex(null);
      setSubSources(defaultSources);
      setHighlight((prev) => !prev);
    } else {
      setSelectedAssistantIndex(index);
      setSubSources(sources);
      setHighlight(true);
    }
  };

  const handleClearChat = () => {
    setSelectedAssistantIndex(null);
    setSubSources(defaultSources);
    message.success("Facts Cleared");
  };

  return (
    <div
      style={{
        display: "flex",
        gap: "20px",
        height: "85vh",
        overflow: "auto",
      }}
    >
      <div
        style={{
          flex: "0 0 35%",
          display: "flex",
          flexDirection: "column",
          maxHeight: "100vh",
        }}
      >
        <ChatPanel
          onClearChat={handleClearChat}
          onAssistantMessageClick={handleAssistantMessageClick}
          selectedAssistantIndex={selectedAssistantIndex}
          setHighlight={setHighlight}
        />
      </div>
      <div style={{ flex: 1, display: "flex", flexDirection: "column" }}>
        <SourcesPanel
          setHighlight={setHighlight}
          highlight={highlight}
          sourcesLoading={sourcesLoading}
          fetchDefaultSources={fetchDefaultSources}
          sources={subsources}
          onBackToDefault={() => {
            setSubSources(defaultSources);
            setSelectedAssistantIndex(null);
          }}
          isSubsourcesView={selectedAssistantIndex !== null}
          setSourceFilter={setSourceFilter}
          defaultSources={defaultSources}
          setDefaultSources={setDefaultSources}
          offsetValue={offsetValue}
          sourceFilter={sourceFilter}
          setSubSources={setSubSources}
          setOffsetValue={setOffsetValue}
          filtersTOC={filtersTOC}
          setFiltersTOC={setFiltersTOC}
        />
      </div>
    </div>
  );
};

export default KbSetupComponent;
