import React, { useEffect, useState } from "react";
import { Modal, Card, message } from "antd";
import EditableParagraph from "../editableParagraph";
import { useAppDispatch } from "../../hooks/reduxHooks";
import { messageVerificationApi } from "../../services/channels.service";

const ChatBubbleModal = ({
  isChatBubbleModalOpen,
  setIsChatBubbleModalOpen,
  message,
}) => {
  return (
    <>
      <Modal
        open={isChatBubbleModalOpen}
        onCancel={() => setIsChatBubbleModalOpen(false)}
        centered
        width={650}
        footer={null}
      >
        {message?.media_ids?.map((item) => {
          return (
            <div
              style={{
                display: "flex",
                justifyContent: "center",
                alignItems: "center",
                marginTop: "32px",
                maxHeight: "580px",
                overflowY: "auto",
              }}
            >
              <img
                src={item}
                alt="No Img Found!"
                width={570}
                style={{
                  objectFit: "contain",
                }}
              />
            </div>
          );
        })}
        <div
          style={{
            maxHeight: "520px",
            overflowY: "auto",
            wordBreak: "break-word",
            whiteSpace: "pre-wrap",
            paddingRight: "18px",
            marginTop: "32px",
            textAlign: "center",
          }}
        >
          {message?.content}
        </div>
      </Modal>
    </>
  );
};
export default ChatBubbleModal;
