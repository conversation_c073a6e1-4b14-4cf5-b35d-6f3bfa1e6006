/* src/components/CustomerList/CustomerList.css */

.customer-list-header {
  margin-bottom: 0px;
  text-align: center;
  padding: 0;
}

.filter-collapse .ant-collapse-header {
  background: #fff;
  padding: 0;
  font-weight: 500;
  font-size: 16px;
  margin-bottom: 0px;
}

.filter-collapse .ant-collapse-content {
  background: #fff;
}

.customer-list {
  overflow-y: auto;
  margin-right: 5px;
  height: 650px !important;
  /* min-height: calc(80vh) !important; */
  background: #fff;
  /* border-top: 1px solid #f0f0f0; */
}

/* Scrollbar track */
.customer-list::-webkit-scrollbar {
  width: 8px; /* Width of the scrollbar */
}

/* Scrollbar thumb */
.customer-list::-webkit-scrollbar-thumb {
  background: #c0c0c0; /* Color of the scrollbar thumb */
  border-radius: 5px; /* Rounded corners */
}

/* Scrollbar thumb hover */
.customer-list::-webkit-scrollbar-thumb:hover {
  background: #a9a9a9; /* Darker color on hover */
}

/* Scrollbar track */
.customer-list::-webkit-scrollbar-track {
  background: #f1f1f1; /* Background of the scrollbar track */
}

.customer-list-item {
  cursor: pointer;
  padding-top: 18px !important;
  padding-bottom: 18px !important;
  padding-left: 8px !important;
  padding-right: 8px !important;

  margin: 0 8px;
  transition: background-color 0.3s;
}

.customer-list-item:hover {
  background-color: #eceff1;
}

/* Background color of SELECTED CUSTOMER LIST ITEM */
.customer-list-item.selected {
  background-color: #e3f2fd;
}

.customer-title {
  display: flex;
  align-items: center;
}

.message-time-customer {
  font-size: 12px;
  font-weight: 500;
  color: #777777;
}

.back-to-top-button {
  position: absolute;
  bottom: 20px;
  right: 30px;
  z-index: 1000;
  font-size: 18px;
  background-color: rgb(202, 198, 198);
}

.back-to-top-button-leftSide {
  position: absolute;
  bottom: 20px;
  left: 20px;
  z-index: 1000;
  font-size: 18px;
  background-color: rgb(202, 198, 198);
}
