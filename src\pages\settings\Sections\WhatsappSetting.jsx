import { Switch, Card, Typography, message, Divider, Skeleton } from "antd";
import React, { useState, useEffect } from "react";
import { useAppDispatch } from "../../../hooks/reduxHooks";
import { getMsgFlag, changeMsgFlag } from "../../../services/customers.service";
import { FormOutlined } from "@ant-design/icons";

const { Title, Text } = Typography;

const WhatsappSetting = () => {
  const dispatch = useAppDispatch();
  const [loading, setLoading] = useState(false);
  const [createCustomer, setCreateCustomer] = useState(false);

  useEffect(() => {
    setLoading(true);
    dispatch(
      getMsgFlag({
        successCallback: (response) => {
          setCreateCustomer(response.flag);
        },
        failureCallback: (errors) => {
          message.error("Failed to fetch customer creation setting.");
        },
        finalCallback: () => {
          setLoading(false);
        },
      })
    );
  }, [dispatch]);

  const toggleCustomerCreation = () => {
    setLoading(true);

    dispatch(
      changeMsgFlag({
        params: {
          flag: !createCustomer,
        },
        successCallback: (response) => {
          setCreateCustomer(!createCustomer);
          message.success("Customer creation setting updated successfully!");
        },
        failureCallback: (errors) => {
          message.error(
            errors?.data?.message ||
              "Failed to update customer creation setting. Please try again."
          );
        },
        finalCallback: () => {
          setLoading(false);
        },
      })
    );
  };

  const StatusIndicator = () => (
    <div style={{ display: "flex", alignItems: "center", marginBottom: 12 }}>
      <div
        style={{
          width: 8,
          height: 8,
          borderRadius: "50%",
          backgroundColor: createCustomer ? "#52c41a" : "#f5222d",
          marginRight: 8,
        }}
      />
      <Text style={{ fontSize: 13, color: "#595959" }}>
        {createCustomer ? "Currently Enabled" : "Currently Disabled"}
      </Text>
    </div>
  );

  return (
    <Card
      style={{
        width: "100%",
        borderRadius: 6,
        boxShadow: "0 1px 4px rgba(0, 0, 0, 0.05)",
      }}
      bodyStyle={{
        padding: 24,
        width: "100%",
      }}
    >
      <Title level={4} style={{ textAlign: "left", marginBottom: "8px" }}>
        WhatsApp Settings
      </Title>

      <Divider style={{ margin: "8px 0 16px 0" }} />

      {/* Main Toggle Section */}
      <div style={{ marginBottom: 20 }}>
        <div
          style={{
            display: "flex",
            justifyContent: "space-between",
            alignItems: "center",
            marginBottom: 12,
          }}
        >
          <div>
            <Text strong style={{ fontSize: 15 }}>
              Customer Creation
            </Text>
            <div>
              <Text type="secondary" style={{ fontSize: 13 }}>
                Enable or disable customer creation through WhatsApp channels
              </Text>
            </div>
          </div>
          <Switch
            checked={createCustomer}
            onChange={toggleCustomerCreation}
            loading={loading}
          />
        </div>
        {createCustomer && <StatusIndicator />}
      </div>

      {/* Description Section */}
      <div
        style={{
          marginTop: 16,
          padding: 12,
          backgroundColor: "#fafafa",
          borderRadius: 4,
          border: "1px solid #f0f0f0",
          width: "100%",
        }}
      >
        <Text type="secondary" style={{ fontSize: 12 }}>
          {createCustomer
            ? "Customer creation is enabled. You can add new customers via the "
            : "Customer creation is disabled. Enable to allow adding new customers via the "}
          <b>Channels</b> page. When enabled, click the
          <FormOutlined
            style={{
              marginLeft: "6px",
              marginRight: "6px",
              color: "#777777",
              fontSize: "14px",
            }}
          />
          icon at the top right of the customers list to create a new customer.
        </Text>
      </div>
    </Card>
  );
};

export default WhatsappSetting;
